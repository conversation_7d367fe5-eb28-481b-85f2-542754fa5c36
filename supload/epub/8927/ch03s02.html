<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Setting up the hardware</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Setting up the hardware"><div class="titlepage"><div><div><h1 class="title"><a id="ch03lvl1sec20"/>Setting up the hardware</h1></div></div></div><p>About the hardware, there<a id="id98" class="indexterm"/> are at least two major issues to be pointed out:</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem" style="list-style-type: disc"><span class="strong"><strong>Power supply</strong></span>: We <a id="id99" class="indexterm"/>have two different voltages to use due to the fact the water pump, the lamp, and the cooler are 12V powered, while the other devices are 5V/3.3V powered. So, we have to use a dual output power source (or two different power sources) to power up our prototype.</li><li class="listitem" style="list-style-type: disc"><span class="strong"><strong>Interface</strong></span>: The <a id="id100" class="indexterm"/>second issue is about using a proper interface circuitry between the 12V world and the 5V one in such a way that it doesn't damage the BeagleBone Black or other devices. Let me point out that a single GPIO of the BeagleBone Black can manage a voltage of 3.3V, so we need a proper circuitry to manage a 12V device.</li></ul></div><div class="section" title="Setting up the 12V devices"><div class="titlepage"><div><div><h2 class="title"><a id="ch03lvl2sec25"/>Setting up the 12V devices</h2></div></div></div><p>As just stated, these <a id="id101" class="indexterm"/>devices need special attention and a dedicated<a id="id102" class="indexterm"/> 12V power line which, of course, cannot be the one we wish to use to supply the BeagleBone Black. On my prototype, I used a 12V power supplier that can supply a current till 1A. These characteristics should be enough to manage a single water pump, a lamp, and a cooler.</p><p>After you get a proper power supplier, we can pass to show the circuitry to use to manage the 12V devices. Since all of them are simple on/off devices, we can use a relay to control them. I used the device shown in the following image where we have 8 relays:</p><div class="mediaobject"><img src="graphics/B00255_03_01.jpg" alt="Setting up the 12V devices"/></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note26"/>Note</h3><p>The devices can be purchased at the following link (or by surfing the Internet): <a class="ulink" href="http://www.cosino.io/product/5v-relays-array">http://www.cosino.io/product/5v-relays-array</a>
</p></div></div><p>Then, the schematic to connect a single 12V device is shown in the following diagram:</p><div class="mediaobject"><img src="graphics/B00255_03_02.jpg" alt="Setting up the 12V devices"/></div><p>Simply speaking, for<a id="id103" class="indexterm"/> each device, we can turn the power supply on and off simply by moving a specific GPIO of our BeagleBone Black. Note that each relays of the array board can be managed in direct or inverse logic by simply choosing the right connections accordingly as reported on the board itself, that is, we can decide that, by putting the GPIO into a logic <code class="literal">0</code> state, we can activate the relay, and then, turning on the attached device, while putting the GPIO into a logic <code class="literal">1</code> state, we can deactivate the relay, and then turn off the attached device.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip26"/>Tip</h3><p>By using the following logic, when the LED of a relay is turned on, the corresponding device is powered on.</p></div></div><p>The BeagleBone Black's GPIOs and the pins of the relays array I used with 12V devices are reported in the following table:</p><div class="informaltable"><table border="1"><colgroup><col style="text-align: left"/><col style="text-align: left"/><col style="text-align: left"/></colgroup><thead><tr><th style="text-align: left" valign="bottom">
<p>Pin</p>
</th><th style="text-align: left" valign="bottom">
<p>Relays Array pin</p>
</th><th style="text-align: left" valign="bottom">
<p>12V Device</p>
</th></tr></thead><tbody><tr><td style="text-align: left" valign="top">
<p>P8.10 - GPIO66</p>
</td><td style="text-align: left" valign="top">
<p>3</p>
</td><td style="text-align: left" valign="top">
<p>Lamp</p>
</td></tr><tr><td style="text-align: left" valign="top">
<p>P8.9 - GPIO69</p>
</td><td style="text-align: left" valign="top">
<p>2</p>
</td><td style="text-align: left" valign="top">
<p>Cooler</p>
</td></tr><tr><td style="text-align: left" valign="top">
<p>P8.12 - GPIO68</p>
</td><td style="text-align: left" valign="top">
<p>1</p>
</td><td style="text-align: left" valign="top">
<p>Pump</p>
</td></tr><tr><td style="text-align: left" valign="top">
<p>P9.1 - GND</p>
</td><td style="text-align: left" valign="top">
<p>GND</p>
</td><td style="text-align: left" valign="top">&#xA0;</td></tr><tr><td style="text-align: left" valign="top">
<p>P9.5 - 5V</p>
</td><td style="text-align: left" valign="top">
<p>Vcc</p>
</td><td style="text-align: left" valign="top">&#xA0;</td></tr></tbody></table></div><p>To test the functionality of each GPIO line, we can use the following command to set up the GPIO as an output line at high state:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@arm:~# ./bin/gpio_set.sh 68 out 1</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip27"/>Tip</h3><p>Note that the <span class="emphasis"><em>off</em></span> state of the relay is <code class="literal">1</code>, while the <span class="emphasis"><em>on</em></span> state is <code class="literal">0</code>.</p></div></div><p>Then, we can<a id="id104" class="indexterm"/> turn the relay on and off by just writing <code class="literal">0</code> and <code class="literal">1</code> into <code class="literal">/sys/class/gpio/gpio68/value</code> file, as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@arm:~# echo 0 &gt; /sys/class/gpio/gpio68/value</strong></span>
<span class="strong"><strong>root@arm:~# echo 1 &gt; /sys/class/gpio/gpio68/value</strong></span>
</pre></div></div><div class="section" title="Setting up the webcam"><div class="titlepage"><div><div><h2 class="title"><a id="ch03lvl2sec26"/>Setting up the webcam</h2></div></div></div><p>The webcam I'm<a id="id105" class="indexterm"/> using in my prototype is a normal UVC-based webcam, but you can safely use another one that is supported by the<a id="id106" class="indexterm"/> <span class="strong"><strong>mjpg-streamer</strong></span> tool.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note27"/>Note</h3><p>See the <a id="id107" class="indexterm"/>mjpg-streamer project's home site for further information at <a class="ulink" href="http://sourceforge.net/projects/mjpg-streamer/">http://sourceforge.net/projects/mjpg-streamer/</a>.</p></div></div><p>Once connected <a id="id108" class="indexterm"/>to the BeagleBone Black USB host port, I get the following kernel activities:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>usb 1-1: New USB device found, idVendor=045e, idProduct=0766</strong></span>
<span class="strong"><strong>usb 1-1: New USB device strings: Mfr=1, Product=2, SerialNumber=0</strong></span>
<span class="strong"><strong>usb 1-1: Product: Microsoft LifeCam VX-800</strong></span>
<span class="strong"><strong>usb 1-1: Manufacturer: Microsoft</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>uvcvideo 1-1:1.0: usb_probe_interface</strong></span>
<span class="strong"><strong>uvcvideo 1-1:1.0: usb_probe_interface - got id</strong></span>
<span class="strong"><strong>uvcvideo: Found UVC 1.00 device Microsoft LifeCam VX-800 (045e:0766)</strong></span>
</pre></div><p>Now, a new driver called <code class="literal">uvcvideo</code> is loaded into the kernel:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# lsmod</strong></span>
<span class="strong"><strong>Module                  Size  Used by</strong></span>
<span class="strong"><strong>snd_usb_audio          95766  0</strong></span>
<span class="strong"><strong>snd_hwdep               4818  1 snd_usb_audio</strong></span>
<span class="strong"><strong>snd_usbmidi_lib        14457  1 snd_usb_audio</strong></span>
<span class="strong"><strong>uvcvideo               53354  0</strong></span>
<span class="strong"><strong>videobuf2_vmalloc       2418  1 uvcvideo</strong></span>
<span class="strong"><strong>...</strong></span>
</pre></div><p>Okay, now, to have a streaming server, we need to download the mjpg-streamer source code and compile it. We can do everything within the BeagleBone Black itself with the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# svn checkout svn://svn.code.sf.net/p/mjpg-streamer/code/ mjpg-streamer-code</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip28"/>Tip</h3><p>The <code class="literal">svn</code> command is part of the <code class="literal">subversion</code> package and can be installed by using the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# aptitude install subversion</strong></span>
</pre></div></div></div><p>After the download is finished, we can compile and install the code by using the following command line:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# cd mjpg-streamer-code/mjpg-streamer/ &amp;&amp; make &amp;&amp; make install</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note28"/>Note</h3><p>You can find a compressed archive copy of this program in the <code class="literal">chapter_03/mjpg-streamer-code.tgz</code> file in the book's example code repository.</p></div></div><p>If no errors <a id="id109" class="indexterm"/>are reported, you should now be able to execute the new command as follows, where we ask for the help message:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# mjpg_streamer --help</strong></span>
<span class="strong"><strong>-----------------------------------------------------------------------</strong></span>
<span class="strong"><strong>Usage: mjpg_streamer</strong></span>
<span class="strong"><strong>  -i | --input "&lt;input-plugin.so&gt; [parameters]"</strong></span>
<span class="strong"><strong>  -o | --output "&lt;output-plugin.so&gt; [parameters]"</strong></span>
<span class="strong"><strong> [-h | --help ]........: display this help</strong></span>
<span class="strong"><strong> [-v | --version ].....: display version information</strong></span>
<span class="strong"><strong> [-b | --background]...: fork to the background, daemon mode</strong></span>
<span class="strong"><strong>...</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip29"/>Tip</h3><p>If you get an error like the following it means that your system misses the <code class="literal">convert</code> tool:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>make[1]: Entering directory `/root/mjpg-streamer-code/mjpg-streamer/plugins/input_testpicture'</strong></span>
<span class="strong"><strong>convert pictures/960x720_1.jpg -resize 640x480!</strong></span>
<span class="strong"><strong>pictures/640x480_1.jpg</strong></span>
<span class="strong"><strong>/bin/sh: 1: convert: not found</strong></span>
<span class="strong"><strong>make[1]: *** [pictures/640x480_1.jpg] Error 127</strong></span>
</pre></div><p>You can install it by using the usual <code class="literal">aptitude</code> command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# aptitude install imagemagick</strong></span>
</pre></div></div></div><p>Okay, now we are ready to test the webcam. Just run the following command line and then point a web browser to the address <code class="literal">http://*************:8080/?action=stream</code> (where you should replace my IP address <code class="literal">*************</code> with your BeagleBone Black's one) in order to get the live video from your webcam:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# LD_LIBRARY_PATH=/usr/local/lib/ mjpg_streamer -i "input_uvc.so -y -f 10 -r QVGA" -o "output_http.so -w /var/www/"</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip31"/>Tip</h3><p>Note that you can use the USB Ethernet address <code class="literal">***********</code> too if you're not using the BeagleBone Black's Ethernet port.</p></div></div><p>If everything works<a id="id110" class="indexterm"/> well, you should get something similar to what is shown in the following screenshot:</p><div class="mediaobject"><img src="graphics/B00255_03_03.jpg" alt="Setting up the webcam"/></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip32"/>Tip</h3><p>If you get an error, as follows it means that some other process is holding the <code class="literal">8080</code> port, and most probably it's occupied by the <code class="literal">Bone101</code> service:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>bind: Address already in use</strong></span>
</pre></div><p>To disable it, you can use the following commands:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@BeagleBone:~# systemctl stop bonescript.socket</strong></span>
<span class="strong"><strong>root@BeagleBone:~# systemctl disable bonescript.socket</strong></span>
<span class="strong"><strong>rm '/etc/systemd/system/sockets.target.wants/bonescript.socket'</strong></span>
</pre></div><p>Or, you <a id="id111" class="indexterm"/>can simply use another port, maybe port <code class="literal">8090</code>, with the following command line:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# LD_LIBRARY_PATH=/usr/local/lib/ mjpg_streamer -i "input_uvc.so -y -f 10 -r QVGA" -o "output_http.so -p 8090 -w /var/www/"</strong></span>
</pre></div></div></div></div><div class="section" title="Connecting the temperature sensor"><div class="titlepage"><div><div><h2 class="title"><a id="ch03lvl2sec27"/>Connecting the temperature sensor</h2></div></div></div><p>The temperature sensor <a id="id112" class="indexterm"/>used in my prototype is the one shown in the following image:</p><div class="mediaobject"><img src="graphics/B00255_03_04.jpg" alt="Connecting the temperature sensor"/></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note29"/>Note</h3><p>The devices can be purchased at the following link (or by surfing the Internet): <a class="ulink" href="http://www.cosino.io/product/waterproof-temperature-sensor">http://www.cosino.io/product/waterproof-temperature-sensor</a>.</p><p>The datasheet of this device is available at <a class="ulink" href="http://datasheets.maximintegrated.com/en/ds/DS18B20.pdf">http://datasheets.maximintegrated.com/en/ds/DS18B20.pdf</a>.</p></div></div><p>As you can see, it's a<a id="id113" class="indexterm"/> waterproof device, so we can safely put it into the water to get its temperature.</p><p>This device is a <span class="strong"><strong>1-Wire</strong></span> device and we can get access to it by using the <code class="literal">w1-gpio</code> driver, which emulates a 1-Wire controller by using a standard BeagleBone Black GPIO pin. The electrical connection must be done according to the following table, keeping in mind that the sensor has three colored connection cables:</p><div class="informaltable"><table border="1"><colgroup><col style="text-align: left"/><col style="text-align: left"/></colgroup><thead><tr><th style="text-align: left" valign="bottom">
<p>Pin</p>
</th><th style="text-align: left" valign="bottom">
<p>Cable color</p>
</th></tr></thead><tbody><tr><td style="text-align: left" valign="top">
<p>P9.4 - Vcc</p>
</td><td style="text-align: left" valign="top">
<p>Red</p>
</td></tr><tr><td style="text-align: left" valign="top">
<p>P8.11 - GPIO1_13</p>
</td><td style="text-align: left" valign="top">
<p>White</p>
</td></tr><tr><td style="text-align: left" valign="top">
<p>P9.2 - GND</p>
</td><td style="text-align: left" valign="top">
<p>Black</p>
</td></tr></tbody></table></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note30"/>Note</h3><p>Interested readers can follow this URL for more information about how 1-Wire works: <a class="ulink" href="http://en.wikipedia.org/wiki/1-Wire">http://en.wikipedia.org/wiki/1-Wire</a>
</p></div></div><p>Keep in mind that, since our 1-Wire controller is implemented in software, we have to add a pull-up resistor of 4.7K&#x2126; between the red and white cable in order to make it work!</p><p>Once all connections are in place, we can use the <code class="literal">chapter_03/BB-W1-GPIO-00A0.dts</code> file in the book's example code repository to enable the 1-Wire controller on the <span class="emphasis"><em>P8.11</em></span> pin of the BeagleBone Black's expansion connector. The following snippet shows the relevant code where we enable the <code class="literal">w1-gpio</code> driver and assign to it the proper GPIO:</p><div class="informalexample"><pre class="programlisting">   fragment@1 {
      target = &lt;&amp;ocp&gt;;

      __overlay__ {
         #address-cells  = &lt;1&gt;;
         #size-cell      = &lt;0&gt;;
         status          = "okay";

         /* Setup the pins */
         pinctrl-names   = "default";
         pinctrl-0       = &lt;&amp;bb_w1_pins&gt;;

         /* Define the new one-wire master as based on w1-gpio
         * and using GPIO1_13
         */
         onewire@0 {
            compatible      = "w1-gpio";
            gpios           = &lt;&amp;gpio2 13 0&gt;;
         };
      };
   };</pre></div><p>To enable it, we <a id="id114" class="indexterm"/>must use the <code class="literal">dtc</code> program to compile it as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# dtc -O dtb -o /lib/firmware/BB-W1-GPIO-00A0.dtbo -b 0 -@ BB-W1-GPIO-00A0.dts</strong></span>
</pre></div><p>Then, we have to load it into the kernel with the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# echo BB-W1-GPIO &gt; /sys/devices/bone_capemgr.9/slots</strong></span>
</pre></div><p>If everything works well, we should see a new 1-Wire device under the <code class="literal">/sys/bus/w1/devices/</code> directory, as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# ls /sys/bus/w1/devices/</strong></span>
<span class="strong"><strong>28-000004b541e9  w1_bus_master1</strong></span>
</pre></div><p>The new temperature sensor is represented by the directory named <code class="literal">28-000004b541e9</code>. To read the current temperature, we can use the <code class="literal">cat</code> command on the <code class="literal">w1_slave</code> file as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# cat /sys/bus/w1/devices/28-000004b541e9/w1_slave</strong></span>
<span class="strong"><strong>d8 01 00 04 1f ff 08 10 1c : crc=1c YES</strong></span>
<span class="strong"><strong>d8 01 00 04 1f ff 08 10 1c t=29500</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip35"/>Tip</h3><p>Note that your sensors have a different ID, so in your system you'll get a different path name in the <code class="literal">/sys/bus/w1/devices/28-NNNNNNNNNNNN/w1_slave </code>form.</p></div></div><p>In the preceding example, the current temperature is <code class="literal">t=29500</code>, which is expressed in <span class="strong"><strong>millicelsius degree</strong></span> (<span class="strong"><strong>m&#xB0;C</strong></span>), so it's equivalent to 29.5&#xB0;C.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip36"/>Tip</h3><p>The reader can take a look at the book <span class="emphasis"><em>BeagleBone Essentials</em></span>, <span class="emphasis"><em>Packt Publishing</em></span>, written by the author of this book, in order to have more information regarding the management of the 1-Wire devices on the BeagleBone Black.</p></div></div></div><div class="section" title="Connecting the feeder"><div class="titlepage"><div><div><h2 class="title"><a id="ch03lvl2sec28"/>Connecting the feeder</h2></div></div></div><p>The fish feeder is a<a id="id115" class="indexterm"/> device that can release some feed by moving a motor. Its functioning is represented in the following screenshot:</p><div class="mediaobject"><img src="graphics/B00255_03_05.jpg" alt="Connecting the feeder"/></div><p>In the closed position, the motor is at horizontal position, so the feed cannot fall down, while in the <span class="strong"><strong>Open</strong></span> position, the motor is at vertical position, so that the feed can fall down. I have no real fish feeder, but looking at the preceding functioning, we can simulate it by using the servo motor shown in the following screenshot:</p><div class="mediaobject"><img src="graphics/B00255_03_06.jpg" alt="Connecting the feeder"/></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note31"/>Note</h3><p>The device can be purchased at the following link (or by surfing the Internet): <a class="ulink" href="http://www.cosino.io/product/nano-servo-motor">http://www.cosino.io/product/nano-servo-motor</a>.</p><p>The datasheet of this device is available at <a class="ulink" href="http://hitecrcd.com/files/Servomanual.pdf.">http://hitecrcd.com/files/Servomanual.pdf.</a>
</p></div></div><p>This device can be<a id="id116" class="indexterm"/> controlled in position, and it can rotate by 90 degrees with a proper <span class="strong"><strong>PWM</strong></span> signal in input. In fact, reading into the datasheet, we discover that the servo can be managed by using a periodic square waveform with a <span class="strong"><strong>period</strong></span> (<span class="strong"><strong>T</strong></span>) of 20ms and with a <span class="strong"><strong>high state time</strong></span> (<span class="strong"><strong>t<sub>high</sub></strong></span>) between 0.9ms and 2.1ms with 1.5ms as (more or less) center. So, we can consider the motor in the <span class="strong"><strong>Open</strong></span> position when <span class="emphasis"><em>t<sub>high</sub> =1ms</em></span> and in the <span class="strong"><strong>Close</strong></span> position when <span class="emphasis"><em>t<sub>high</sub>=2ms</em></span> (of course, these values should be carefully calibrated once the feeder is really built up!).</p><p>Let's connect the servo as described by the following table:</p><div class="informaltable"><table border="1"><colgroup><col style="text-align: left"/><col style="text-align: left"/></colgroup><thead><tr><th style="text-align: left" valign="bottom">
<p>Pin</p>
</th><th style="text-align: left" valign="bottom">
<p>Cable color</p>
</th></tr></thead><tbody><tr><td style="text-align: left" valign="top">
<p>P9.3 - Vcc</p>
</td><td style="text-align: left" valign="top">
<p>Red</p>
</td></tr><tr><td style="text-align: left" valign="top">
<p>P9.22 - PWM</p>
</td><td style="text-align: left" valign="top">
<p>Yellow</p>
</td></tr><tr><td style="text-align: left" valign="top">
<p>P9.1 - GND</p>
</td><td style="text-align: left" valign="top">
<p>Black</p>
</td></tr></tbody></table></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip37"/>Tip</h3><p>Interested readers can find more details about the PWM at <a class="ulink" href="https://en.wikipedia.org/wiki/Pulse-width_modulation">https://en.wikipedia.org/wiki/Pulse-width_modulation</a>.</p></div></div><p>To test the connections, we have to enable one PWM generator of the BeagleBone Black. So, to respect the preceding connections, we need the one which has its output line on pin <code class="literal">P9.22</code> of the expansion connectors. To do it, we can use the following commands:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# echo am33xx_pwm &gt; /sys/devices/bone_capemgr.9/slots</strong></span>
<span class="strong"><strong>root@beaglebone:~# echo bone_pwm_P9_22 &gt; /sys/devices/bone_capemgr.9/slots</strong></span>
</pre></div><p>Then, in the <code class="literal">/sys/devices/ocp.3</code> directory, we should find a new entry related to the new enabled PWM device, as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# ls -d /sys/devices/ocp.3/pwm_*</strong></span>
<span class="strong"><strong>/sys/devices/ocp.3/pwm_test_P9_22.12</strong></span>
</pre></div><p>Looking at the <code class="literal">/sys/devices/ocp.3/pwm_test_P9_22.12</code> directory, we see the files we can use to manage our new PWM device:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# ls /sys/devices/ocp.3/pwm_test_P9_22.12/</strong></span>
<span class="strong"><strong>driver	duty   modalias   period   polarity   power   run   subsystem   uevent</strong></span>
</pre></div><p>As we can deduce<a id="id117" class="indexterm"/> from the preceding file names, we have to properly set up the values into the files named as <code class="literal">polarity</code>, <code class="literal">period</code>, and <code class="literal">duty</code>. So, for instance, the center position of the servo can be achieved by using the following commands:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# echo 0 &gt; /sys/devices/ocp.3/pwm_test_P9_22.12/polarity</strong></span>
<span class="strong"><strong>root@beaglebone:~# echo 20000000 &gt; /sys/devices/ocp.3/pwm_test_P9_22.12/period</strong></span>
<span class="strong"><strong>root@beaglebone:~# echo 1500000 &gt; /sys/devices/ocp.3/pwm_test_P9_22.12/duty</strong></span>
</pre></div><p>The polarity is set to <code class="literal">0</code> to invert it, while the values written in the other files are time values expressed in nanoseconds, set at a period of 20ms and a duty cycle of 1.5ms, as requested by the datasheet (time values are all in nanoseconds.) Now, to move the gear totally clockwise, we can use the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# echo 2100000 &gt; /sys/devices/ocp.3/pwm_test_P9_22.12/duty</strong></span>
</pre></div><p>On the other hand, the following command is to move it totally anticlockwise:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# echo 900000 &gt; /sys/devices/ocp.3/pwm_test_P9_22.12/duty</strong></span>
</pre></div><p>So, by using the following command sequence, we can open and then close (with a delay of <code class="literal">1</code> second) the gate of the feeder:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>echo 1000000 &gt; /sys/devices/ocp.3/pwm_test_P9_22.12/duty</strong></span>
<span class="strong"><strong>sleep 1</strong></span>
<span class="strong"><strong>echo 2000000 &gt; /sys/devices/ocp.3/pwm_test_P9_22.12/duty</strong></span>
</pre></div><p>Note that by simply modifying the delay, we can control how much feed should fall down when the feeder is activated.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note32"/>Note</h3><p>The script that implements the feeder controlling mechanism can be found in the <code class="literal">chapter_03/feeder.sh</code> file in the book's example code repository.</p></div></div></div><div class="section" title="The water sensor"><div class="titlepage"><div><div><h2 class="title"><a id="ch03lvl2sec29"/>The water sensor</h2></div></div></div><p>The <a id="id118" class="indexterm"/>water sensor I used is shown in the following screenshot:</p><div class="mediaobject"><img src="graphics/B00255_03_07.jpg" alt="The water sensor"/></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note33"/>Note</h3><p>The device can be purchased at the following link (or by surfing the Internet): <a class="ulink" href="http://www.cosino.io/product/water_sensor">http://www.cosino.io/product/water_sensor</a>.</p></div></div><p>This is a really<a id="id119" class="indexterm"/> simple device that implements what is shown in the following screenshot, where the <span class="strong"><strong>resistor</strong></span> (<span class="strong"><strong>R</strong></span>) has been added to limit the current when the water closes the circuit:</p><div class="mediaobject"><img src="graphics/B00255_03_08.jpg" alt="The water sensor"/></div><p>When a single drop of water <span class="emphasis"><em>touches</em></span> two or more teeth of the comb in the schematic, the circuit is closed and the <span class="strong"><strong>output voltage</strong></span> (<span class="strong"><strong>Vout</strong></span>) drops from <span class="strong"><strong>Vcc</strong></span> to 0V.</p><p>So, if we wish to check the water level in our aquarium, that is, if we wish to check for a water leakage, we can imagine to put the aquarium into some sort of saucer, and then this device into it, so, if a water leakage occurs, the water is collected by the saucer, and the output voltage from the sensor should move from <span class="strong"><strong>Vcc</strong></span> to GND.</p><p>The GPIO used for this device are shown in the following table:</p><div class="informaltable"><table border="1"><colgroup><col style="text-align: left"/><col style="text-align: left"/></colgroup><thead><tr><th style="text-align: left" valign="bottom">
<p>Pin</p>
</th><th style="text-align: left" valign="bottom">
<p>Cable color</p>
</th></tr></thead><tbody><tr><td style="text-align: left" valign="top">
<p>P9.3 - 3.3V</p>
</td><td style="text-align: left" valign="top">
<p>Red</p>
</td></tr><tr><td style="text-align: left" valign="top">
<p>P8.16 - GPIO67</p>
</td><td style="text-align: left" valign="top">
<p>Yellow</p>
</td></tr><tr><td style="text-align: left" valign="top">
<p>P9.1 - GND</p>
</td><td style="text-align: left" valign="top">
<p>Black</p>
</td></tr></tbody></table></div><p>To test the<a id="id120" class="indexterm"/> connections, we have to define GPIO 67 as an input line with the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# ../bin/gpio_set.sh 67 in</strong></span>
</pre></div><p>Then, we can try to read the GPIO status while the sensor is in the water and when it is not, by using the following two commands:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# cat /sys/class/gpio/gpio67/value</strong></span>
<span class="strong"><strong>0</strong></span>
<span class="strong"><strong>root@beaglebone:~# cat /sys/class/gpio/gpio67/value</strong></span>
<span class="strong"><strong>1</strong></span>
</pre></div></div><div class="section" title="The final picture"><div class="titlepage"><div><div><h2 class="title"><a id="ch03lvl2sec30"/>The final picture</h2></div></div></div><p>The following screenshot shows the prototype I realized to implement this project and to test the software. As you can see, the aquarium has been replaced by a cup of water:</p><div class="mediaobject"><img src="graphics/B00255_03_09.jpg" alt="The final picture"/></div><p>Note that we have <a id="id121" class="indexterm"/>two external power suppliers: the usual one at 5V for the BeagleBone Black, and the other one with an output voltage of 12V for the other devices (you can see its connector in the upper-right corner on the right of the webcam.)</p></div></div></body></html>

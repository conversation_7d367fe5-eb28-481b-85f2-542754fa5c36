<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Final test</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Final test"><div class="titlepage"><div><div><h1 class="title"><a id="ch05lvl1sec32"/>Final test</h1></div></div></div><p>To test the <a id="id246" class="indexterm"/>prototype, I used some tricks to simulate the washing machine and the light in the room. The washing machine can be easily simulated by an audio/video played on the host PC with a reasonable volume level, while the room light on/off status can be simulated by using a small cup to cover the light sensor.</p><p>To set up all peripherals and drivers, we can use all the preceding commands or the <code class="literal">SYSINIT.sh</code> script as follows:</p><div class="informalexample"><pre class="programlisting">root@beaglebone:~# ./SYSINIT.sh
done!</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note51"/>Note</h3><p>This command can be found in the <code class="literal">chapter_05/SYSINIT.sh</code> file in the book's example code repository</p></div></div><p>As an initial state (<code class="literal">IDLE</code>), we should cover the light sensor (to simulate that the light is off) and we should stop the video/audio player (to simulate that the washing machine is off). Then, we have to set a low threshold level into the configuration file for both sound and light detection and a very short timeout (<code class="literal">5</code> seconds) in order to speed up the test. The following is my configuration file:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# cat config.sh</strong></span>
<span class="strong"><strong># Set the timeout value</strong></span>
<span class="strong"><strong>TIMEOUT=5</strong></span>

<span class="strong"><strong># Set the sound threshold</strong></span>
<span class="strong"><strong>SOUND_TH=200</strong></span>
<span class="strong"><strong> </strong></span>
<span class="strong"><strong># Set the light threshold</strong></span>
<span class="strong"><strong>LIGHT_TH=200</strong></span>

<span class="strong"><strong># Set the Whatsapp account</strong></span>
<span class="strong"><strong>WHATSAPP_USER=39YYYYYYYYYY</strong></span>
</pre></div><p>Then, I started the system, enabling all debugging messages on the terminal, by using the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# ./state_machine.sh -d -l</strong></span>
<span class="strong"><strong>state_machine.sh: using TIMEOUT=5 SOUND_TH=200 LIGHT_TH=200</strong></span>
<span class="strong"><strong>state_machine.sh: old-status=IDLE</strong></span>
<span class="strong"><strong>state_machine.sh: status=IDLE sound=0 light=0 t-t0=**********</strong></span>
<span class="strong"><strong>state_machine.sh: new-status=IDLE</strong></span>
<span class="strong"><strong>state_machine.sh: old-status=IDLE</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note52"/>Note</h3><p>Note that the initial state is <code class="literal">IDLE</code> and nothing changes until no new events are detected.</p></div></div><p>In the next output<a id="id247" class="indexterm"/> listing, I'm going to use the <code class="literal">...</code> characters to skip non-relevant lines:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>state_machine.sh: status=IDLE sound=0 light=0 t-t0=1398295379</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>state_machine.sh: status=IDLE sound=1 light=0 t-t0=1398295381</strong></span>
<span class="strong"><strong>state_machine.sh: new-status=SOUND</strong></span>
<span class="strong"><strong>state_machine.sh: old-status=SOUND</strong></span>
</pre></div><p>Now I'm going to simulate the following situation: first, I turn on the washing machine and wait for the end of its job. Then, I go to the laundry room to pick up my washed clothes. As already said before, I'm going to simulate the running washing machine with a video/audio player while the light on/off is simulated by uncovering/covering the light sensor with a cup.</p><p>Okay, the test begins. After a while, I start the video/audio player. So, a sound has been detected and the new state turns to <code class="literal">SOUND</code>:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>state_machine.sh: status=SOUND sound=1 light=0 t-t0=1</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>state_machine.sh: status=SOUND sound=0 light=0 t-t0=4</strong></span>
<span class="strong"><strong>state_machine.sh: new-status=IDLE</strong></span>
<span class="strong"><strong>state_machine.sh: old-status=IDLE</strong></span>
</pre></div><p>Ouch! For a moment, the sound level went under the threshold, so we switched again to the <code class="literal">IDLE</code> state! This is correct because it may happen that the washing machine stops for a while. Here is where the <code class="literal">timeout</code> enters in action, that is, we have to select it for longer than all the possible washing machine's pauses:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>state_machine.sh: status=IDLE sound=1 light=0 t-t0=1</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>state_machine.sh: old-status=SOUND</strong></span>
<span class="strong"><strong>cat: /sys/devices/ocp.3/helper.12/AIN0: Resource temporarily unavailable</strong></span>
</pre></div><p>This an error during the reading of the ADC input, but the software is written to retry the faulty operation without problems:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>state_machine.sh: status=SOUND sound=1 light=0 t-t0=4</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>state_machine.sh: status=SOUND sound=1 light=0 t-t0=6</strong></span>
<span class="strong"><strong>state_machine.sh: user=393492432127 msg="washing machine is started!"</strong></span>
<span class="strong"><strong>WARNING:yowsup.stacks.yowstack:Implicit declaration of parallel layers in a tuple is deprecated, pass a YowParallelLayer instead</strong></span>
<span class="strong"><strong>INFO:yowsup.demos.sendclient.layer:Message sent</strong></span>

<span class="strong"><strong>Yowsdown</strong></span>
<span class="strong"><strong>state_machine.sh: new-status=RUNNING</strong></span>
<span class="strong"><strong>state_machine.sh: old-status=RUNNING</strong></span>
</pre></div><p>Good! When the timeout expires while we are into the <code class="literal">SOUND</code> state, it means that a continuous sound has been detected, so it means that the washing machine has started its job.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip73"/>Tip</h3><p>Note that a more reliable implementation should use different timeouts to identify a specific transaction.</p></div></div><p>This is<a id="id248" class="indexterm"/> demonstrated in the following snippet:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>state_machine.sh: status=RUNNING sound=1 light=0 t-t0=2</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>state_machine.sh: new-status=RUNNING</strong></span>
<span class="strong"><strong>state_machine.sh: old-status=RUNNING</strong></span>
<span class="strong"><strong>state_machine.sh: status=RUNNING sound=0 light=0 t-t0=8</strong></span>
<span class="strong"><strong>state_machine.sh: new-status=NO_SOUND</strong></span>
<span class="strong"><strong>state_machine.sh: old-status=NO_SOUND</strong></span>
</pre></div><p>Now, I have stopped the video/audio player and no sound has been detected, so we switch to the <code class="literal">NO_SOUND</code> state:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>state_machine.sh: status=NO_SOUND sound=0 light=0 t-t0=1</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>state_machine.sh: status=NO_SOUND sound=0 light=0 t-t0=6</strong></span>
<span class="strong"><strong>state_machine.sh: user=393492432127 msg="washing machine has finished!"</strong></span>
<span class="strong"><strong>WARNING:yowsup.stacks.yowstack:Implicit declaration of parallel layers in a tuple is deprecated, pass a YowParallelLayer instead</strong></span>
<span class="strong"><strong>INFO:yowsup.demos.sendclient.layer:Message sent</strong></span>

<span class="strong"><strong>Yowsdown</strong></span>
<span class="strong"><strong>state_machine.sh: new-status=DONE</strong></span>
<span class="strong"><strong>state_machine.sh: old-status=DONE</strong></span>
</pre></div><p>Okay, when the timeout expires when we are in the <code class="literal">NO_SOUND</code> state, we switch to the <code class="literal">DONE</code> state to signal that the washing machine has finished its job:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>state_machine.sh: status=DONE sound=0 light=0 t-t0=1</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>state_machine.sh: status=DONE sound=0 light=1 t-t0=10</strong></span>
<span class="strong"><strong>state_machine.sh: new-status=LIGHT</strong></span>
<span class="strong"><strong>state_machine.sh: old-status=LIGHT</strong></span>
</pre></div><p>Now, I have uncovered the light sensor to simulate that someone has turned on the light in the laundry room:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>state_machine.sh: status=LIGHT sound=0 light=1 t-t0=1</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>state_machine.sh: status=LIGHT sound=0 light=1 t-t0=6</strong></span>
<span class="strong"><strong>state_machine.sh: new-status=ROOM</strong></span>
<span class="strong"><strong>state_machine.sh: old-status=ROOM</strong></span>
</pre></div><p>Again, the<a id="id249" class="indexterm"/> timeout has expired, so we can consider that the light has been on for a long time, which means that the user has received our WhatsApp message and they have come into the laundry room to pick up the laundry:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>state_machine.sh: status=ROOM sound=0 light=1 t-t0=1</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>state_machine.sh: status=ROOM sound=0 light=0 t-t0=8</strong></span>
<span class="strong"><strong>state_machine.sh: new-status=NO_LIGHT</strong></span>
<span class="strong"><strong>state_machine.sh: old-status=NO_LIGHT</strong></span>
</pre></div><p>Now, I have covered the light sensor again to simulate that the light in the laundry room has been turned off:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>state_machine.sh: status=NO_LIGHT sound=0 light=0 t-t0=1</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>state_machine.sh: status=NO_LIGHT sound=0 light=0 t-t0=6</strong></span>
<span class="strong"><strong>state_machine.sh: new-status=IDLE</strong></span>
<span class="strong"><strong>state_machine.sh: old-status=IDLE</strong></span>
<span class="strong"><strong>state_machine.sh: status=IDLE sound=0 light=0 t-t0=1</strong></span>
<span class="strong"><strong>state_machine.sh: new-status=IDLE</strong></span>
<span class="strong"><strong>state_machine.sh: old-status=IDLE</strong></span>
</pre></div><p>In the end, after <code class="literal">timeout</code> has expired, we can return to the <code class="literal">IDLE</code> state waiting for a new cycle to begin.</p><p>The following is the screenshot of my smartphone showing the WhatsApp activity:</p><div class="mediaobject"><img src="graphics/B00255_05_07.jpg" alt="Final test"/></div></div></body></html>

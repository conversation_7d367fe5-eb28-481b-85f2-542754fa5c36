<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Setting up the software</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Setting up the software"><div class="titlepage"><div><div><h1 class="title"><a id="ch09lvl1sec52"/>Setting up the software</h1></div></div></div><p>After the <a id="id440" class="indexterm"/>hardware has been set up, most of the job is done; to finish our job, we need to first install a tool to get access to our Twitter account, and then we have to add a mechanism to call it each time a successful identification process is accomplished. So, in the following sections I'm going to show how to install and correctly set up a command line tool to communicate with Twitter and then how to call it in three different programming languages for three different identification systems.</p><p>To simplify the project a bit, we can use a static list of known IDs stored in each program, but you can understand that this list can be easily managed by an external database. So, I leave this implementation as an exercise for you.</p><div class="section" title="Setting up the Twitter utility"><div class="titlepage"><div><div><h2 class="title"><a id="ch09lvl2sec77"/>Setting up the Twitter utility</h2></div></div></div><p>The utility I'm<a id="id441" class="indexterm"/> going to use to get access to a Twitter account is named with the single character <code class="literal">t</code>. The <code class="literal">t</code> program, as reported on its home page, derives from the Twitter SMS commands:</p><div class="blockquote"><blockquote class="blockquote"><p><span class="emphasis"><em>The CLI takes syntactic cues from the Twitter SMS commands, but it offers vastly more commands and capabilities than are available via SMS.</em></span></p></blockquote></div><p>In fact, once installed, it uses simple commands to update our Twitter status, follow/unfollow users, retrieve detailed information about a Twitter user, create a list for everyone you're following, and so on.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note99"/>Note</h3><p>For a complete reference of<a id="id442" class="indexterm"/> the <code class="literal">t</code> tool, the <a class="ulink" href="https://github.com/sferik/t">https://github.com/sferik/t</a> URL is a good starting point.</p></div></div><p>To install this tool into our BeagleBone Black, we first need the <code class="literal">ruby-dev</code> package with the <code class="literal">aptitude</code> program:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# aptitude install ruby-dev</strong></span>
</pre></div><p>Then, <code class="literal">t</code> is installed with the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# gem install t -V</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip121"/>Tip</h3><p>The execution of this command can be very slow! So, be patient and wait.</p></div></div><p>Once the installation has ended, we can execute the program, and if everything works well, a long list of available commands should be displayed as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# t -h</strong></span>
<span class="strong"><strong>Commands:</strong></span>
<span class="strong"><strong>  t accounts                          # List accounts</strong></span>
<span class="strong"><strong>  t authorize                         # Allows an application to request user...</strong></span>
<span class="strong"><strong>  t block USER [USER...]              # Block users.</strong></span>
<span class="strong"><strong>  t delete SUBCOMMAND ...ARGS         # Delete Tweets, Direct Messages, etc.</strong></span>
<span class="strong"><strong>  t direct_messages                   # Returns the 20 most recent Direct Mes...</strong></span>
<span class="strong"><strong>  t direct_messages_sent              # Returns the 20 most recent Direct Mes...</strong></span>
<span class="strong"><strong>  t dm USER MESSAGE                   # Sends that person a Direct Message.</strong></span>
<span class="strong"><strong>  t does_contain [USER/]LIST USER     # Find out whether a list contains a user.</strong></span>
<span class="strong"><strong>  t does_follow USER [USER]           # Find out whether one user follows ano...</strong></span>
<span class="strong"><strong>  ...</strong></span>
</pre></div><p>At this point, as<a id="id443" class="indexterm"/> done for other social networks, we have to create a special application for our Twitter account to get access to our data. To do so, let's<a id="id444" class="indexterm"/> point our browser to the <a class="ulink" href="https://apps.twitter.com/app/new">https://apps.twitter.com/app/new</a> URL. We'll see a form where we can fill out information about our new application. Simply fill in three fields: <span class="strong"><strong>Name</strong></span>, <span class="strong"><strong>Description</strong></span>, and <span class="strong"><strong>Website</strong></span>. Note that the name of the application needs to be unique across all Twitter users and cannot contain the word <code class="literal">twitter</code>, while the website can be arbitrary (for instance, <code class="literal">http://www.mydomain.com</code>), as shown in the following screenshot:</p><div class="mediaobject"><img src="graphics/B00255_09_06.jpg" alt="Setting up the Twitter utility"/></div><p>Regarding the <a id="id445" class="indexterm"/>
<span class="strong"><strong>Callback URL</strong></span> field, you can leave it blank. Then, click on the checkbox for developer terms agreement at the bottom of the page and then click on the <span class="strong"><strong>Create your Twitter application</strong></span> button.</p><p>Once your application has been successfully created, you will see a page where you can manage your application settings, as shown in the following screenshot:</p><div class="mediaobject"><img src="graphics/B00255_09_07.jpg" alt="Setting up the Twitter utility"/></div><p>Now, go to the<a id="id446" class="indexterm"/> <span class="strong"><strong>Permissions</strong></span> tab and change <span class="strong"><strong>Access</strong></span> type to <span class="strong"><strong>Read, Write and Access direct messages</strong></span> and save.</p><p>The next step is to authorize your application to access your Twitter account. For that, run the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# t authorize</strong></span>
<span class="strong"><strong>Welcome! Before you can use t, you'll first need to register an</strong></span>
<span class="strong"><strong>application with Twitter. Just follow the steps below:</strong></span>
<span class="strong"><strong>  1. Sign in to the Twitter Application Management site and click</strong></span>
<span class="strong"><strong>     "Create New App".</strong></span>
<span class="strong"><strong>  2. Complete the required fields and submit the form.</strong></span>
<span class="strong"><strong>     Note: Your application must have a unique name.</strong></span>
<span class="strong"><strong>  3. Go to the Permissions tab of your application, and change the</strong></span>
<span class="strong"><strong>     Access setting to "Read, Write and Access direct messages".</strong></span>
<span class="strong"><strong>  4. Go to the API Keys tab to view the consumer key and secret,</strong></span>
<span class="strong"><strong>     which you'll need to copy and paste below when prompted.</strong></span>

<span class="strong"><strong>Press [Enter] to open the Twitter Developer site.</strong></span>
</pre></div><p>Then, once the <span class="emphasis"><em>Enter</em></span> <a id="id447" class="indexterm"/>key has been pressed, the following output is shown:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>xprop:  unable to open display ''</strong></span>
<span class="strong"><strong>xprop:  unable to open display ''</strong></span>
<span class="strong"><strong>Enter your API key: /usr/bin/xdg-open: 1: eval: www-browser: not found</strong></span>
<span class="strong"><strong>/usr/bin/xdg-open: 1: eval: links2: not found</strong></span>
<span class="strong"><strong>/usr/bin/xdg-open: 1: eval: elinks: not found</strong></span>
<span class="strong"><strong>/usr/bin/xdg-open: 1: eval: links: not found</strong></span>
<span class="strong"><strong>/usr/bin/xdg-open: 1: eval: lynx: not found</strong></span>
<span class="strong"><strong>/usr/bin/xdg-open: 1: eval: w3m: not found</strong></span>
<span class="strong"><strong>xdg-open: no method available for opening 'https://apps.twitter.com'</strong></span>
</pre></div><p>Apart from the error messages due to the fact that <code class="literal">t</code> cannot execute any browser at all, we have to go to the <span class="strong"><strong>Keys and Access Token</strong></span> tab and enter the key in the <span class="strong"><strong>Consumer Key (API Key)</strong></span> field located under <span class="strong"><strong>Application Settings</strong></span>. Then, the tool will ask for the API secret, so you have to enter the <span class="strong"><strong>Consumer Secret (API Secret)</strong></span> in the same page as before.</p><div class="mediaobject"><img src="graphics/B00255_09_08.jpg" alt="Setting up the Twitter utility"/></div><p>When finished, if<a id="id448" class="indexterm"/> both the keys are valid, the tool will display the following output:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>In a moment, you will be directed to the Twitter app authorization page.</strong></span>
<span class="strong"><strong>Perform the following steps to complete the authorization process:</strong></span>
<span class="strong"><strong>  1. Sign in to Twitter.</strong></span>
<span class="strong"><strong>  2. Press "Authorize app".</strong></span>
<span class="strong"><strong>  3. Copy and paste the supplied PIN below when prompted.</strong></span>

<span class="strong"><strong>Press [Enter] to open the Twitter app authorization page.</strong></span>
</pre></div><p>As before, the tool will try to open the browser again in order to show the Twitter application authorization page, but of course, it cannot, so the following error message is shown:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>xprop:  unable to open display ''</strong></span>
<span class="strong"><strong>xprop:  unable to open display ''</strong></span>
<span class="strong"><strong>Enter the supplied PIN: /usr/bin/xdg-open: 1: eval: www-browser: not found</strong></span>
<span class="strong"><strong>/usr/bin/xdg-open: 1: eval: links2: not found</strong></span>
<span class="strong"><strong>/usr/bin/xdg-open: 1: eval: elinks: not found</strong></span>
<span class="strong"><strong>/usr/bin/xdg-open: 1: eval: links: not found</strong></span>
<span class="strong"><strong>/usr/bin/xdg-open: 1: eval: lynx: not found</strong></span>
<span class="strong"><strong>/usr/bin/xdg-open: 1: eval: w3m: not found</strong></span>
<span class="strong"><strong>xdg-open: no method available for opening 'https://api.twitter.com/oauth/authorize?oauth_callback=oob&amp;oauth_consumer_key=sHSeFMEGPRqRyf9V0UB4LtQOg&amp;oauth_nonce=9T9rSHXiaSiWXkh0ksVE5ioTcop0srz7xMG92VhVI&amp;oauth_signature=oNWj1Lj%225BUmrFkD%252B065axJv6WSeM%253D&amp;oauth_signature_method=HMAC-SHA1&amp;oauth_timestamp=**********&amp;oauth_token=J2fp-gAAAAAAhyrAAABAUA-YNw8&amp;oauth_version=1.0'</strong></span>
</pre></div><p>Okay, we just need to<a id="id449" class="indexterm"/> <span class="emphasis"><em>copy and paste</em></span> the preceding URL into our browser on the host PC to finish the job. To be clear, the URL is as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>https://api.twitter.com/oauth/authorize?oauth_callback=oob&amp;oauth_consumer_key=sHSeFMEGPRqRyf9V0UB4LtQOg&amp;oauth_nonce=9T9rSHXiaSiWXkh0ksVE5ioTcop0srz7xMG92VhVI&amp;oauth_signature=oNWj1Lj%225BUmrFkD%252B065axJv6WSeM%253D&amp;oauth_signature_method=HMAC-SHA1&amp;oauth_timestamp=**********&amp;oauth_token=J2fp-gAAAAAAhyrAAABAUA-YNw8&amp;oauth_version=1.0</strong></span>
</pre></div><p>Then, a new page where your Twitter credentials are to be put should appear, as shown in the following screenshot:</p><div class="mediaobject"><img src="graphics/B00255_09_09.jpg" alt="Setting up the Twitter utility"/></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip122"/>Tip</h3><p>Sorry for the Italian, but this is what my Twitter account's default language is set to.</p></div></div><p>Put your<a id="id450" class="indexterm"/> Twitter credentials and, if they are correct, the system should give you a PIN to be used to finish the authorization process (see the following screenshot):</p><div class="mediaobject"><img src="graphics/B00255_09_10.jpg" alt="Setting up the Twitter utility"/></div><p>Just <span class="emphasis"><em>copy and paste</em></span> the PIN into the terminal where the tool is running and press <span class="emphasis"><em>Enter</em></span> (again, you <a id="id451" class="indexterm"/>should not care about the error when launching the browser). However, if all steps are correct, the last message from <code class="literal">t</code> should be the following:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>Authorization successful.</strong></span>
</pre></div><p>Great! Now, we are ready to do our first tweet from the BeagleBone Black's command line! The command is as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@beaglebone:~# t update 'Hello there! This is my first tweet from the command line!'</strong></span>
<span class="strong"><strong>Tweet posted by @RodolfoGiometti.</strong></span>

<span class="strong"><strong>Run `t delete status 648174339569897474` to delete.</strong></span>
</pre></div><p>The following screenshot shows a snippet of my Twitter account where the recently sent message is published:</p><div class="mediaobject"><img src="graphics/B00255_09_11.jpg" alt="Setting up the Twitter utility"/></div></div><div class="section" title="The smart card implementation"><div class="titlepage"><div><div><h2 class="title"><a id="ch09lvl2sec78"/>The smart card implementation</h2></div></div></div><p>Let's now <a id="id452" class="indexterm"/>start with the first implementation of our identifying system by getting access to the smart card. The code is written in Python, and it shows a possible implementation of our access control system by using a smart card reader.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip123"/>Tip</h3><p>Note that implementation is very minimal since we limit our attention to the ATR parameter, which cannot be used to uniquely identify a smart card in all circumstances.</p></div></div><p>The program is very similar to the one stored in the <code class="literal">chapter_09/smart_card/smart_card.py</code> file, so I'm going to show only the relevant differences here:</p><div class="informalexample"><pre class="programlisting"># The known IDs
ID2NAME = {
        '11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11': "user1",
        '22 22 22 22 22 22 22 22 22 22 22 22 22 22 22 22 22 22 22': "user2",
        '3B BE 11 00 00 41 01 38 00 00 00 00 00 00 00 00 01 90 00': 'Rodolfo Giometti'
}
...
#
# Smart Card Observer
#

class printobserver(CardObserver):
   def update(self, observable, (addedcards, removedcards)):
      for card in addedcards:
         try:
            id = toHexString(card.atr)
         except:
            pass
         if len(id) == 0:
            continue
         logging.info("got tag ID " + id)

         # Verify that the tag ID is known and then
         # tweet the event
         try:
            name = ID2NAME[id]
        except:
           logging.info("unknow tag ID! Ignored")
           continue
 
        logging.info("Twitting that " + name + " was arrived!")
        call([t_cmd, t_args, name + " was arrived!"])</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note100"/>Note</h3><p>The complete code is stored in the <code class="literal">chapter_09/smart_card/smart_card2twitter.py</code> script in the book's example code repository.</p></div></div><p>The <code class="literal">ID2NAME</code>
<a id="id453" class="indexterm"/> array holds a list of known IDs, that is, our <span class="emphasis"><em>database</em></span> of valid IDs that are associated with well-known people. It's easy here to imagine that using a real database would be a better implementation, but this solution is fine for our teaching purposes.</p><p>The <code class="literal">update()</code> method extracts the smart card's ATR field, and then, instead of simply printing it, it compares the current ID with our internal database, and in case of positive match, it calls the <code class="literal">t</code> tool in order to update the Twitter account.</p></div><div class="section" title="The RFID LF implementation"><div class="titlepage"><div><div><h2 class="title"><a id="ch09lvl2sec79"/>The RFID LF implementation</h2></div></div></div><p>As in the preceding <a id="id454" class="indexterm"/>example, we have to modify the <code class="literal">chapter_09/rfid_lf/rfid_lf.sh</code> <span class="strong"><strong>Bash</strong></span> script<a id="id455" class="indexterm"/> a bit in order to call the <code class="literal">t</code> tool if the current tag ID is found in the list of the known IDs held by the <code class="literal">ID2NAME</code> array. A snippet of the modified code is as follows:</p><div class="informalexample"><pre class="programlisting"># The known IDs
declare -gA 'ID2NAME=(
   [************]="user1",
   [************]="user2"
   [6F007F4E1E40]="Rodolfo Giometti"
)'
&#x2026;
# Read the tags' IDs
cat $dev | while read id ; do
   # Remove the non printable characters
   id=$(echo $id | tr -cd '[:alnum:]')
   info "got tag ID $id"

   # Verify that the tag ID is known and then tweet the event
   name=${ID2NAME[$id]}
   if [ -z "$name" ] ; then
      info "unknow tag ID! Ignored"
   else
      info "Twitting that $name was arrived!"
      $t_cmd $t_args "$name was arrived!"
   fi
done</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note101"/>Note</h3><p>The <a id="id456" class="indexterm"/>complete code is stored in the <code class="literal">chapter_09/rfid_lf/rfid_lf2twitter.sh</code> script in the book's example code repository.</p></div></div></div><div class="section" title="The RFID UHF implementation"><div class="titlepage"><div><div><h2 class="title"><a id="ch09lvl2sec80"/>The RFID UHF implementation</h2></div></div></div><p>The last implementation<a id="id457" class="indexterm"/> is in C<span class="strong"><strong>,</strong></span> and it uses the RFID UHF reader in order to take the identification string. The trick is now well-known; we simply need to modify the <code class="literal">chapter_09/rfid_uhf/rfid_uhf.c</code> program in order to check the current tag ID with the known ones held again in the well-known<code class="literal"> ID2NAME</code> array. The code snippet is as follows:</p><div class="informalexample"><pre class="programlisting">/* The known IDs */
struct associative_array_s {
   char *id;
   char *name;
} ID2NAME[] = {
   { "************************", "user1" },
   { "************************", "user2" },
   { "e280113020002021dda500ab", "Rodolfo Giometti" },
};
...
   /* The main loop */
   while (1) {
      /* Do the inventory */
      ret = caenrfid_inventory(&amp;handle, string, &amp;tag, &amp;size);
      if (ret &lt; 0) {
         err("cannot get data (err=%d)", ret);
         exit(EXIT_FAILURE);
      }

      /* Report results */
      for (i = 0; i &lt; size; i++) {
         str = bin2hex(tag[i].id, tag[i].len);
         EXIT_ON(!str);
         info("got tag ID %.*s", tag[i].len * 2, str);

         for (j = 0; j &lt; ARRAY_SIZE(ID2NAME); j++)
            if (strncmp(str, ID2NAME[j].id,
               tag[i].len * 2) == 0)
            break;
         if (j &lt; ARRAY_SIZE(ID2NAME)) {
            info("Twitting that %s was arrived!",
               ID2NAME[j].name);
            ret = asprintf(&amp;cmd, "%s %s %s was arrived!", t_cmd, t_arg, ID2NAME[j].name);
            EXIT_ON(ret &lt; 1);
            ret = system(cmd);
            EXIT_ON(ret &lt; 0);
            free(cmd);
         } else
         info("unknow tag ID! Ignored");

         free(str);
   }

   /* Free inventory data */
   free(tag);
}</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note102"/>Note</h3><p>The complete code is stored in the <code class="literal">chapter_09/rfid_uhf/rfid_uhf2twitter.c</code> file in the book's example code repository.</p></div></div><p>Before <a id="id458" class="indexterm"/>executing it, don't forget to compile it!</p></div></div></body></html>

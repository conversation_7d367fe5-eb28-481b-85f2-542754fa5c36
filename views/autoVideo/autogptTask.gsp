<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">AutoGPT V2</h3>
                    <div class="form-group table-responsive" id="intrst-area">
                        <div class="form-group">
                           <label for="chapterId">ChapterId</label><br>
                           <input type="number" class="w-100 form-control" id="chapterId" name="chapterId">
                           </div>

                           <button class="btn btn-primary"  onclick="startAutoGPT()">Submit</button>
                           <div id="results"></div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
<script>
    var resId
    function startAutoGPT(){
        var chapterId = $('#chapterId').val();
        if(chapterId==''){
            alert('Please enter the chapter id');
            return;
        }
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="prompt" action="storePdfVectors" params="'chapterId='+chapterId" onSuccess = "vectorStored(data);"/>
    }

    function vectorStored(data){
        $('.loading-icon').addClass('hidden');
        if(data.status=='OK'){
            document.getElementById('results').innerHTML='PDF Vectors stored successfully';
            resId = data.resId;
            storeMetadata();
        }else{
            document.getElementById('results').innerHTML='Error in storing PDF Vectors';
        }
    }

    function storeMetadata(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="autogpt" action="getChapterMetaData" params="'resId='+resId" onSuccess = "metadataStored(data);"/>
    }

    function metadataStored(data){
        $('.loading-icon').addClass('hidden');
        if(data.status=='OK'){
            document.getElementById('results').innerHTML+='<br>Metadata stored successfully';
            exerciseCollector();
        }else{
            document.getElementById('results').innerHTML+='<br> Error in storing metadata';
        }
    }

    function exerciseCollector(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="autogpt" action="exerciseCollector" params="'resId='+resId" onSuccess = "exercisesCollected(data);"/>
    }

    function exercisesCollected(data){
        $('.loading-icon').addClass('hidden');
        if(data.status=='OK'){
            document.getElementById('results').innerHTML+='<br>Exercises collected successfully';
            questionBankBuilder();
        }else{
            document.getElementById('results').innerHTML+='<br> Error in collecting exercises';
        }
    }

    function questionBankBuilder(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="autogpt" action="questionBankBuilder" params="'resId='+resId" onSuccess = "questionBankBuilt(data);"/>
    }

    function questionBankBuilt(data){
        $('.loading-icon').addClass('hidden');
        if(data.status=='OK'){
            document.getElementById('results').innerHTML+='<br>Question bank built successfully';
        }else{
            document.getElementById('results').innerHTML+='<br> Error in building question bank';
        }
    }


</script>

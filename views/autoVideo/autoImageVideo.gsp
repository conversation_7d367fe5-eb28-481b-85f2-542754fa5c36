<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Automated Video</title>
    <link rel="icon"  href="${assetPath(src: 'landingpageImages/favicon.ico')}" type="image/x-icon">
    <link rel="android-touch-icon" href="${assetPath(src: 'landingpageImages/wsmetalogo.png')}"/>
    <link rel="windows-touch-icon" href="icon.png" />
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">
    <link href="https://fonts.googleapis.com/css?family=Rubik:400,500" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Merriweather:300,400,700" rel="stylesheet">
    <asset:stylesheet src="autovideo/style.css"/>
    <asset:stylesheet src="autovideo/splitting.css"/>
    <asset:stylesheet src="autovideo/splitting-cells.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
</head>
<body class="darktheme">

<div id="overlaydark"></div>

<div class="container-fluid automated_video p-0">
    <audio id="backgroundAudioElement" loop="loop" src="${assetPath(src: 'autovideo/automatedVideoMusic.mp3')}"></audio>

    <section class="ws-title-wrapper p-3" id="beforeStartVideo">
        <div class="ws-title d-flex flex-column justify-content-center align-items-center">
            <div class="img-wrapper">
                <img src="${assetPath(src: 'autovideo/wslogo.svg')}" class="img-responsive">
                <p>Always Keep Learning !</p>
            </div>
            <div class="form-wrapper">
                <select id="client" class="form-control">
                    <option value="wonderslate">Wonderslate</option>
                    <option value="prepjoy">Prepjoy</option>
                </select>
                <select id="timeDuration" class="form-control" onchange="changeTimeDuration(this)">
                    <option value="" selected="selected">Select Time Duration</option>
                    <option value="5">5 Seconds</option>
                    <option value="6">6 Seconds</option>
                    <option value="7">7 Seconds</option>
                    <option value="8">8 Seconds</option>
                    <option value="9">9 Seconds</option>
                    <option value="10">10 Seconds</option>
                </select>
                <p id="timeDurationError" class="d-none"></p>
                <select id="theme" class="form-control" onchange="changeTheme()">
                    <option value="darkmode">Dark Mode</option>
                    <option value="lightmode">Light Mode</option>
                </select>
            </div>
            <div class="button-wrapper mt-3">
                <button type="button" class="btn" id="next-btn" onclick="startSlides();">Next</button>
            </div>
        </div>
    </section>

    <section class="ws-title-wrapper" id="videoStarted" style="display: none;">
        <div class="ws-title d-flex flex-column justify-content-center justify-content-md-start align-items-center align-items-md-start pl-md-3 pt-md-3">
            <div class="img-wrapper" style="display: none;" id="wonderslate">
                <img src="${assetPath(src: 'ws/mobile-ws-logo.svg')}" class="img-responsive animate__animated">
                <span class="ws-link animate__animated">
                    <a href="https://www.wonderlsate.com" target="_blank">www.wonderslate.com</a>
                </span>
            </div>
            <div class="img-wrapper" style="display: none;" id="prepjoy">
                <img src="${assetPath(src: 'autovideo/prepjoy-logo.png')}" class="img-responsive animate__animated">
                <span class="ws-link animate__animated">
                    <a href="https://www.prepjoy.com" target="_blank">www.prepjoy.com</a>
                </span>
            </div>
        </div>

        <div class="slides">
            <div id="wonderslateEndImage" class="animate__animated animate__fadeOut d-none" style="background-image:url(${assetPath(src: 'autovideo/wonderslatefeatures.png')});"></div>
            <div id="prepjoyEndImage" class="animate__animated animate__fadeOut d-none" style="background-image:url(${assetPath(src: 'autovideo/prepjoyfeatures.png')});"></div>
            <div id="backgroundImages"></div>
            <div id="slideText" class="animate__animated animate__slow"></div>
        </div>
    </section>

</div>
<asset:javascript src="landingpage/jquery-3.2.1.min.js" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>
<asset:javascript src="autovideo/splitting.min.js"/>
<script src='https://cdnjs.cloudflare.com/ajax/libs/gsap/3.5.1/gsap.min.js'></script>
<script>
    var setSlides;
    var slideData;
    var slide_count = 0;
    var slide_numb = 1;
    var timer;
    var backgroundAudioElement;
    var delayTime = 500;
    var fadeOutTime;

    function changeTimeDuration(time) {
        timer = time.value*1000;
        fadeOutTime = timer-2000;
        $('#timeDurationError').addClass('d-none');
    }

    function changeTheme() {
        if(document.getElementById("theme").selectedIndex==0) {
            $("body").addClass("darktheme").removeClass("lightheme");
        }
        else if(document.getElementById("theme").selectedIndex==1) {
            $("body").addClass("lightheme").removeClass("darktheme");
        }
    }

    //getting slides data on load of the page
    getSlideDetails();

    function getSlideDetails(){
        $("body").addClass("video-making");
        var resId = "${params.resId}";
        <g:remoteFunction controller="autoVideo" action="getResourceSlide" params="'resId='+resId" onSuccess="setSlideDetails(data);"/>
    }

    //store slides data to array slideData;
    function setSlideDetails(data) {
        setSlides = data;
        if(setSlides.status=="OK"){
            slideData = setSlides.resources;
            getSlideImages();
        }
    }

    //start slides of the video
    function startSlides(){
        if( !$('#timeDuration').val() ) {
            document.getElementById('timeDurationError').innerHTML = "Please select time duration of slides.";
            $('#timeDurationError').removeClass('d-none');
        } else {
            //coming inside the resource
            if(setSlides.status=="OK"){
                slideData = setSlides.resources;

                $("#videoStarted").show();
                $("#beforeStartVideo").hide();

                if(document.getElementById("client").selectedIndex==0) {
                    $("#wonderslate").css("display","flex");
                } else if(document.getElementById("client").selectedIndex==1){
                    $("#prepjoy").css("display","flex");
                }

                backgroundAudioElement = document.getElementById("backgroundAudioElement");
                backgroundAudioElement.volume = 0.1;
                setTimeout(function(){
                    backgroundAudioElement.play();
                },delayTime);
            }
            setTimeout(function () {
                slideData = slideData.reverse();
                slideDetails();
            },delayTime);

        }
    }

    //Shift to Next slide with timer
    function nextSlide(){
        if(slide_count < slideData.length - 1){
            slide_count++;
            slide_numb++;
            slideDetails();
        } else {
            //last slide for Wonderslate/Prepjoy

            $("#overlaydark").addClass("animate__animated animate__slow animate__fadeOut");
            if(document.getElementById("client").selectedIndex==0) {
                $("#wonderslateEndImage").removeClass("animate__fadeOut d-none").addClass("animate__fadeIn");
                $("#wonderslate img, #wonderslate span").addClass("animate__fadeOut");
            } else if(document.getElementById("client").selectedIndex==1){
                $("#prepjoyEndImage").removeClass("animate__fadeOut d-none").addClass("animate__fadeIn");
                $("#prepjoy img, #prepjoy span").addClass("animate__fadeOut");
                $("body").addClass("prepjoy-video-end");
            }

            $("#backgroundAudioElement").animate({volume: 0.0}, timer);

            setTimeout(function(){
                backgroundAudioElement.pause();
            },timer);
        }
    }

    function slideDetails(){
        displaySlides(slide_count);
    }

    function displaySlides(index){
        var contentText = slideData[index].slide1;
        var slideText = document.querySelector("#slideText");

        setTimeout(function () {
            var hideImageNo = slideData.length - (slide_count + 1);
            $('#bgImage_'+hideImageNo).addClass("animate__fadeOut");
        },(fadeOutTime+1000));

        setTimeout(function () {
            slideText.innerHTML = contentText;
        },delayTime);

        setTimeout(nextSlide,timer);
        setTimeout(fadeInAnimation,0);
        setTimeout(fadeOutAnimation,fadeOutTime);
        setTimeout(textAnimation,delayTime);
    }

    function getSlideImages() {
        var newArr = [];
        var imgStr = "";
        slideData = slideData.reverse();
        for(var i=0; i<slideData.length; i++){
            imgStr += "<div id='bgImage_"+i+"' class='background-images animate__animated animate__slow'>"+slideData[i].slide+
                "<div class='slider_text d-md-none'>" +slideData[i].slide1+"</div>" +
                "</div>";
        }
        document.getElementById("backgroundImages").innerHTML = imgStr;

        $(".background-images img").each(function() {
            var imgSrc = $(this).attr("src");
            newArr.push(imgSrc);
        });

        //newArr.reverse();

        for(var i=0; i<newArr.length; i++){
            var imgSource = newArr[i];
            $('#bgImage_'+i).attr("style","background-image:url("+imgSource+")");
        }

        if ($('.background-images img').length === 0) {
            $('#videoStarted').addClass('no-images');
            $('#backgroundImages').css('height','0px');
        }

    }

    function fadeInAnimation() {
        $("#slideText").removeClass("animate__fadeOut").addClass("animate__fadeIn animate__delay-1s");
    }

    function fadeOutAnimation() {
        $("#slideText").removeClass("animate__fadeIn animate__delay-1s").addClass("animate__fadeOut");
    }

    function textAnimation() {
        const target = document.querySelector('#slideText p');
        const results = Splitting({ target: target, by: 'lines' });

        results[0].lines.forEach((line, index) => {
            line.forEach((word) => {
                gsap.from(word, {
                    //duration: 1,
                    opacity: 0,
                    delay: index /  4,
                });
            });
        });
    }

</script>

</body>
</html>

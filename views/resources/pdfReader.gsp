<!DOCTYPE html>
<!--
Copyright 2012 Mozilla Foundation

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Adobe CMap resources are covered by their own copyright but the same license:

    Copyright 1990-2015 Adobe Systems Incorporated.

See https://github.com/adobe-type-tools/cmap-resources
-->
<html dir="ltr" mozdisallowselectionprint>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="google" content="notranslate">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>PDF.js viewer</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" integrity="sha512-MV7K8+y+gLIBoVD59lQIYicR65iaqukzvf/nwasF0nqhPay5w/9lJmVM2hMDcnK1OnMGCdVK+iQrJ7lzPJQd1w==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="/assets/viewer.css">
    <!-- This snippet is used in production (included from viewer.html) -->
    <link rel="resource" type="application/l10n" href="locale/locale.properties">
%{--    <script src="/assets/pdf.js"></script>--}%
    <script src="/assets/viewer.js"></script>

    <script src="/assets/jquery-1.11.2.min.js"></script>
    <script src="/assets/annotator-full.js"></script>
    <script  src="/assets/annotator.touch.js"></script>
    <link rel="stylesheet" href="/assets/annotator-template.css">
    <script  src="/assets/popper.min.js"></script>
    <script src="/assets/landingpage/bootstrap.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/13.0.1/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.4.0/highlight.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400..700;1,400..700&family=Merriweather:ital,wght@0,300;0,400;0,700;0,900;1,300;1,400;1,700;1,900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap" rel="stylesheet">
    <style>
        .pdf-annotatoe-hll{
            background-color: red;
        }
        .card-hl{
            padding: 1rem;
            background: #fff;
        }
        .popover{
            background: #fff;
            border: 0.5px solid rgba(68, 68, 68, 0.24);
            box-sizing: border-box;
            box-shadow: 0 2px 16px rgba(0,0,0, 0.5);
        }
        .popover .btn{
            background-color:#ffffff;
            color:#444444;
            background: none;
            border:none;
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.8%);
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            font-size: 12px;
            border-radius: 4px;
        }
        .popover .btn.btn-primary {
            color: #fff;
            background-color: #007bff;
            border-color: #007bff;
        }
        .popover .btn.btn-danger {
            color: #fff;
            background-color: #dc3545;
            border-color: #dc3545;
        }
        .popover .form-control{
            display: block;
            min-width: 200px;
            padding: .375rem .75rem;
            font-size: 1rem;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #ced4da;
            border-radius: .25rem;
            transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
            margin: 1rem;
        }
        .popover .btn.btn-secondary {
            color: #fff;
            background-color: #6c757d;
            border-color: #6c757d;
        }
        .popover .btn.btn-success {
            color: #fff;
            background-color: #28a745;
            border-color: #28a745;
        }
        .popover div.d-flex{
            display: flex;
            justify-content: space-around;
            background: #ededed;
            padding: 4px;
        }
        .popover-body p{
            margin-bottom: 10px;
            padding: 1rem;
        }

        .popover{
            background: #fff;
            border-radius: 4px;

            min-width: 200px;
        }
        .lazyLoader{
            background: #fff;
            height: 100vh;
            position: absolute;
            opacity: 0.9;
            z-index: 9999;
            top: 0;
            left: 0;
            right: 0;
        }
        .lazyLoader h2{
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            font-size: 5rem;
        }

        .loaderHidden{
            display: none;
        }
        @media print {
            body {
                display:none;
            }
        }
        html,body{
            scroll-behavior: smooth !important;
        }

        .anim{
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
        }
        .pdfReaderFooter{
            position: relative;
            top: -60px;
            background: #f4f4f4;
            height: 50px;
            padding: 5px;
            display: flex;
            width: 100%;
            justify-content: space-evenly;
            align-items: center;
        }
        @media (max-width: 768px){
            .pdfReaderFooter{
                height: 40px;
            }
        }
        .footerPageNumbers,.range{
            display: flex;
            justify-content: center;
            align-items: center;
            height: 50%;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .lazyLoader{
                top: 70px;
            }
        }

        .annotateColorGreen{
            background-color: green;
            border-radius: 3px;
        }
        .annotateColorBlue{
            background-color: blue;
            border-radius: 3px;
        }
        .annotateColorYellow{
            background-color: yellow;
            border-radius: 3px;
        }
        .annotateColorPicker{
            position: absolute;
            top: -55px;
            background: #fff;
            width: auto;
            padding: 10px;
            border-radius: 5px;
            display: flex;
            gap: 10px;
            justify-content: center;
            align-items: center;
            right: 75px;
        }
        .annotateColorPicker:after{
            content: '';
            width: 20px;
            height: 20px;
            position: absolute;
            background: #fff;
            top: 35px;
            transform: rotate(45deg);
            left: 55px;
            z-index: -1;
            border-left: 1px solid;
        }
        .annotateColorPicker button,
        .notesColorBtn,
        .annotator-editor .notesColorBtn{
            width: 30px;
            height: 30px;
            border-radius: 50%;
            opacity: 0.6;
            cursor: pointer;
        }
        .annotator-editor .notesColorBtn:after{
            position: revert;
            width: 0;
            height: 0;
            background-image: none;
        }
        .annotator-editor .notesColorBtn{
            padding: 0;
        }
        .annotator-editor .notesColorBtn.green:hover{
            padding: 0;
            background: green;
        }
        .annotator-editor .notesColorBtn.blue:hover{
            padding: 0;
            background: blue;
        }
        .annotateColorPicker button.blue,
        .notesColorBtn.blue{
            background: blue;
            border: 2px solid transparent;
        }
        .annotateColorPicker button.green,
        .notesColorBtn.green{
            background: green;
            border: 2px solid transparent;
        }
        .annotateColorPicker button.yellow,
        .notesColorBtn.yellow{
            background: yellow;
            border: 2px solid transparent;
        }
        .annotator-adder .annotate-btn{
            border-right: 1px solid #ededed;
        }
        .annotateColorPicker button.selected,
        .notesColorBtn.selected{
            border-color: black;
        }
        .notesColorBtn.yellow:hover,
        .notesColorBtn.yellow:focus,
        .notesColorBtn.yellow:active{
            background: yellow !important;
        }
        .notesColorBtn.green:hover,
        .notesColorBtn.green:focus,
        .notesColorBtn.green:active{
            background: green !important;
        }
        .notesColorBtn.blue:hover,
        .notesColorBtn.blue:focus,
        .notesColorBtn.blue:active{
            background: blue !important;
        }

        #selection-box {
            position: absolute;
            border: 1px solid transparent;
            display: none;
            z-index: 1000;
            border-radius: 5px;
        }
        .overlay {
            position: fixed;
            background-color: rgba(0, 0, 0, 0.5); /* semi-transparent overlay */
            pointer-events: none;
            z-index: 999; /* make sure overlay is on top */
        }
        #snip-btn {
            display: none;
            position: absolute;
        }
        .mainContainerBorder{
            border: 2px solid blue;
            border-radius: 10px;
        }
        .process-message{
            position: absolute;
            top: 50%;
            z-index: 99999;
            width: 250px;
            height: 70px;
            background: #fff;
            border-radius: 5px;
            border: 1px solid;
            display: none;
            justify-content: center;
            align-items: center;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .mid-overlay{
            position: absolute;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            display: none;
            top: 0;
            left: 0;
        }
        pre{
            white-space: break-spaces !important;
            line-height: 1.6 !important;
        }
        .katex-display>.katex{
            text-align: justify !important;
        }
    </style>
</head>
<body style="overflow: hidden">
<div class="lazyLoader">
    <div class="anim">
        <div class="slider-anim">
            <div class="lottieLoader">
            </div>
            <div class="loaderText">
                <h3 id="sloganTexts" style="font-family:'poppins', sans-serif;color: #6677">'Ebooks: the future of reading, today.'</h3>
            </div>
        </div>
    </div>
</div>

<div id="outerContainer">
    <!-- sidebarContainer -->
    <div  id="sidebarContainer">
        <div id="toolbarSidebar" >
            <div style="display: none" class="splitToolbarButton toggled">
                <button id="viewThumbnail" class="toolbarButton toggled" title="Show Thumbnails" tabindex="2" data-l10n-id="thumbs">
                    <span data-l10n-id="thumbs_label">Thumbnails</span>
                </button>
                <button id="viewOutline" class="toolbarButton" title="Show Document Outline (double-click to expand/collapse all items)" tabindex="3" data-l10n-id="document_outline">
                    <span data-l10n-id="document_outline_label">Document Outline</span>
                </button>
                <button id="viewAttachments" class="toolbarButton" title="Show Attachments" tabindex="4" data-l10n-id="attachments">
                    <span data-l10n-id="attachments_label">Attachments</span>
                </button>
            </div>
        </div>
        <div id="sidebarContent">
            <div id="thumbnailView">
            </div>
            <div id="outlineView" class="hidden">
            </div>
            <div id="attachmentsView" class="hidden">
            </div>
        </div>
        <div id="sidebarResizer" class="hidden"></div>
    </div>

    <!-- mainContainer -->
    <div id="mainContainer">
        <!-- findbar -->
        <div class="findbar hidden doorHanger" id="findbar">
            <div id="findbarInputContainer">
                <input id="findInput" class="toolbarField" title="Find" placeholder="Find in document…" tabindex="91" data-l10n-id="find_input">
                <div class="splitToolbarButton">
                    <button id="findPrevious" class="toolbarButton findPrevious" title="Find the previous occurrence of the phrase" tabindex="92" data-l10n-id="find_previous">
                        <span data-l10n-id="find_previous_label">Previous</span>
                    </button>
                    <div class="splitToolbarButtonSeparator"></div>
                    <button id="findNext" class="toolbarButton findNext" title="Find the next occurrence of the phrase" tabindex="93" data-l10n-id="find_next">
                        <span data-l10n-id="find_next_label">Next</span>
                    </button>
                </div>
            </div>

            <div id="findbarOptionsOneContainer">
                <input type="checkbox" id="findHighlightAll" class="toolbarField" tabindex="94">
                <label for="findHighlightAll" class="toolbarLabel" data-l10n-id="find_highlight">Highlight all</label>
                <input type="checkbox" id="findMatchCase" class="toolbarField" tabindex="95">
                <label for="findMatchCase" class="toolbarLabel" data-l10n-id="find_match_case_label">Match case</label>
            </div>
            <div id="findbarOptionsTwoContainer">
                <input type="checkbox" id="findEntireWord" class="toolbarField" tabindex="96">
                <label for="findEntireWord" class="toolbarLabel" data-l10n-id="find_entire_word_label">Whole words</label>
                <span id="findResultsCount" class="toolbarLabel hidden"></span>
            </div>

            <div id="findbarMessageContainer">
                <span id="findMsg" class="toolbarLabel"></span>
            </div>
        </div>
        <div  id="secondaryToolbar" class="secondaryToolbar hidden doorHangerRight">
            <div id="secondaryToolbarButtonContainer">
                <a href="#" id="secondaryViewBookmark" class="secondaryToolbarButton bookmark visibleSmallView" title="Current view (copy or open in new window)" tabindex="55" data-l10n-id="bookmark">
                    <span data-l10n-id="bookmark_label">Current View</span>
                </a>

                <div class="horizontalToolbarSeparator visibleLargeView"></div>

                <button id="firstPage" class="secondaryToolbarButton firstPage" title="Go to First Page" tabindex="56" data-l10n-id="first_page">
                    <span data-l10n-id="first_page_label">Go to First Page</span>
                </button>
                <button id="lastPage" class="secondaryToolbarButton lastPage" title="Go to Last Page" tabindex="57" data-l10n-id="last_page">
                    <span data-l10n-id="last_page_label">Go to Last Page</span>
                </button>

                <div class="horizontalToolbarSeparator"></div>

                <button id="pageRotateCw" class="secondaryToolbarButton rotateCw" title="Rotate Clockwise" tabindex="58" data-l10n-id="page_rotate_cw">
                    <span data-l10n-id="page_rotate_cw_label">Rotate Clockwise</span>
                </button>
                <button id="pageRotateCcw" class="secondaryToolbarButton rotateCcw" title="Rotate Counterclockwise" tabindex="59" data-l10n-id="page_rotate_ccw">
                    <span data-l10n-id="page_rotate_ccw_label">Rotate Counterclockwise</span>
                </button>

                <div class="horizontalToolbarSeparator"></div>

                <button id="cursorSelectTool" class="secondaryToolbarButton selectTool toggled" title="Enable Text Selection Tool" tabindex="60" data-l10n-id="cursor_text_select_tool">
                    <span data-l10n-id="cursor_text_select_tool_label">Text Selection Tool</span>
                </button>
                <button id="cursorHandTool" class="secondaryToolbarButton handTool" title="Enable Hand Tool" tabindex="61" data-l10n-id="cursor_hand_tool">
                    <span data-l10n-id="cursor_hand_tool_label">Hand Tool</span>
                </button>

                <div class="horizontalToolbarSeparator"></div>

                <button id="scrollVertical" class="secondaryToolbarButton scrollModeButtons scrollVertical toggled" title="Use Vertical Scrolling" tabindex="62" data-l10n-id="scroll_vertical">
                    <span data-l10n-id="scroll_vertical_label">Vertical Scrolling</span>
                </button>
                <button id="scrollHorizontal" class="secondaryToolbarButton scrollModeButtons scrollHorizontal" title="Use Horizontal Scrolling" tabindex="63" data-l10n-id="scroll_horizontal">
                    <span data-l10n-id="scroll_horizontal_label">Horizontal Scrolling</span>
                </button>
                <button id="scrollWrapped" class="secondaryToolbarButton scrollModeButtons scrollWrapped" title="Use Wrapped Scrolling" tabindex="64" data-l10n-id="scroll_wrapped">
                    <span data-l10n-id="scroll_wrapped_label">Wrapped Scrolling</span>
                </button>

                <div class="horizontalToolbarSeparator scrollModeButtons"></div>

                <button id="spreadNone" class="secondaryToolbarButton spreadModeButtons spreadNone toggled" title="Do not join page spreads" tabindex="65" data-l10n-id="spread_none">
                    <span data-l10n-id="spread_none_label">No Spreads</span>
                </button>
                <button id="spreadOdd" class="secondaryToolbarButton spreadModeButtons spreadOdd" title="Join page spreads starting with odd-numbered pages" tabindex="66" data-l10n-id="spread_odd">
                    <span data-l10n-id="spread_odd_label">Odd Spreads</span>
                </button>
                <button id="spreadEven" class="secondaryToolbarButton spreadModeButtons spreadEven" title="Join page spreads starting with even-numbered pages" tabindex="67" data-l10n-id="spread_even">
                    <span data-l10n-id="spread_even_label">Even Spreads</span>
                </button>

                <div class="horizontalToolbarSeparator spreadModeButtons"></div>

                <button id="documentProperties" class="secondaryToolbarButton documentProperties" title="Document Properties…" tabindex="68" data-l10n-id="document_properties">
                    <span data-l10n-id="document_properties_label">Document Properties…</span>
                </button>
            </div>
        </div>  <!-- secondaryToolbar -->
        <div  class="toolbar" style="transform: translateY(0);display: none">
            <div id="toolbarContainer">
                <div id="toolbarViewer">
                    <div id="toolbarViewerLeft">
                        <div class="rowOne">
                            <!--- Back button --->
                            <div class="backButton">
                                <button class="backBtn" title="Back to Chapter"><i class="fa-solid fa-arrow-left-long"></i></button>
                            </div>
                            <!--- Title only visible for non generic books --->
                            <div class="bookTitle" style="display: none">
                                <p id="bookTitle"></p>
                            </div>

                            <!--- Chapter list visible only for generic books --->
                            <div class="chapterListWrapper" style="margin-left: 10px;display: none">
                                <div id="chaptersListDisplay">
                                    <g:select id="chapterId" class="form-control" optionKey="id" optionValue="name" onchange="javascript:window.parent.getHtmlsDataByChapter(this.value);"
                                              value="${params.chapterId}" name="chapterId" from="${topicMst}"/>
                                </div>
                            </div>
                        </div>
                        <div class="rowTwo">
                            <div class="toolbarViewerLeftWrapper">

                                <div class="highlightsAndNotes" id="highlightsAndNotes">
                                    <button class="highlightsAndNotesBtn" title="Notes and Highlights">
                                    </button>
                                </div>
                                <button class="customMenuIcon" title="Options">
                                    <i class="fa-solid fa-ellipsis-vertical"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="headerOptionsWrapper">
                        <div id="toolbarViewerRight">
                            <div class="splitToolbarButton">
                                <button id="zoomOut" class="toolbarButton zoomOut" title="Zoom Out" tabindex="21" data-l10n-id="zoom_out">
                                    <span data-l10n-id="zoom_out_label">Zoom Out</span>
                                </button>
                                <div class="splitToolbarButtonSeparator"></div>
                                <button id="zoomIn" class="toolbarButton zoomIn" title="Zoom In" tabindex="22" data-l10n-id="zoom_in">
                                    <span data-l10n-id="zoom_in_label">Zoom In</span>
                                </button>
                            </div>
                            <span id="scaleSelectContainer" class="dropdownToolbarButton">
                                <select id="scaleSelect" title="Zoom" tabindex="23" data-l10n-id="zoom">
                                    <option id="pageAutoOption" title="" value="auto"  data-l10n-id="page_scale_auto">Automatic Zoom</option>
                                    <option id="pageActualOption" title="" value="page-actual" data-l10n-id="page_scale_actual">Actual Size</option>
                                    <option id="pageFitOption" title="" value="page-fit" data-l10n-id="page_scale_fit">Page Fit</option>
                                    <option id="pageWidthOption" title="" value="page-width" selected="selected" data-l10n-id="page_scale_width">Page Width</option>
                                    <option id="customScaleOption" title="" value="custom" disabled="disabled" hidden="true"></option>
                                    <option title="" value="0.75" data-l10n-id="page_scale_percent" data-l10n-args='{ "scale": 75 }'>75%</option>
                                    <option title="" value="1" data-l10n-id="page_scale_percent" data-l10n-args='{ "scale": 100 }'>100%</option>
                                    <option title="" value="1.25" data-l10n-id="page_scale_percent" data-l10n-args='{ "scale": 125 }'>125%</option>
                                    <option title="" value="1.5" data-l10n-id="page_scale_percent" data-l10n-args='{ "scale": 150 }'>150%</option>
                                    <option title="" value="2" data-l10n-id="page_scale_percent" data-l10n-args='{ "scale": 200 }'>200%</option>
                                    <option title="" value="3" data-l10n-id="page_scale_percent" data-l10n-args='{ "scale": 300 }'>300%</option>
                                    <option title="" value="4" data-l10n-id="page_scale_percent" data-l10n-args='{ "scale": 400 }'>400%</option>
                                </select>
                            </span>
                            <button id="sidebarToggle" class="toolbarButton" title="Toggle Sidebar" tabindex="11" data-l10n-id="toggle_sidebar">
                                <span data-l10n-id="toggle_sidebar_label">Toggle Sidebar</span>
                            </button>
                            <button id="viewFind" class="toolbarButton" title="Find in Document" tabindex="12" data-l10n-id="findbar">
                                <span data-l10n-id="findbar_label">Find</span>
                            </button>
                            <div class="splitToolbarButton hiddenSmallView">
                                <button class="toolbarButton pageUp" title="Previous Page" id="previous" tabindex="13" data-l10n-id="previous">
                                    <span data-l10n-id="previous_label">Previous</span>
                                </button>
                                <div class="splitToolbarButtonSeparator"></div>
                                <button class="toolbarButton pageDown" title="Next Page" id="next" tabindex="14" data-l10n-id="next">
                                    <span data-l10n-id="next_label">Next</span>
                                </button>
                            </div>
                            <input type="number" id="pageNumber" class="toolbarField pageNumber" title="Page" value="1" onchange="_pageChenged(this)" size="4" min="1" tabindex="15" data-l10n-id="page" autocomplete="off">
                            <span id="numPages" class="toolbarLabel"></span>
                            <a href="#" id="viewBookmark" class="toolbarButton bookmark hiddenSmallView" style="display: none" title="Current view (copy or open in new window)" tabindex="35" data-l10n-id="bookmark">
                                <span data-l10n-id="bookmark_label">Current View</span>
                            </a>

                            <div class="verticalToolbarSeparator hiddenSmallView"></div>

                            <button id="secondaryToolbarToggle" class="toolbarButton" title="Tools" tabindex="36" data-l10n-id="tools">
                                <span data-l10n-id="tools_label">Tools</span>
                            </button>
                        </div>
                    </div>
                </div>
                <div id="loadingBar">
                    <div class="progress">
                        <div class="glimmer">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <menu type="context" id="viewerContextMenu">
            <menuitem id="contextFirstPage" label="First Page"
                      data-l10n-id="first_page"></menuitem>
            <menuitem id="contextLastPage" label="Last Page"
                      data-l10n-id="last_page"></menuitem>
            <menuitem id="contextPageRotateCw" label="Rotate Clockwise"
                      data-l10n-id="page_rotate_cw"></menuitem>
            <menuitem id="contextPageRotateCcw" label="Rotate Counter-Clockwise"
                      data-l10n-id="page_rotate_ccw"></menuitem>
        </menu>

        <div id="viewerContainer" tabindex="0" style="margin-top: 0 !important;top: 0 !important;">
            <div id="pdfReaderContent" class="pdfViewer">
                <div id="viewer" class="pdfViewer" style="display: none"></div>
            </div>
        </div>

        <div id="errorWrapper" hidden='true'>
            <div id="errorMessageLeft">
                <span id="errorMessage"></span>
                <button id="errorShowMore" data-l10n-id="error_more_info">
                    More Information
                </button>
                <button id="errorShowLess" data-l10n-id="error_less_info" hidden='true'>
                    Less Information
                </button>
            </div>
            <div id="errorMessageRight">
                <button id="errorClose" data-l10n-id="error_close">
                    Close
                </button>
            </div>
            <div class="clearBoth"></div>
            <textarea id="errorMoreInfo" hidden='true' readonly="readonly"></textarea>
        </div>
    </div>

    <!-- overlayContainer -->
    <div id="overlayContainer"  class="hidden">
        <div id="passwordOverlay" class="container hidden">
            <div class="dialog">
                <div class="row">
                    <p id="passwordText" data-l10n-id="password_label">Enter the password to open this PDF file:</p>
                </div>
                <div class="row">
                    <input type="password" id="password" class="toolbarField">
                </div>
                <div class="buttonRow">
                    <button id="passwordCancel" class="overlayButton"><span data-l10n-id="password_cancel">Cancel</span></button>
                    <button id="passwordSubmit" class="overlayButton"><span data-l10n-id="password_ok">OK</span></button>
                </div>
            </div>
        </div>
        <div id="documentPropertiesOverlay" class="container hidden">
            <div class="dialog">
                <div class="row">
                    <span data-l10n-id="document_properties_file_name">File name:</span> <p id="fileNameField">-</p>
                </div>
                <div class="row">
                    <span data-l10n-id="document_properties_file_size">File size:</span> <p id="fileSizeField">-</p>
                </div>
                <div class="separator"></div>
                <div class="row">
                    <span data-l10n-id="document_properties_title">Title:</span> <p id="titleField">-</p>
                </div>
                <div class="row">
                    <span data-l10n-id="document_properties_author">Author:</span> <p id="authorField">-</p>
                </div>
                <div class="row">
                    <span data-l10n-id="document_properties_subject">Subject:</span> <p id="subjectField">-</p>
                </div>
                <div class="row">
                    <span data-l10n-id="document_properties_keywords">Keywords:</span> <p id="keywordsField">-</p>
                </div>
                <div class="row">
                    <span data-l10n-id="document_properties_creation_date">Creation Date:</span> <p id="creationDateField">-</p>
                </div>
                <div class="row">
                    <span data-l10n-id="document_properties_modification_date">Modification Date:</span> <p id="modificationDateField">-</p>
                </div>
                <div class="row">
                    <span data-l10n-id="document_properties_creator">Creator:</span> <p id="creatorField">-</p>
                </div>
                <div class="separator"></div>
                <div class="row">
                    <span data-l10n-id="document_properties_producer">PDF Producer:</span> <p id="producerField">-</p>
                </div>
                <div class="row">
                    <span data-l10n-id="document_properties_version">PDF Version:</span> <p id="versionField">-</p>
                </div>
                <div class="row">
                    <span data-l10n-id="document_properties_page_count">Page Count:</span> <p id="pageCountField">-</p>
                </div>
                <div class="row">
                    <span data-l10n-id="document_properties_page_size">Page Size:</span> <p id="pageSizeField">-</p>
                </div>
                <div class="separator"></div>
                <div class="row">
                    <span data-l10n-id="document_properties_linearized">Fast Web View:</span> <p id="linearizedField">-</p>
                </div>
                <div class="buttonRow">
                    <button id="documentPropertiesClose" class="overlayButton"><span data-l10n-id="document_properties_close">Close</span></button>
                </div>
            </div>
        </div>
        <div id="printServiceOverlay" class="container hidden">
            <div class="dialog">
                <div class="row">
                    <span data-l10n-id="print_progress_message">Preparing document for printing…</span>
                </div>
                <div class="row">
                    <progress value="0" max="100"></progress>
                    <span data-l10n-id="print_progress_percent" data-l10n-args='{ "progress": 0 }' class="relative-progress">0%</span>
                </div>
                <div class="buttonRow">
                    <button id="printCancel" class="overlayButton"><span data-l10n-id="print_progress_close">Cancel</span></button>
                </div>
            </div>
        </div>
    </div>
    <section class="notesWrapper displayOpenClose">
        <div class="notesDisplay">
            <div class="notesDisplay_header">
                <h3>Highlights</h3>
                <button class="close"><i class="fa-solid fa-xmark"></i></button>
            </div>
            <div class="notesDisplay_body">
                <ul class="notesList"></ul>
            </div>
        </div>
    </section>
</div>
<div class="pdfReaderFooter">
    <div id="previousChapter" style="visibility: hidden"></div>
    <div style="display:flex;flex-direction: column;gap: 10px;">
        <div class="footerPageNumbers">
            <p class="footerPageCounts"></p>
            <span style="margin-left: 5px;margin-right:5px;">●</span>
            <p class="footerPagePercentage"></p>
            <button class="nextResourceBtn" style="display: none;">Next Resource</button>
        </div>
        <div class="range" style="display:none;">
            <input type="range" id="footerProgressSlider" step="1" class="footerProgressSlider">
        </div>
    </div>

    <div id="nextChapter" style="visibility: hidden"></div>
</div>

<div>
    <div id="top-overlay" class="overlay"></div>
    <div id="left-overlay" class="overlay"></div>
    <div id="right-overlay" class="overlay"></div>
    <div id="bottom-overlay" class="overlay"></div>
    <div>
        <div id="selection-box" style="position: fixed; display: none;"></div>
    </div>
    <div class="process-message">
        <p style="color: blue">Processing, please wait.</p>
    </div>
    <div class="mid-overlay"></div>
</div>

<!-- outerContainer -->
%{--<div id="printContainer"></div>--}%
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script>
    var annotation;
    var resIdVal;
    var pdfReaderLibrary = "yes";
    var bookTitle="${params.title}";
    var genericReader=${genericReader};
    var nextChapterIdVal;
    var previousChapterIdVal;
    var annotationColorVal = 'red';
    var fromPreviewPage = "${params.fromPreviewPage}";
    var bookGPT = '${params.bookGPT}'
    let isSnapAvl = false
    let showsnapPdfVal  = false;
    let showMcqVal  = false;
    let mcqQuizId = null;
    let mcqResId = null;

    function createBookSnapsElement(rootElement) {
        var bookSnaps = document.createElement("div");
        bookSnaps.id = "book_snaps";
        bookSnaps.className = "book_snaps";
        bookSnaps.style.display = "none";

        function createElement(tag, attributes, textContent) {
            var element = document.createElement(tag);
            if (attributes) {
                for (var key in attributes) {
                    element.setAttribute(key, attributes[key]);
                }
            }
            if (textContent) {
                element.textContent = textContent;
            }
            return element;
        }

        var summarySnap = createElement("div", { id: "summary_snap", class: "summary_snap", style: "display: none" });
        var snapContent = createElement("div", { class: "snap_content", id: "snap_content" });
        var toggleSnapBtn = createElement("button", { id: "toggleSnapBtn", class: "toggleBtn" }, "Show more");

        summarySnap.appendChild(snapContent);
        summarySnap.appendChild(toggleSnapBtn);

        var qaSnap = createElement("div", { id: "qa_snap", class: "qa_snap", style: "display: none" });
        var snapTitleWrapQA = createElement("div", { class: "snapTitleWrapQA" });
        var qaContent = createElement("div", { class: "qa_content", id: "qa_content" });
        var toggleQABtn = createElement("button", { id: "toggleQABtn", class: "toggleBtn" }, "Show All QA");

        qaSnap.appendChild(snapTitleWrapQA);
        qaSnap.appendChild(qaContent);
        qaSnap.appendChild(toggleQABtn);

        var mcqSnap = createElement("div", { id: "mcq_snap", class: "mcq_snap", style: "display: none" });
        var snapTitleWrapMCQ = createElement("div", { class: "snapTitleWrapMCQ" });
        var mcqContent = createElement("div", { class: "mcq_content", id: "mcq_content" });
        var toggleBtn = createElement("button", { id: "toggleBtn", class: "toggleBtn" }, "Show All MCQs");

        mcqSnap.appendChild(snapTitleWrapMCQ);
        mcqSnap.appendChild(mcqContent);
        mcqSnap.appendChild(toggleBtn);

        var infoRibbon = createElement("div", { class: "info_ribbon" }, "Below given notes are not part of the book you've purchased, this is for your ready reference.");

        bookSnaps.appendChild(summarySnap);
        bookSnaps.appendChild(qaSnap);
        bookSnaps.appendChild(mcqSnap);
        bookSnaps.appendChild(infoRibbon);

        rootElement.insertBefore(bookSnaps, rootElement.firstChild);
    }

    if(bookGPT!='true'){
        document.getElementById('viewer').style.display = "block"
        document.querySelector('.toolbar').removeAttribute('style')
        document.querySelector('.toolbar').setAttribute('style','transform: translateY(0);')
        document.getElementById('viewerContainer').removeAttribute('style')
    }else if(bookGPT=='true' || bookGPT==true){
        $('.lazyLoader').hide();
    }
    <%if(!genericReader){%>
        bookTitle = "${title}";
        document.getElementById('bookTitle').textContent = bookTitle;
        document.querySelector('.bookTitle').style.display = 'flex';
    <%}else {%>
        if(fromPreviewPage!='true' && fromPreviewPage!=true){
            document.querySelector('.chapterListWrapper').style.display = 'flex';
            document.getElementById('nextChapter').style.display = 'none';
            document.getElementById('previousChapter').style.display = 'none';
        }
    <%}%>

    var mobileView="${params.mobileView}";
    if(mobileView==true || mobileView=="true"){
        document.querySelector(".headerOptionsWrapper").classList.add('headerOptionsWrapperClose');
    }

    if(document.querySelector('.backBtn')){
        document.querySelector('.backBtn').addEventListener('click',function (){
            if(genericReader) {
                history.back();
            }
            else {
                parentElementOBJ.closePDFReaderView();
            }
        })
    }
    if(fromPreviewPage=='true' || fromPreviewPage==true){
        document.querySelector('.backButton').style.display = 'none';
    }
    Annotator.Plugin.StoreLogger = function (element) {
        return {
            pluginInit: function () {
                this.annotator
                    .subscribe("annotationCreated", function (annotation) {
                    })
                    .subscribe("annotationUpdated", function (annotation) {
                    })
                    .subscribe("annotationDeleted", function (annotation) {
                    });
            }
        }
    };

    var serverPath = "${serverURL}";
    var bookLang="${params.bookLang}";
    var loggedInUser = "${params.loggedInUser}";


    function renderedNewHighlightedText(data) {

    }

    function renderCreatedNotes(data) {

    }

    function loadAnnotator(resId,data) {
        var url = $('<textarea/>').html("${params.bookUrl}").text();
        displayPdfWithHighlight(url.replaceAll("__","&"),data);
        var bookId=0,resId=0;
        if("${params.bookId}" != "") bookId=${params.bookId};
        resId=${params.resId};
        if(annotation !== undefined) {
            annotation.annotator('destroy');
        }

        annotation = $('#pdfReaderContent').annotator();
        resIdVal = resId;

        annotation.annotator('addPlugin', 'Store', {
            // The endpoint of the store on your server.
            prefix: serverPath+'/wonderpublish',

            // Attach the uri of the current page to all annotations to allow search.
            annotationData: {
                'uri': resId,
                'bookId' : bookId
            },

            urls: {
                // These are the default URLs.
                create:  '/annotateSave',
                update:  '/annotateUpdate/:id',
                destroy: '/annotateDestroy/:id'
            },

            // This will perform a "search" action when the plugin loads. Will
            // request the last 20 annotations for the current url.
            // eg. /store/endpoint/search?limit=20&uri=http://this/document/only
            loadFromSearch: {
                'limit': 100,
                'all_fields': 1,
                'uri': resId,
                'bookId':bookId
            },

            showViewPermissionsCheckbox: true,
            showEditPermissionsCheckbox: true
        });

        annotation.annotator('addPlugin', 'Tags');
        annotation.annotator('addPlugin', 'StoreLogger');
        annotation.annotator().annotator("addPlugin", "Touch");
    }

    function getAnnotatedData(){
        var resId=${params.resId};
        var bookId=${params.bookId};
         // console.log(window.frames.length + ':' + parent.frames.length);
         if(parent.frames.length > 0) {
            <g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess="loadAnnotator(resId,data)" params="'limit=100&all_fields=1&uri='+resId+'&bookId='+bookId" />
         }
    }

    function dummyAnnotatorData(){
        if(parent.frames.length > 0) {
            var resId=${params.resId};
            var bookId=${params.bookId};
            <g:remoteFunction controller="wonderpublish" action="dummyAnnotatorData"  onSuccess="loadAnnotator(resId,data)" params="'limit=100&all_fields=1&uri='+resId+'&bookId='+bookId" />
        }
    }

    var appType='${params.appType}';
    var userDetails = "${session.getAttribute("userdetails")}";
    var knimbusInstUser = '${params.knimbusInstUser}'
    if(appType !=''){
        if(bookLang != "" || bookLang == null || bookLang == "null" || bookLang == "") getAnnotatedData();
    }else {
        if ((bookLang != " " || bookLang == null || bookLang == "null" || bookLang == "") && userDetails != "" && userDetails != null && userDetails != undefined && knimbusInstUser=="false") getAnnotatedData();
        else loadAnnotator('${params.resId}',[]);
    }

    function openPdf() {
        var resId =${params.resId};
        var bookId =${params.bookId};
        if (parent.frames.length > 0) {
            var url = $('<textarea/>').html("${params.bookUrl}").text();
            //var url = "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf";

            displayPdfWithHighlight(url.replaceAll("__", "&"), []);
            var bookId = 0, resId = 0;
            if ("${params.bookId}" != "") bookId =${params.bookId};
            resId =${params.resId};

            resIdVal = resId;

        }
    }

    document.body.oncontextmenu = function() {return false;}

    function _pageChenged(field) {
        console.log(field);
    }
    $(document).ready(function(){
        $('[data-toggle="popover"]').popover();
    });

    // $('form input').on('keyup keypress', function(e) {
    //     var keyCode = e.keyCode || e.which;
    //     if (keyCode === 13) {
    //         alert();
    //         e.preventDefault();
    //         return false;
    //     }
    // });
</script>
<script>
    let pinchZoomEnabled = false;
    function enablePinchZoom(pdfViewer) {
        let startX = 0,
            startY = 0;
        let initialPinchDistance = 0;
        let pinchScale = 1;
        const viewer = document.getElementById("viewer");
        const container = document.getElementById("viewerContainer");
        const reset = () => {
            startX = startY = initialPinchDistance = 0;
            pinchScale = 1;
        };
        document.addEventListener("touchstart", (e) => {
            if (e.touches.length > 1) {
                startX = (e.touches[0].pageX + e.touches[1].pageX) / 2;
                startY = (e.touches[0].pageY + e.touches[1].pageY) / 2;
                initialPinchDistance = Math.hypot(
                    e.touches[1].pageX - e.touches[0].pageX,
                    e.touches[1].pageY - e.touches[0].pageY
                );
            } else {
                initialPinchDistance = 0;
            }
        });
        document.addEventListener(
            "touchmove",
            (e) => {
                if (initialPinchDistance <= 0 || e.touches.length < 2) {
                    return;
                }
                if (e.scale !== 1) {
                    e.preventDefault();
                }
                const pinchDistance = Math.hypot(
                    e.touches[1].pageX - e.touches[0].pageX,
                    e.touches[1].pageY - e.touches[0].pageY
                );
                const originX = startX + container.scrollLeft;
                const originY = startY + container.scrollTop;
                pinchScale = pinchDistance / initialPinchDistance;
                viewer.style.transform = `scale(${pinchScale})`;
                viewer.style.transformOrigin = `${originX}px ${originY}px`;
            },
            { passive: false }
        );
        document.addEventListener("touchend", (e) => {
            if (initialPinchDistance <= 0) {
                return;
            }
            if (pinchScale>0.9){
                viewer.style.transform = `none`;
                viewer.style.transformOrigin = `unset`;
                PDFViewerApplication.pdfViewer.currentScale *= pinchScale;
                const rect = container.getBoundingClientRect();
                const dx = startX - rect.left;
                const dy = startY - rect.top;
                container.scrollLeft += dx * (pinchScale - 1);
                container.scrollTop += dy * (pinchScale - 1);
            }else{
                pinchScale = 0.5;
                viewer.style.transform = `none`;
                viewer.style.transformOrigin = `unset`;
                PDFViewerApplication.pdfViewer.currentScale = pinchScale;
                const rect = container.getBoundingClientRect();
                const dx = startX - rect.left;
                const dy = startY - rect.top;
                container.scrollLeft += dx * (pinchScale - 1);
                container.scrollTop += dy * (pinchScale - 1);
            }
            reset();
        });
    }
    document.addEventListener("DOMContentLoaded", () => {
        if (!pinchZoomEnabled) {
            pinchZoomEnabled = true;
            enablePinchZoom();
        }
    });


    document.addEventListener("pagesloaded", function(e) {
        console.log("pages laoded")
    });

    var notesWrapper = document.querySelector('.notesWrapper');

    function openCloseNotesWindow(){
        notesWrapper.classList.toggle('openPopupWindow');
        notesWrapper.classList.toggle('displayOpenClose');
        if (notesWrapper.classList.contains('openPopupWindow')){
            document.querySelector('.pdfReaderFooter').style.display='none';
            document.querySelector('.toolbar').style.transform = 'translateY(-100%)';
            document.querySelector('#viewerContainer').setAttribute('style','top:0px !important');
            notesWrapper.addEventListener('click',function (){
                openCloseNotesWindow();
            })
        }else{
            document.querySelector('.pdfReaderFooter').style.display='flex';
            document.querySelector('.toolbar').style.transform = 'translateY(0)';
            document.querySelector('#viewerContainer').removeAttribute('style');
        }
    }

    function viewChapterNotes() {
        var chapterId="${params.chapterId}";
        var rId = ${params.resId};
        var bId = ${params.bookId};
        <g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess='renderChapterNotesPDF(data);' params="'limit=100&all_fields=1&uri='+rId+'&bookId='+bId" />
    }

    function renderChapterNotesPDF(data){
        var notesData = data.rows;
        var notesHTML = "";

        if (notesData.length>0){
            for (var i=0;i<notesData.length;i++){
                if (notesData[i].text==null){
                    notesHTML+= "<li onclick='gotoHighlight("+notesData[i].ranges.pageNumber+")'>" +
                                    "<p>Highlight <span>•</span> Page "+notesData[i].ranges.pageNumber+"</p>"+
                                    "<a href='javascript:gotoHighlight("+notesData[i].ranges.pageNumber+")'>"+(i+1)+". "+notesData[i].quote+"</a>" +
                                "</li>";
                }else{
                    notesHTML+= "<li onclick='gotoHighlight("+notesData[i].ranges.pageNumber+")'>" +
                                    "<p>Note <span>•</span> Page "+notesData[i].ranges.pageNumber+"</p>"+
                                    "<a href='javascript:gotoHighlight("+notesData[i].ranges.pageNumber+")'>"+(i+1)+". "+notesData[i].quote+"</a>" +
                                    "<p style='margin-left: 15px;margin-top: 10px;word-break: break-all;font-size: 15px'>"+notesData[i].text+"</p>"+
                                "</li>";
                }
            }
        } else{
            notesHTML+="No Highlights found."
        }
        document.querySelector('.notesList').innerHTML = notesHTML;
    }

    if(!(userDetails != "" && userDetails != null && userDetails != undefined && knimbusInstUser=="false")){
        document.getElementById('highlightsAndNotes').style.display="none"
    }
    if(document.getElementById('highlightsAndNotes')){
        document.getElementById('highlightsAndNotes').addEventListener('click',function (){
            if (parentElementOBJ.loggedInUser){
                openCloseNotesWindow();
                viewChapterNotes();
            }else{
                parentElementOBJ.loginOpen();
            }
        });
    }


    document.querySelector('.close').addEventListener('click',function (){
        openCloseNotesWindow()
    });

    document.querySelector('.backBtn').addEventListener('click',function (){
        var lastReadDetailsArr = [];
        var oldLastReadDetails = localStorage.getItem('lastReadDetails');
        var lastReadDetailsObj = {
            resId:resIdVal,
            pageNo:footerCurPage
        }

        //localStorage.setItem('lastReadPDF',JSON.stringify({resId:resIdVal,pdfOpen:false,chapterId:"${params.chapterId}"}))
        if (oldLastReadDetails){
            oldLastReadDetails =  JSON.parse(oldLastReadDetails);
            oldLastReadDetails.map(item=>{
                if (item.resId == resIdVal){
                    item.pageNo = footerCurPage;
                }else{
                    oldLastReadDetails.push(lastReadDetailsObj)
                }
            });
            oldLastReadDetails = Array.from(new Set(oldLastReadDetails.map(JSON.stringify))).map(JSON.parse);
            localStorage.setItem('lastReadDetails',JSON.stringify(oldLastReadDetails));
        }else{
            lastReadDetailsArr.push(lastReadDetailsObj)
            localStorage.setItem('lastReadDetails',JSON.stringify(lastReadDetailsArr));
        }
    })

    document.querySelector('.customMenuIcon').addEventListener('click',function (){
       document.querySelector('.headerOptionsWrapper').classList.toggle('headerOptionsWrapperClose');
       document.querySelector('.headerOptionsWrapper').classList.toggle('headerOptionsWrapperAnimation');
    });

    document.getElementById('viewer').addEventListener('mousemove',function(e) {
        if (e.y<=100){
            document.querySelector('.toolbar').style.transform = 'translateY(0)';
            document.querySelector('#viewerContainer').removeAttribute('style');
        }
    })

    var loaderSlogans = ["Expand your mind, not your shelves: switch to ebooks.",
        "Ebooks: countless stories at your fingertips.",
        "Ebooks: the future of reading, today.",
        "Take your library with you: read ebooks!",
        "Carry a library in your pocket with ebooks.",
        "Get lost in a good book, anytime, anywhere with ebooks.",
    ];

    sloganInterval = setInterval(function (){
        document.getElementById('sloganTexts').innerHTML = "<p>'"+loaderSlogans[Math.round(Math.random()*5)]+"'</p>";
    },2500);

    setTimeout(function (){
        $('#loadAnim-2').show();
        $('#loadAnim-1').hide();
    },2500);

    function openLoginModalWindow(){
        parentElementOBJ.loginOpen();
    }


    function getNextElementValue(selectElement) {
        const selectedIndex = selectElement.selectedIndex;
        const nextIndex = selectedIndex + 1;

        if (nextIndex < selectElement.options.length) {
            return selectElement.options[nextIndex].value;
        }

        return null; // Next element does not exist
    }

    function getPreviousElementValue(selectElement) {
        const selectedIndex = selectElement.selectedIndex;
        const previousIndex = selectedIndex - 1;

        if (previousIndex >= 0) {
            return selectElement.options[previousIndex].value;
        }

        return null; // Previous element does not exist
    }

    function displayNextAndPreviousValues(selectElement) {
        const selectedIndex = selectElement.selectedIndex;

        // Get the value of the next element
        const nextValue = getNextElementValue(selectElement);
        if (nextValue !== null) {
            document.getElementById("nextChapter").style.visibility = 'visible';
            document.getElementById("nextChapter").style.display = 'flex';
            document.getElementById("nextChapter").innerHTML="<a href='javascript:window.parent.getHtmlsDataByChapter("+nextValue+");' title='Next Chapter'>Next Chapter</a>";
            nextChapterIdVal=nextValue;
        } else {
            document.getElementById("nextChapter").innerHTML="";
        }

        // Get the value of the previous element
        const previousValue = getPreviousElementValue(selectElement);
        if (previousValue !== null) {
            document.getElementById("previousChapter").style.visibility = 'visible';
            document.getElementById("previousChapter").style.display = 'flex';
            document.getElementById("previousChapter").innerHTML="<a href='javascript:window.parent.getHtmlsDataByChapter("+previousValue+");' title='Previous Chapter'>Previous Chapter</a>";
            previousChapterIdVal=previousValue;
        } else {
            document.getElementById("previousChapter").innerHTML="";
        }

    }

    if(window.parent.previewMode || !genericReader){
        document.getElementById("chaptersListDisplay").innerHTML="";
    }else{
        const mySelectElement = document.getElementById("chapterId");
        displayNextAndPreviousValues(mySelectElement);
    }

    var notesDisplayText =  $('.notesDisplay');
    notesDisplayText.bind('cut copy paste', function(e) {
        e.preventDefault();
    });

    function enableOnswipe(){
        // Get the PDF viewer container element
        const pdfViewerContainer = document.getElementById('viewer');

        let touchStartX = 0;
        let touchStartY = 0;
        let touchEndX = 0;
        let touchEndY = 0;

        // Add touch event listeners to detect swipes
        pdfViewerContainer.addEventListener('touchstart', (event) => {
            touchStartX = event.touches[0].clientX;
            touchStartY = event.touches[0].clientY;
        });

        pdfViewerContainer.addEventListener('touchmove', (event) => {
            touchEndX = event.touches[0].clientX;
            touchEndY = event.touches[0].clientY;
        });

        pdfViewerContainer.addEventListener('touchend', handleSwipe);

        // Function to handle the swipe
        function handleSwipe() {
            const horizontalSwipeDistance = touchEndX - touchStartX;
            const verticalSwipeDistance = touchEndY - touchStartY;

            const swipeThreshold = 80;

            if (Math.abs(horizontalSwipeDistance) > Math.abs(verticalSwipeDistance)) {
                if (horizontalSwipeDistance > swipeThreshold) {
                    leftSwipeHandler();
                } else if (horizontalSwipeDistance < -swipeThreshold) {
                    rightSwipeHandler();
                }
            }
        }

        // Your swipe left handler function
        function leftSwipeHandler() {
            if (previousChapterIdVal!=null && previousChapterIdVal!=undefined){
                window.parent.getHtmlsDataByChapter(previousChapterIdVal);
            }

        }

        // Your swipe right handler function
        function rightSwipeHandler() {
            if (nextChapterIdVal!=null && nextChapterIdVal!=undefined){
                window.parent.getHtmlsDataByChapter(nextChapterIdVal);
            }

        }
    }

    if (genericReader){
        enableOnswipe();
    }

    document.addEventListener('keydown', event => {
        if (event.ctrlKey || event.metaKey && event.key === 's') {
            event.preventDefault();
            event.stopPropagation();
            return false;
        }
    },false);
    document.addEventListener('keydown', event => {
        if (event.ctrlKey || event.metaKey && event.key === 'c') {
            event.preventDefault();
            event.stopPropagation();
            return false;
        }
    },false);

    <%if("1".equals(""+session["siteId"]) || "3".equals(""+session["siteId"]) || "37".equals(""+session["siteId"]) || "71".equals(""+session["siteId"])){%>
    if ('matchMedia' in window) {
        // Chrome, Firefox, and IE 10 support mediaMatch listeners
        window.matchMedia('print').addListener(function(media) {
            console.log("pringgg",media);
            if (media.matches) {
                beforeBookPrint();
            } else {
                // Fires immediately, so wait for the first mouse movement
                $(document).one('mouseover', afterBookPrint);
            }
        });
    } else {
        // IE and Firefox fire before/after events
        $(window).on('beforeprint', beforeBookPrint);
        $(window).on('afterprint', afterBookPrint);
    }
    function beforeBookPrint() {
        $("html,body").hide();
    }
    function afterBookPrint() {
        $("html,body").show();
    }
    <%}%>
</script>

<script>
    document.addEventListener("DOMContentLoaded", async () => {
        if(bookGPT == "true"){
            const { showSnapshot,showMcq,showQa, showsnapPdf} = window.parent.snapStateChecker()
            showsnapPdfVal = showsnapPdf
            showMcqVal = showMcq
            if(showSnapshot=="true" || showMcq=="true" || showQa=="true"){
                isSnapAvl = true
            }
            if(showsnapPdf=="true" || showsnapPdf==""){
                document.getElementById('viewer').style.display = "block"
                document.querySelector('.pdfReaderFooter').style.display='flex';
            }else{
                document.getElementById('viewer').style.display = "none"
                document.querySelector('.pdfReaderFooter').style.display='none';
            }
            if((showsnapPdfVal!="true" || showsnapPdfVal==null) && showMcqVal=="true"){
                createBookSnapsElement(document.getElementById("viewerContainer"));
            }else{
                createBookSnapsElement(document.getElementById("pdfReaderContent"));
            }
            const book_snaps = document.getElementById('book_snaps')
            const summary_snap = document.getElementById('summary_snap')
            const snap_content = document.getElementById('snap_content')
            const mcq_snap = document.getElementById('mcq_snap')
            const qa_snap = document.getElementById('qa_snap')
            const mcq_content = document.getElementById('mcq_content')
            const qa_content = document.getElementById('qa_content')
            const toggleBtn = document.getElementById('toggleBtn')
            const toggleQABtn = document.getElementById('toggleQABtn')
            const toggleSnapBtn = document.getElementById('toggleSnapBtn')
            const snapTitleWrapMCQ = document.querySelector('.snapTitleWrapMCQ')
            const snapTitleWrapQA = document.querySelector('.snapTitleWrapQA')
            const res_tempId = "${params.resId}"
            let showAllMCQs = false;
            let showAllQA = false;
            const initialVisibleQuestions = 1;
            let maxLength = 800;
            let showFullText = false;
            let fullScreen = null;
            let panelLeftDiv = null;
            let selectedLanguageGlobal = 0;
            let languageVal = "English"
            if(mobileView=="true"){
                maxLength  = 400
            }
            const getGPTInfo = async (res_tempId)=>{
                const response = await fetch("/prompt/createTest?readingMaterialResId="+res_tempId)
                if(!response.ok){
                    return
                }
                return await response.json()
            }

            const getSnapshot = async (res_tempId)=>{
                const response = await fetch("/prompt/getGPTsForResource?resId="+res_tempId+"&promptType=chapter_snapshot")
                if(!response.ok){
                    return
                }
                return await response.json()
            }
            let snapdata;
            let data;
            if(showSnapshot=="true"){
                snapdata = await getSnapshot(res_tempId)
                if(snapdata.hasResource){
                    renderSnapshot(snapdata)
                }
            }

            if(showMcq=="true" || showQa=="true"){
                data = await getGPTInfo(res_tempId)
                mcqQuizId = data.mcqQuizId
                mcqResId = data.mcqResId
                if(showMcq=="true"){
                    if(data.mcqs.length>0){
                        if((showsnapPdfVal!="true" || showsnapPdfVal==null) && showMcqVal=="true"){
                            toggleBtn.style.display = 'none';
                            renderMCQsQuestions(data.mcqs, data.mcqLanguages);
                        }else{
                            renderMCQsQuestions(data.mcqs.slice(0, initialVisibleQuestions), data.mcqLanguages);
                        }
                    }

                }

                if(showQa=="true"){
                    if(data.qna.length>0){
                        renderQAQuestions(data.qna.slice(0, initialVisibleQuestions), data.qnaLanguages);
                    }
                }
            }

            function renderSnapshot(data){
                let parsedContent = data.answer;
                if(parsedContent.length<=800 && mobileView!="true"){
                    toggleSnapBtn.style.display = 'none'
                }
                let snapHTML = ''
                snapHTML+= "<div class='snap_content_div' id='snap_content_div'></div>"
                snap_content.innerHTML = snapHTML;

                const ansDiv = document.getElementById("snap_content_div")
                if (!showFullText) {
                    parsedContent = parsedContent.substring(0, maxLength) + '...';
                    toggleSnapBtn.innerText = 'Show More';
                }else{
                    toggleSnapBtn.innerText = 'Show Less';
                }
                ansDiv.innerHTML = parsedContent

                renderMathInElement(ansDiv, {
                    delimiters: [
                        { left: "\\(", right: "\\)", display: false },
                        { left: "\\[", right: "\\]", display: true }
                    ]
                });
                ansDiv.innerHTML = marked.parse(ansDiv.innerHTML)
                document.querySelectorAll('pre code').forEach((block) => {
                    hljs.highlightBlock(block);
                });
                summary_snap.style.display =  "block";
            }

            function renderMCQsQuestions(questionsToShow, languages) {
                let questions = questionsToShow
                const siteName="${session['siteName']}";
                let languagesList = languages ? [...new Set([languages.language1 !=null ? languages.language1 : null, languages.language2 !=null ? languages.language2 : null])] : []
                languagesList = languagesList.filter(item => item != null);
                snapTitleWrapMCQ.innerHTML = constructSnapTitle("Multiple Choice Questions", data.mcqs.length, languagesList, "mcqLanguages")
                if((showsnapPdf!="true" || showsnapPdf==null) && showMcq=="true"){
                    var html_ts = '<div>' +
                        '<a href="/prepjoy/prepJoyGame?quizId='+mcqQuizId+'&resId='+mcqResId+'&quizType=&source=web&siteName='+siteName+'&learn=false&pubDesk=false&dailyTest=false&fromgpt=true&readId='+resIdVal+'" target="_blank" class="ts_opt_link">Play</a>' +
                        '<span class="ts_opt_separator">|</span>' +
                        '<a href="/prepjoy/prepJoyGame?quizId='+mcqQuizId+'&resId='+mcqResId+'&quizType=practice&source=web&siteName='+siteName+'&learn=false&pubDesk=false&dailyTest=false&fromgpt=true&readId='+resIdVal+'" target="_blank" class="ts_opt_link">Practice</a>' +
                        '<span class="ts_opt_separator">|</span>' +
                        '<a href="/prepjoy/prepJoyGame?quizId='+mcqQuizId+'&resId='+mcqResId+'&quizType=testSeries&source=web&siteName='+siteName+'&learn=false&pubDesk=false&dailyTest=false&fromgpt=true&readId='+resIdVal+'" target="_blank" class="ts_opt_link">Test</a>' +
                        '</div>';
                    document.getElementById("ts_opt_mcqLanguages").innerHTML+= html_ts
                }
                mcq_content.innerHTML += "<div id='mcqContentRenderer'></div>";
                let selectedLanguage = 0
                if(languagesList.length>1){
                    const selectElement = document.getElementById("mcqLanguages")
                    languagesList.forEach((option, index)=>{
                        const optionElement = document.createElement("option")
                        optionElement.value = index
                        optionElement.textContent = option
                        selectElement.appendChild(optionElement)
                    })
                    languageVal = selectElement.options[selectElement.selectedIndex].text;
                    selectElement.addEventListener("change", (event) => {
                        const selectedValue = event.target.value;
                        const selectedText = event.target.options[event.target.selectedIndex].text;
                        languageVal = selectedText
                        selectedLanguage = selectedValue
                        selectedLanguageGlobal = selectedLanguage
                        showAllMCQs ? questions = data.mcqs : questions
                        constructMCQHandler(questions, selectedLanguage)
                    });
                }
                constructMCQHandler(questions, selectedLanguage)
            }

            function renderQAQuestions(questionsToShow, languages) {
                let questions = questionsToShow

                let languagesList = languages ? [...new Set([languages.language1 !=null ? languages.language1 : null, languages.language2 !=null ? languages.language2 : null])] : []

                languagesList = languagesList.filter(item => item != null);
                snapTitleWrapQA.innerHTML = constructSnapTitle("Question and Answers ", data.qna.length, languagesList, "qnaLanguages")
                qa_content.innerHTML += "<div id='qnaContentRenderer'></div>";
                let selectedLanguage = 0
                if(languagesList.length>1){
                    const selectElement = document.getElementById("qnaLanguages")
                    languagesList.forEach((option, index)=>{
                        const optionElement = document.createElement("option")
                        optionElement.value = index
                        optionElement.textContent = option
                        selectElement.appendChild(optionElement)
                    })

                    selectElement.addEventListener("change", (event) => {
                        const selectedValue = event.target.value;
                        const selectedText = event.target.options[event.target.selectedIndex].text;
                        selectedLanguage = selectedValue
                        showAllQA ? questions = data.qna : questions
                        constructQAHandler(questions, selectedLanguage)
                    });
                }

                constructQAHandler(questions, selectedLanguage)
            }

            if(showSnapshot=="true" || showMcq=="true" || showQa=="true"){
                book_snaps.style.display = 'block';
            }

            if((showsnapPdf!="true" || showsnapPdf==null) && showMcq=="true"){
                document.querySelector('.info_ribbon').style.display = "none"
            }

            function toggleMCQs() {
                if(data){
                    showAllMCQs = !showAllMCQs;
                    if (showAllMCQs) {
                        const selectedLanguage = document.getElementById('mcqLanguages')?.value || 0
                        constructMCQHandler(data.mcqs, selectedLanguage)
                        toggleBtn.innerText = 'Show All MCQs';
                        toggleBtn.style.display = 'none';
                    }
                }
            }

            function toggleQA() {
                if(data){
                    showAllQA = !showAllQA;
                    if (showAllQA) {
                        const selectedLanguage = document.getElementById('qnaLanguages')?.value || 0
                        constructQAHandler(data.qna, selectedLanguage)
                        toggleQABtn.innerText = 'Show All QA';
                        toggleQABtn.style.display = 'none';
                    }
                }
            }

            function toggleSnap() {
                if(snapdata){
                    showFullText = !showFullText;
                    if(showFullText){
                        toggleSnapBtn.style.display = 'none'
                        renderSnapshot(snapdata)
                    }
                }
            }
            function extractText(text, languageIndex) {
                if(text==null){
                    return ""
                }
                const parts = text.split('~~');

                const firstPart = parts[0].trim();
                const secondPart = parts[1] ? parts[1].trim() : '';
                return languageIndex == 0 ? firstPart : secondPart;
            }

            function constructMCQHandler(questions, language){
                let qCount = 0;
                let questionHTML = "";
                for(var q=0;q<questions.length;q++){
                    qCount++
                    questionHTML+= "<div class='queWrap'>"
                    questionHTML+= "<p class='ques'>Q"+qCount+". "+extractText(questions[q].question, parseInt(language))+"</p>"
                    questionHTML+= "<p class='optNo'>A. "+extractText(questions[q].option1, parseInt(language))+"</p>"
                    questionHTML+= "<p class='optNo'>B. "+extractText(questions[q].option2, parseInt(language))+"</p>"
                    questionHTML+= "<p class='optNo'>C. "+extractText(questions[q].option3, parseInt(language))+"</p>"
                    questionHTML+= "<p class='optNo'>D. "+extractText(questions[q].option4, parseInt(language))+"</p>"
                    if(questions[q].option5){
                        questionHTML+= "<p class='optNo'>E. "+extractText(questions[q].option5, parseInt(language))+"</p>"
                    }

                    if(questions[q].answer1 && questions[q].answer1 === "Yes"){
                        questionHTML+= "<p class='answerwrapItem'><span class='ansTxt'>Answer:</span><strong style='font-weight: bold !important;'>(A) </strong> "+extractText(questions[q].option1, parseInt(language))+"</p>"
                    }else if(questions[q].answer2 && questions[q].answer2 === "Yes"){
                        questionHTML+= "<p class='answerwrapItem'>Answer: <strong style='font-weight: bold !important;'> (B) </strong>"+extractText(questions[q].option2, parseInt(language))+"</p>"
                    }else if(questions[q].answer3 && questions[q].answer3 === "Yes"){
                        questionHTML+= "<p class='answerwrapItem'>Answer: <strong style='font-weight: bold !important;'> (C) </strong>"+extractText(questions[q].option3, parseInt(language))+"</p>"
                    }else if(questions[q].answer4 && questions[q].answer4 === "Yes"){
                        questionHTML+= "<p class='answerwrapItem'>Answer: <strong style='font-weight: bold !important;'> (D) </strong>"+extractText(questions[q].option4, parseInt(language))+"</p>"
                    }else if(questions[q].answer5 && questions[q].answer5 === "Yes"){
                        questionHTML+= "<p class='answerwrapItem'>Answer: <strong style='font-weight: bold !important;'> (E) </strong>"+extractText(questions[q].option5, parseInt(language))+"</p>"
                    }

                    if(questions[q].difficultyLevel){
                        questionHTML+= "<p><span class='ansTxt'>Difficulty:</span> "+questions[q].difficultyLevel+"</p>";
                    }

                    if(questions[q].answerDescription){
                        questionHTML+= "<p class='expwrapItem'><span class='ansTxt'>Explanation:</span> "+extractText(questions[q].answerDescription, parseInt(language))+"</p>";
                    }

                    if((showsnapPdf!="true" || showsnapPdf==null) && showMcq=="true"){
                        questionHTML += constructActionButtons(q)
                    }
                    questionHTML+= "</div>"
                }

                const tempEl = document.createElement("div")
                tempEl.innerHTML = questionHTML
                renderMathInElement(tempEl, {
                    delimiters: [
                        { left: "\\(", right: "\\)", display: false },
                        { left: "\\[", right: "\\]", display: true }
                    ]
                });

                questionHTML = marked.parse(tempEl.innerHTML);
                document.getElementById("mcqContentRenderer").innerHTML = questionHTML
                if((showsnapPdf!="true" || showsnapPdf==null) && showMcq=="true"){
                    attachEventListeners()
                    book_snaps.style.fontFamily = "font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;"
                }
                mcq_snap.style.display = 'block'
            }

            function constructQAHandler(questions, language){
                let questionHTML = ""
                let qCount = 0
                for(var q=0;q<questions.length;q++){
                    qCount++
                    questionHTML+= "<div class='queWrap'>"
                    questionHTML+= "<p class='ques'>Q"+qCount+". "+extractText(questions[q].question, parseInt(language))+"</p>"
                    questionHTML+= "<p><span class='ansTxt'>Answer:</span> "+extractText(questions[q].answer, parseInt(language))+"</p>"
                    questionHTML+= "<p><span class='ansTxt'>Difficulty:</span> "+questions[q].difficultyLevel+"</p>"
                    questionHTML+= "</div>"
                }
                const tempEl = document.createElement("div")
                tempEl.innerHTML = questionHTML
                renderMathInElement(tempEl, {
                    delimiters: [
                        { left: "\\(", right: "\\)", display: false },
                        { left: "\\[", right: "\\]", display: true }
                    ]
                });
                questionHTML = marked.parse( tempEl.innerHTML);
                document.getElementById("qnaContentRenderer").innerHTML = questionHTML
                qa_snap.style.display = 'block'
            }
            function constructSnapTitle(title, totalLength, languagesList, id){
                let headerMCQHTML = '';
                headerMCQHTML+= "<h4 class='snapTitle'>" + title+ "("+totalLength+")</h4>"+
                                "<div class='ts_opt_cont' id='ts_opt_"+id+"'>";
                                    if(languagesList.length>1){
                                        headerMCQHTML+="<select class='languagedropdown' id='"+id+"'></select>";
                                    }
                headerMCQHTML+= "</div>";
                return headerMCQHTML
            }

            function constructActionButtons(ques_index){
                var buttonGroup = '<div class="button-group">' +
                                    '<button class="btn btn-primary giveHint" id="hint_'+ques_index+'" data-index="hint_'+ques_index+'">' +
                                        '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">' +
                                            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>' +
                                        '</svg>' +
                                        'Give Hint' +
                                    '</button>' +

                                    '<button class="btn btn-secondary explainMCQ" id="explain_'+ques_index+'" data-index="explain_'+ques_index+'">' +
                                        '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">' +
                                            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>' +
                                        '</svg>' +
                                        'Explain MCQ' +
                                    '</button>' +

                                    '<button class="btn btn-tertiary similarMCQ" id="similar_'+ques_index+'" data-index="similar_'+ques_index+'">' +
                                        '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">' +
                                            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>' +
                                        '</svg>' +
                                        'Create similar MCQs' +
                                    '</button>' +
                                    '<button class="btn btn-tertiary showHistory" id="history_'+ques_index+'" data-index="history_'+ques_index+'">'+
                                        '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">'+
                                            '<path strokelinecap="round" strokelinejoin="round" strokewidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>'+
                                        '</svg>'+
                                        'Show History'+
                                    '</button>'+
                            '</div>';

                return buttonGroup;
            }

            function attachEventListeners() {
                document.querySelectorAll('.giveHint').forEach(function (button) {
                    button.addEventListener('click', function () {
                        var index = this.dataset.index;
                        const actionType = index.split("_")[0]
                        const actionId = index.split("_")[1]
                        const actionObj = data.mcqs[parseInt(actionId)]
                        //console print all input parameters to the console with name and value
                        console.log("actionType: " + actionType)
                        console.log("actionId: " + actionId)
                        console.log("actionObj: " + actionObj)
                        console.log("selectedLanguageGlobal: " + selectedLanguageGlobal)
                        console.log("languageVal: " + languageVal)


                        window.parent.showChatWindowCB(actionObj, actionType, actionId,selectedLanguageGlobal, languageVal)
                    });
                });

                document.querySelectorAll('.explainMCQ').forEach(function (button) {
                    button.addEventListener('click', function () {
                        var index = this.dataset.index;
                        const actionType = index.split("_")[0]
                        const actionId = index.split("_")[1]
                        const actionObj = data.mcqs[parseInt(actionId)]
                        window.parent.showChatWindowCB(actionObj, actionType, actionId, selectedLanguageGlobal, languageVal)
                    });
                });

                document.querySelectorAll('.similarMCQ').forEach(function (button) {
                    button.addEventListener('click', function () {
                        var index = this.dataset.index;
                        const actionType = index.split("_")[0]
                        const actionId = index.split("_")[1]
                        const actionObj = data.mcqs[parseInt(actionId)]
                        window.parent.showChatWindowCB(actionObj, actionType, actionId, selectedLanguageGlobal, languageVal)
                    });
                });

                document.querySelectorAll('.showHistory').forEach(function (button) {
                    button.addEventListener('click', function () {
                        var index = this.dataset.index;
                        const actionType = index.split("_")[0]
                        const actionId = index.split("_")[1]
                        const actionObj = data.mcqs[parseInt(actionId)]
                        window.parent.showChatWindowCB(actionObj, actionType, actionId, selectedLanguageGlobal, languageVal)
                    });
                });
            }

            toggleBtn.addEventListener('click',toggleMCQs)
            toggleQABtn.addEventListener('click',toggleQA)
            toggleSnapBtn.addEventListener('click',toggleSnap)
        }else{
            showsnapPdfVal = "${showPdf}"
        }
    });
</script>

<script>
    const selectionBox = document.getElementById('selection-box')
    const topOverlay = document.getElementById('top-overlay')
    const leftOverlay = document.getElementById('left-overlay')
    const rightOverlay = document.getElementById('right-overlay')
    const bottomOverlay = document.getElementById('bottom-overlay')
    const mainContainerDiv = document.getElementById('mainContainer')
    const processMsg = document.querySelector('.process-message')
    const midOverlay = document.querySelector('.mid-overlay')
    let startCaptureBtn = window.parent.captureBtn()
    let snipCancelBtn = window.parent.snipCancelDiv()
    let startX, startY, endX, endY, isSelecting = false
    let captureActive = false

    selectionBox.style.display = 'none'
    startCaptureBtn.disabled = false
    startCaptureBtn.addEventListener('click', captureClicked)

    function captureClicked() {
        captureActive = !captureActive // Toggle capture mode
        if (captureActive) {
            if (mobileView == "true") {
                window.parent.hideMobileChat()
            }
            document.body.style.cursor = 'crosshair'
            selectionBox.style.display = 'none'
            mainContainerDiv.classList.add('mainContainerBorder')
        } else {
            resetCapture()
        }
        startCaptureBtn.disabled = captureActive
    }

    document.addEventListener('mousedown', startSelection)
    document.addEventListener('mousemove', moveSelection)
    document.addEventListener('mouseup', endSelection)

    document.addEventListener('touchstart', startSelection)
    document.addEventListener('touchmove', moveSelection)
    document.addEventListener('touchend', endSelection)

    function startSelection(e) {
        if (!captureActive || e.target === startCaptureBtn) return
        isSelecting = true
        const { clientX, clientY } = e.type.includes('touch') ? e.touches[0] : e

        startX = clientX
        startY = clientY
        selectionBox.style.left = startX + "px"
        selectionBox.style.top = startY + "px"
        selectionBox.style.width = '0px'
        selectionBox.style.height = '0px'
        selectionBox.style.display = 'block'
        updateOverlay(startX, startY, startX, startY)
    }

    function moveSelection(e) {
        if (!isSelecting) return
        e.preventDefault()

        const { clientX, clientY } = e.type.includes('touch') ? e.touches[0] : e
        endX = clientX
        endY = clientY

        selectionBox.style.width = Math.abs(endX - startX) + 'px'
        selectionBox.style.height = Math.abs(endY - startY) + 'px'
        selectionBox.style.left = Math.min(startX, endX) + 'px'
        selectionBox.style.top = Math.min(startY, endY) + 'px'
        updateOverlay(startX, startY, endX, endY)
    }

    function endSelection(e) {
        if (!isSelecting) return
        isSelecting = false
        e.preventDefault()

        if (selectionBox.style.width !== '0px' && selectionBox.style.height !== '0px') {
            const rect = selectionBox.getBoundingClientRect()
            if (rect.width === 0 || rect.height === 0) {
                alert('Please select a portion of the page before capturing.')
                return
            }
            selectionBox.style.display = 'none'
            mainContainerDiv.classList.remove('mainContainerBorder')

            showProcessing();
            html2canvas(mainContainerDiv, {
                x: rect.left + window.scrollX,
                y: rect.top + window.scrollY,
                width: rect.width,
                height: rect.height,
            }).then((canvas) => {
                const imgData = canvas.toDataURL('image/png')
                const blob = dataURLToBlob(imgData)

                displayCapturedImage(imgData)
                resetCapture()
                startCaptureBtn.disabled = false
                document.body.style.cursor = 'default'
            }).catch((error) => {
                console.error('Error capturing image:', error)
            })
        }
    }

    function displayCapturedImage(imgData) {
        const imgElement = document.createElement('img')
        imgElement.src = imgData
        const captureResult = window.parent.captureResultDiv()
        captureResult.innerHTML = ''
        captureResult.appendChild(imgElement)
        captureResult.style.display = "block"
        snipCancelBtn.style.display = "flex"
        window.parent.getSnippedImg(imgData)
    }

    function dataURLToBlob(dataURL) {
        const byteString = atob(dataURL.split(',')[1])
        const mimeString = dataURL.split(',')[0].split(':')[1].split(';')[0]
        const ab = new ArrayBuffer(byteString.length)
        const ia = new Uint8Array(ab)
        for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i)
        }
        return new Blob([ab], { type: mimeString })
    }

    function updateOverlay(startX, startY, endX, endY) {
        const rectX = Math.min(startX, endX)
        const rectY = Math.min(startY, endY)
        const rectWidth = Math.abs(endX - startX)
        const rectHeight = Math.abs(endY - startY)

        topOverlay.style.width = '100%'
        topOverlay.style.height = rectY + 'px'
        topOverlay.style.top = '0'
        topOverlay.style.left = '0'

        leftOverlay.style.width = rectX + 'px'
        leftOverlay.style.height = rectHeight + 'px'
        leftOverlay.style.top = rectY + 'px'
        leftOverlay.style.left = '0'

        rightOverlay.style.width = 'calc(100% - ' + (rectX + rectWidth) + 'px)'
        rightOverlay.style.height = rectHeight + 'px'
        rightOverlay.style.top = rectY + 'px'
        rightOverlay.style.left = (rectX + rectWidth) + 'px'

        bottomOverlay.style.width = '100%'
        bottomOverlay.style.height = 'calc(100% - ' + (rectY + rectHeight) + 'px)'
        bottomOverlay.style.top = (rectY + rectHeight) + 'px'
        bottomOverlay.style.left = '0'
    }

    function hideOverlay() {
        topOverlay.style.width = leftOverlay.style.width = rightOverlay.style.width = bottomOverlay.style.width = '0'
        topOverlay.style.height = leftOverlay.style.height = rightOverlay.style.height = bottomOverlay.style.height = '0'
    }

    function resetCapture() {
        captureActive = false
        document.body.style.cursor = 'default'
        processMsg.style.display  = "none"
        midOverlay.style.display = "none"
        if (mobileView == "true") {
            window.parent.showMobileChat()
        }
    }

    function showProcessing(){
        processMsg.style.display  = "flex"
        hideOverlay()
        midOverlay.style.display = "block"
    }
</script>
</body>
</html>


<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Required meta tags -->
  <title><%= title!=null?title:"Oswal Publishers - High-Quality Academic Books & Resources. Best books for CBSE | ICSE\n" +
          "| CUET | NCERT| ISC | Oswal books\n" +
          "Exams"%></title>
  <%if(seoDesc!=null){%>
  <meta name="description" content="${seoDesc}">
  <%}else{%>
  <meta name="description" content="Oswal Publishers is helping Class 9th to 12th students with high-quality books and resources like solved papers, sample papers, question banks, and many more.">
  <%}%>
  <meta charset="utf-8">
  <%if(keywords!=null){%>
  <meta name="keywords" content="${keywords}"/>
  <%}else{%>
  <meta name="keywords" content="Oswal Publishers is helping Class 9th to 12th students with high-quality books and resources like solved papers, sample papers, question banks, and many more. Best books for CBSE | ICSE| CUET | NCERT | ISC." />
  <%}%>
  <meta name="generator" content="OswalPublishers" />
  <meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name="author" content="webmaster - Oswal Printers and Publishers Pvt Ltd">
  <meta name="Oswal Printers and Publishers Pvt Ltd" content="https://oswalpublishers.com/">
  <meta name="subject" content="Book Store Online : Buy Books Online from Oswal Publishers Store">
  <meta name="keyphrase" content="Medical Exam Books, Board exams books, CBSE Exam Books, UGC Net, Air Force Books, state exam books, Govt Exam Books, NDA Exam Books, Bank Po Books, Entrance Exam Books, Engineering Books, Exam Books, General Books, General English Books, General Knowledge Books, NDA & CDS Books, SBI exam books, competition books in Hindi, ssc competition books, civil service books, banking Exams books, Gate, Teacher exam Books, buy books online, shop books online, online shopping for books, book shop, bookstore, online bookstore, online book shop india, books online, online book store, online bookstore india, Competitive Exam Book.">
  <meta name="abstract" content="Find large collection of Entrance Exam Books for engineering, medical, Banking, school and other Exam.">
  <%-- Add noindex, nofollow meta tags for read.oswalpublishers.com subdomain --%>
  <meta name="robots" content="noindex, nofollow">
  <link rel="shortcut icon" href="${assetPath(src: 'oswalpublisher/oswalpub-fav.png')}" type="image/x-icon"/>
  <link rel="icon"  href="${assetPath(src: 'oswalpublisher/oswalpub-fav.png')}" type="image/x-icon">
  <link rel="android-touch-icon" href="${assetPath(src: 'oswalpublisher/oswalpub-fav.png')}"/>
  <link rel="windows-touch-icon" href="${assetPath(src: 'oswalpublisher/oswalpub-fav.png')}" />

  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp" async>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css"  crossorigin="anonymous" referrerpolicy="no-referrer" />
  <!-- Bootstrap CSS -->
  <asset:stylesheet href="arihant/style.css"/>
  <asset:stylesheet href="arihant/responsive.css" />
  <asset:stylesheet href="font-awesome.min.css"/>
  <asset:stylesheet href="arihant/animate.css"/>
  <asset:stylesheet href="landingpage/fonts/flaticon.css"/>
  <asset:stylesheet href="/assets/katex.min.css"/>

  <asset:stylesheet href="wonderslate/material.css" async="true"/>
  <asset:stylesheet href="landingpage/bootstrap.min.css" async="true"/>
  <asset:stylesheet href="wonderslate/headerws.css" async="true"/>
  <asset:stylesheet href="landingpage/slick.min.css" async="true"/>
  <asset:stylesheet href="landingpage/slick.theme.css" async="true"/>
  <asset:stylesheet href="landingpage/homepageStyle.css" async="true" data-role="baseline" />
  <asset:stylesheet href="oswalpublisher/oswalpublisherStyles.css" async="true"/>
  <asset:stylesheet href="oswalpublisher/newHeader.css" async="true"/>

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,700|Oswald:200,300,400,500,600,700&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css?family=Playfair+Display:400,400i&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css?family=Fira+Sans:300,400,500&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&family=Rubik:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
  <asset:javascript src="jquery-1.11.2.min.js"/>
  <script src="/assets/katex.min.js"></script>
  <script src="/assets/auto-render.min.js"></script>
  <!-- General styles for admin & user pages -->
  <%if("true".equals(commonTemplate)){%>
  <asset:stylesheet href="oswalpublisher/oswalpublisherTemplate.css" async="true"/>
  <%}%>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-ECV3C0L1MW"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-ECV3C0L1MW');
    gtag('config', 'G-2Y9D9ELSN2');  //WS GA code
  </script>
</head>
<body class="arihant oswal_publisher">
<sec:ifNotLoggedIn>
  <g:render template="/books/signIn"></g:render>
</sec:ifNotLoggedIn>

<header class="oswal__header">
  <div class="oswal__header-container">
    <nav class="oswal__header-navbar w-100">
      <ul class="oswal__header-navbar__list list-one d-none d-md-flex justify-content-start w-100 ml-2">
        <li class="oswal__header-navbar__list-item d-none d-md-block"><a href="/oswalpublisher/store">Store</a></li>
        <sec:ifLoggedIn>
          <li class="oswal__header-navbar__list-item d-none d-md-block"><a href="/wsLibrary/myLibrary">My Books</a></li>
          <sec:ifAllGranted roles="ROLE_FINANCE">
            <li class="oswal__header-navbar__list-item d-none d-md-block"><a href="/publishing-sales" class="header-menu-item-link">Sales</a></li>
          </sec:ifAllGranted>
          <sec:ifAllGranted roles="ROLE_EXTERNAL_SALES_VIEWER">
            <li class="oswal__header-navbar__list-item d-none d-md-block"><a href="/reports/externalReportInput" class="header-menu-item-link">Integration Sales</a></li>
          </sec:ifAllGranted>
          <sec:ifAllGranted roles="ROLE_CLIENT_ORDER_MANAGER">
            <li class="oswal__header-navbar__list-item d-none d-md-block"><a href="/wsshop/orderManagement">Order Management</a></li>
          </sec:ifAllGranted>
          <sec:ifAllGranted roles="ROLE_BOOK_CREATOR">
            <li class="oswal__header-navbar__list-item d-none d-md-block">
              <a class="dropdown-toggle" href="#" id="publishing" data-toggle="dropdown">
                Publishing
              </a>
              <div class="dropdown-menu">
                <a class="dropdown-item" href="/publishing-desk">Publishing Desk</a>
                <a class="dropdown-item" href="/wonderpublish/manageTabs">Manage Tags</a>
                <a class="dropdown-item" href="/wonderpublish/manageExams">Manage MCQs Templates</a>
              </div>
            </li>
          </sec:ifAllGranted>
          <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_WS_CONTENT_ADMIN,ROLE_NOTIFICATION,ROLE_USER_LOGIN_RESET_MANAGER,ROLE_APP_ADMIN,ROLE_INSTITUTE_REPORT_MANAGER,ROLE_WEB_CHAT,ROLE_WS_EDITOR,ROLE_LIBRARY_ADMIN,ROLE_MASTER_LIBRARY_ADMIN">
            <li class="oswal__header-navbar__list-item d-none d-md-block">
              <a class="dropdown-toggle" href="#" id="ad" data-toggle="dropdown">
                Admin
              </a>
              <div class="dropdown-menu ">
                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                  <a class="dropdown-item" href="/admin/priceList">Price List</a>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                  <a class="dropdown-item" href="/excel/fileUploader">File Uploader</a>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                  <a class="dropdown-item" href="/admin/discountManager">Discount Management</a>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_DELETE_USER">
                  <a class="dropdown-item" href="/admin/deleteuser">Delete User</a>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_INSTITUTE_ADMIN">
                  <a class="dropdown-item" href="/institute/admin">eClass+</a>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                  <a class="dropdown-item" href="/institute/libAdmin">Library Management</a>
                </sec:ifAllGranted>
                <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_INSTITUTE_REPORT_MANAGER">
                  <a class="dropdown-item" href="/reports/instituteReport">Institute reports</a>
                </sec:ifAnyGranted>
                <sec:ifAnyGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                  <a class="dropdown-item" href="/institute/usageReportInstituteAdmin">Usage Statistics Report</a>
                </sec:ifAnyGranted>
                <sec:ifAllGranted roles="ROLE_PUBLISHER">
                  <a class="dropdown-item" href="/log/quizissues">Quiz Issues</a>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_NOTIFICATION">
                  <a class="dropdown-item" href="/log/notification">Notification</a>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_USER_LOGIN_RESET_MANAGER">
                  <a class="dropdown-item" href="/log/userManagement">Force Logout</a>
                </sec:ifAllGranted>

                <sec:ifAllGranted roles="ROLE_ACCESS_CONTROLL">
                  <a class="dropdown-item" href="/log/userAccess">User Access</a>
                </sec:ifAllGranted>

                <sec:ifAllGranted roles="ROLE_WS_EDITOR">
                  <a class="dropdown-item" href="/wonderpublish/wseditor">WS Editor</a>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_NOTIFICATION">
                  <a class="dropdown-item" href="/log/notificationManagement">Notification Management</a>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN,ROLE_BOOK_CREATOR">
                  <a class="dropdown-item" href="/admin/managePublishers">Publisher Management</a>
                </sec:ifAllGranted>
                <%if(session["userdetails"].publisherId!=null){%>
                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                  <a class="dropdown-item" href="/publisherManagement/publisherReport">Publisher Report</a>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                  <a class="dropdown-item" href="/admin/accessCodeUsage">Access Code Report</a>
                </sec:ifAllGranted>
                <%}%>
                <sec:ifAllGranted roles="ROLE_LIBRARY_ADMIN">
                  <a class="dropdown-item" href="/institute/userManagement">Library Management</a>
                  <a class="dropdown-item" href="/institute/manageInstitutePage">Institute Page Management</a>
                  <a class="dropdown-item" href="/institute/usageReportInstituteAdmin">Usage Statistics Report</a>
                </sec:ifAllGranted>
                <sec:ifAnyGranted roles="ROLE_BOOK_CREATOR,ROLE_DIGITAL_MARKETER">
                  <a class="dropdown-item" href="/wonderpublish/bannerManagement">Banner Management</a>
                </sec:ifAnyGranted>
              </div>
            </li>
          </sec:ifAnyGranted>
        </sec:ifLoggedIn>
      </ul>

      <div class="oswal__header-navbar__logo d-flex justify-content-start justify-content-lg-center">
        <a href="/oswalpublisher/">
          <img src="${assetPath(src: 'oswalpublisher/oswalpub-logo.svg')}" alt="Oswal Publisher Logo" id="logoFull" class="logo-imgText d-flex">
        </a>
      </div>

      <ul class="oswal__header-navbar__list list-two w-100 justify-content-end">
        <li class="oswal__header-navbar__list-item "><a href="/wsshop/cart" class="menu-link-anchor mobile_cart_icon d-flex align-items-center"><i class="material-icons-outlined cart">shopping_cart</i><span class="cart_count" id="navbarCartCount">0</span></a></li>

        <sec:ifLoggedIn>
          <li class="oswal__header-navbar__list-item"><a href="javascript:openSearchBar()" ><i class="material-icons-outlined" id="searchIcon">search</i></a></li>
          <li class="oswal__header-navbar__list-item account" title="My Account">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="material-icons user">person_outline</i></a>
            <div class="dropdown-menu ">
              <div class="dropdown-menu-wrapper">
                <a href="/creation/userProfile">My Account</a>
                <a href="javascript:logout()">Logout</a>
              </div>
            </div>
          </li>
        </sec:ifLoggedIn>

        <sec:ifNotLoggedIn>
          <li class="oswal__header-navbar__list-item"><a href="javascript:openSearchBar()" ><i class="material-icons-outlined" id="searchIcon">search</i></a></li>
          <li class="oswal__header-navbar__list-item " title="My Oswal Account"><a href="javascript:loginOpen()"><i class="material-icons user">person_outline</i></a></li>
        </sec:ifNotLoggedIn>
      </ul>
    </nav>
  </div>
  <div class="oswal__header-searchBar container" style="display: none">
    <input type="search" class="typeahead" name="search" id="search-book-header" autocomplete="off" placeholder="Search title, subject, author, ISBN, language etc.">
  </div>
  <div class="headerCategoriesMenu">
    <div class="header__categories">
      <ul class="header__categories-list" id="catList"></ul>
    </div>
    <div class="header__submenus" id="headerCategorySubMenus">
      <p class="text-center subMenuTitleText"><strong id="subMenuTitle"></strong></p>
      <div class="submenuLists">
        <ul class="syllabusList"></ul>
        <div class="listScrollArrow">
          <div class="listScrollArrowBall"></div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-backdrop fade show headerBackdrop d-none" id="categoryMenuBackdrop" style="position: absolute !important;height: 100vh"></div>
</header>

<script>
  var activeCategories = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
  function logout(){
    window.location.href = '/logoff';
  }
  function openSearchBar(){
    var searchIcon = document.getElementById('searchIcon');
    $(".oswal__header-searchBar").slideToggle();
    searchIcon.textContent == 'search' ? searchIcon.textContent='close' :  searchIcon.textContent='search';
  }

  window.onscroll= function (){
    var largeLogo = document.getElementById('logoFull');
    if (window.pageYOffset > 100){
      $(window).width() > 768 ?  largeLogo.style.width='80px':'';
    }else{
      $(window).width() > 768 ?  largeLogo.style.width='90px' :'';
    }
    largeLogo.classList.add('transition');
  }
  var levelTags = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
  var syllabusTags = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
  function updateHeaderCategories(){
    let catItems="";
    const catList = levelTags;
    const catListElement = document.getElementById('catList');
    catList.forEach((cat,index)=>{
      const catName = cat.level.replaceAll(' ','-');
      catItems += "<li class='header__categories-list__item'>" +
              "<a href='/${session["entryController"]}/store?level="+catName+"' class='headerLevel' target='_blank'>"+cat.level+"</a>"+
              "</li>";
    });
    catListElement.innerHTML = catItems;
    const hoverElement = document.querySelectorAll('.headerLevel');
    const showDiv = document.getElementById('headerCategorySubMenus');
    const handleMouseLeave = () => {
      addRemoveBackDrop('hide',showDiv);
    };
    hoverElement.forEach(elem=>{
      elem.addEventListener('mouseover', () => {
        updateSubMenus(elem.textContent)
        addRemoveBackDrop('show',showDiv);
      });
      showDiv.addEventListener('mouseout', handleMouseLeave);
      showDiv.addEventListener('mouseover', ()=>{
        addRemoveBackDrop('show',showDiv);
      });
      document.querySelector('.oswal__header-container').addEventListener('mouseover',handleMouseLeave);
      document.addEventListener('click',handleMouseLeave);
    });
  }
  function updateSubMenus(hoveredText){
    let syllabusListHTML = "";
    const syllabusListDiv =  document.querySelector('.syllabusList');
    const headerCategorySubMenus = document.getElementById('headerCategorySubMenus');
    document.getElementById('subMenuTitle').innerHTML = hoveredText;
    const listScrollArrow = document.querySelector('.listScrollArrow');
    let syllabusCount = 0;
    syllabusTags.forEach(syllabus=>{
      if (syllabus.level === hoveredText){
        let syllabusLink = syllabus.level.replaceAll(" ",'-');
        syllabusLink += "&syllabus="+ syllabus.syllabus.replaceAll(" ",'-');
        syllabusListHTML += "<li><a href='/${session["entryController"]}/store?level="+syllabusLink+"' target='_blank'>"+syllabus.syllabus+"</a></li>";
        syllabusCount++;
      }
    })
    syllabusListDiv.innerHTML = syllabusListHTML;
    setTimeout(()=>{
      if (headerCategorySubMenus.offsetHeight >= 500 && syllabusCount >= 30){
        listScrollArrow.style.display = 'flex';
      }else{
        listScrollArrow.style.display = 'none';
      }
    })
  }
  function addRemoveBackDrop(action,showDiv){
    const categoryMenuBackdrop = document.getElementById('categoryMenuBackdrop');
    const headerElement = document.querySelector('.oswal__header-container');
    const headerCategoriesMenu = document.querySelector('.headerCategoriesMenu');
    if(action=='show'){
      showDiv.style.display = 'block';
      showDiv.style.opacity = '1';
      categoryMenuBackdrop.classList.remove('d-none');
      categoryMenuBackdrop.style.top = headerElement.offsetHeight + headerCategoriesMenu.offsetHeight +'px';
    }else if(action=='hide'){
      showDiv.style.display = 'none';
      showDiv.style.opacity = '0';
      categoryMenuBackdrop.classList.add('d-none');
    }
  }
  if (levelTags.length>0){
    updateHeaderCategories();
  }else {
    document.querySelector('.headerCategoriesMenu').classList.add('d-none');
  }
  if ($(window).width() < 767){
    let accordionHTMl = "";
    let accID = "";
    accordionHTMl +="<div class='accordion' id='accordion'>";
    levelTags.forEach((cat,index)=>{
      accID = index;
      accordionHTMl += "<div class='card mb-3'>" +
              "<div class='card-header p-2' id='heading-"+index+"'>";
      if(index==0){
        accordionHTMl +="<button class='text-dark btn btn-link w-100 d-flex justify-content-between pl-0' data-toggle='collapse' data-target='#collapse-"+index+"' aria-expanded='true' aria-controls='#collapse-"+index+"'>";
      }else{
        accordionHTMl +="<button class='text-dark btn btn-link w-100 d-flex justify-content-between pl-0 collapsed' data-toggle='collapse' data-target='#collapse-"+index+"' aria-expanded='true' aria-controls='#collapse-"+index+"'>";
      }
      accordionHTMl +="<a href='/${session["entryController"]}/store?level="+cat.level.replaceAll(" ",'-')+"' target='_blank'><p>"+cat.level+"</p></a>"+
              "</button>" +
              "</div>";
      if (index==0){
        accordionHTMl +="<div id='collapse-"+accID+"' class='collapse show' aria-labelledby='heading-"+accID+"' data-parent='#accordion'>";
      }else{
        accordionHTMl +="<div id='collapse-"+accID+"' class='collapse' aria-labelledby='heading-"+accID+"' data-parent='#accordion'>";
      }
      accordionHTMl +="<div class='card-body'>";
      syllabusTags.forEach((syllabus,index)=>{
        if (cat.level === syllabus.level){
          accordionHTMl +="<a href='/${session["entryController"]}/store?level="+cat.level+"&"+ "&syllabus="+ syllabus.syllabus.replaceAll(" ",'-')+"'  target='_blank'><p>"+syllabus.syllabus+"</p></a>";
        }
      })
      accordionHTMl +="</div>"+
              "</div>"+
              "</div>";
    })
    accordionHTMl+="</div>";
    if (document.querySelector('.arihant-big-menu-side-wrp')){
      document.querySelector('.arihant-big-menu-side-wrp').innerHTML = accordionHTMl;
    }
  }
</script>

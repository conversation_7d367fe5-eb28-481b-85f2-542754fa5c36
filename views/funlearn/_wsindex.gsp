<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Wonderslate</title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1 maximum-scale=1, minimum-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <link rel="icon" sizes="192x192" href="${assetPath(src: 'app-icon-4x.png')}">
    <link rel="manifest" href="manifest.json">
    <link rel="icon" href="${assetPath(src: 'favicon.ico')}" type="image/x-icon" />
    <link href='https://fonts.googleapis.com/css?family=Roboto:300,700,900' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Exo' rel='stylesheet' type='text/css'>
    <asset:stylesheet href="bootstrap.css"/>
    <asset:stylesheet href="style.css"/>
    <asset:stylesheet href="font-awesome.min.css"/>

	<asset:javascript src="soundmanager2-jsmin.js"/>	
	<asset:javascript src="bar-ui.js"/>	
	<asset:stylesheet href="bar-ui.css"/>	
</head>
<%
    String url = (request.getRequestURL()).toString();
    String serverUrl = url.substring(0,url.indexOf('/',9));
    String requestURL = request.getRequestURL().toString();
    String servletPath = request.getServletPath();
    String appURL = requestURL.substring(0, requestURL.indexOf(servletPath));

    session.setAttribute("servername", appURL);

%>
<body>
<div class="sm2-bar-ui compact">
 <div class="bd sm2-main-controls">
  <div class="sm2-inline-texture"></div>
  <div class="sm2-inline-gradient"></div>

  <div class="sm2-inline-element sm2-button-element">
   <div class="sm2-button-bd">
    <a href="#play" class="sm2-inline-button play-pause">Play / pause</a>
   </div>
  </div>

  <div class="sm2-inline-element sm2-inline-status">
   <div class="sm2-playlist">
    <div class="sm2-playlist-target">
     <!-- playlist <ul> + <li> markup will be injected here -->
     <!-- if you want default / non-JS content, you can put that here. -->
     <noscript><p>JavaScript is required.</p></noscript>
    </div>
   </div>

   <div class="sm2-progress">
    <div class="sm2-row">
    <div class="sm2-inline-time">0:00</div>
     <div class="sm2-progress-bd">
      <div class="sm2-progress-track">
       <div class="sm2-progress-bar"></div>
       <div class="sm2-progress-ball"><div class="icon-overlay"></div></div>
      </div>
     </div>
     <div class="sm2-inline-duration">0:00</div>
    </div>
   </div>
  </div>

  <div class="sm2-inline-element sm2-button-element sm2-volume">
   <div class="sm2-button-bd">
    <span class="sm2-inline-button sm2-volume-control volume-shade"></span>
    <a href="#volume" class="sm2-inline-button sm2-volume-control">volume</a>
   </div>
  </div>
 </div>

 <div class="bd sm2-playlist-drawer sm2-element">
  <div class="sm2-inline-texture">
   <div class="sm2-box-shadow"></div>
  </div>
  <!-- playlist content is mirrored here -->
  <div class="sm2-playlist-wrapper">
    <ul class="sm2-playlist-bd">
     <li><a href="funlearn/getMP3?id=1&fileName=rain.mp3">Rain</a></li>
    </ul>
  </div>
 </div>
</div>
<div class="header-and-topic-select">
    <div class="container">
        <div class="header">
            <div class="row">
                <div class="col-md-6 col-sm-6">
                    <div class="brand">
                        <a  href="#"><img src="${assetPath(src: 'logo-ws.png')}">WONDERSLATE <span class="smallerText">(BETA)</span></a>
                    </div>
                </div>
                <div class="col-md-6 col-sm-6 text-right">

                    <ul class="list-inline">
                        <li class="transparent-button"><a href="/funlearn/aboutus">&nbsp;What is Wonderslate?&nbsp;</a></li>
                        <sec:ifNotLoggedIn>
                            <li class="transparent-button"><a href="javascript:showregister('login');">&nbsp;Sign in&nbsp;</a></li>
                            <li class="transparent-button"><a href="javascript:showregister('signup');">&nbsp;Register&nbsp;</a></li>
                        </sec:ifNotLoggedIn>
                        <sec:ifLoggedIn>
                            <li class="transparent-button"><g:link uri="/logoff">&nbsp;Logout&nbsp;</g:link></li>
                        </sec:ifLoggedIn>
                    </ul>

                </div>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="topic-select">
            <div class="row">
                <div class="col-md-8 col-sm-8  col-xs-12 homemessage">Create and store your study materials at one place.
                </div>
            </div>
            <div class="row">
                <div class="col-md-8 col-sm-8  col-xs-12 homemessagesmall">The easiest and safest way for students, teachers and parents to add study materials, connect and collaborate with each other.<BR>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8 col-sm-8  col-xs-12 homemessagesmallest">See what others have published ..
                </div>
            </div>
            <div class="row" id="homeInputs">
                <div class="col-md-2 col-sm-4 col-xs-12 col-centered inputArrow">
                    <select required id="selectedLevel" name="selectedLevel" class="form-control" onchange="javascript:changeBoard(this.value);blur();">
                        <option value=""  selected>Select </option>

                    </select>
                </div>
                <div class="col-md-2 col-sm-4 col-xs-12 col-centered inputArrow">
                    <select required id="selectedBoard" name="selectedBoard" class="form-control" onchange="javascript:changeGrade(this.value);blur();">
                        <option value=""  selected>My board is ...</option>

                    </select>
                </div>
                <div class="col-md-2 col-sm-4 col-xs-12 col-centered">
                    <select required id="selectedGrade" name="selectedGrade"  class="form-control" onchange="javascript:changeSubject(this.value);blur();">
                        <option value="" selected >My grade is ...</option>
                    </select>
                </div>
                <div class="col-md-2 col-sm-4 col-xs-12 col-centered">
                    <select required id="selectedSubject" name="selectedSubject" class="form-control" onchange="javascript:changeTopic(this.value);blur();">
                        <option value=""  selected >I want to study ...</option>
                    </select>
                </div>
            </div>
            <div class="row" id="homeInputs">
                <div class="col-md-6 col-sm-6 col-centered col-xs-12">
                    <select required id="selectedTitleAndId" name="selectedTitleAndId" class="form-control" onchange="javascript:loadDetailsPage(this,'topic');blur();">
                        <option value=""  selected >I am interested in ...</option>
                    </select>
                </div>
            <!--</div><br><br><br><br><br><br>-->
        </div>
        <div class="row" id="homeRegister">
            <div class="row row-centered">
                <div class="col-md-6 col-sm-6  col-xs-12 homemessagesmall">Create your free account
                </div>
            </div>
            <div class="row row-centered">
                <div class="col-md-6 col-sm-6  col-xs-12 homemessagesmall"><a class='btn btn-success' type='button' href="javascript:showregister('signup');">Sign up</a>
                </div>
            </div>
        </div>    

    </div>
</div>
</div>

<div style="background-color: white">
    <div class="container"><br>
        <div class="row row-centered">
            <div class="col-md-12 col-sm-12  col-xs-12 homemessage greytext" >Features
            </div>
        </div><br>

    </div>
</div>

            <div class="container-fluid studentsRow">
                <div class="row">
                    <div class="col-md-6 btm points" id="student">
                        <div class="studentP">
                            <br><br><br><br><br><br><h2 style="color: white">Personal repository of study materials</h2><br>
                        <hr class="style-seven">
                        <p>Add your own videos, quizzes, notes,mindmaps etc.</p>
                            <p> Organise materials as per your syllabus.</p>
                            <p>Use quizzes to prepare for your exams.</p>
                            <p>Use it anytime, anywhere.</p>
                        </div>
                    </div>
                    <div class="col-md-6 btm" id="students">
                    </div>
                </div>
                <br><br>
                <div class="row">
                    <div class="col-md-6 btm" id="teachers">
                    </div>
                    <div class="col-md-6 btm points" id="student">
                        <br><br><br><br><br><br> <h2 style="color: white">Collaboration</h2><br>
                        <hr class="style-seven">
                        <p>Create study groups, join existing ones.</p>
                        <p>Share study materials among friends and learn together.</p>
                        <p>Connect with subject experts.</p>
                        <p>Ask questions and get answers.</p>
                    </div>
                </div>
                <br><br>
                <div class="row">
                    <div class="col-md-6 btm points" id="student">
                        <br><br><br><br><br> <h2 style="color: white">Gamification</h2><br>
                        <hr class="style-seven">
                        <p>Gamification of your learning process</p>
                        <p>Motivational - Encourages incremental learning.</p>
                        <p>Cognitive - Improves attention, focus and reaction time</p>
                        <p>Emotional - Encourages positive mood states by rewarding the both the participation and progress.</p>
                    </div>
                    <div class="col-md-6 btm" id="parents">
                    </div>
                </div>
        <br><br>
                <div class="row">
                    <div class="col-md-6 btm analytics">
                    </div>
                    <div class="col-md-6 btm points" id="student">
                        <br><br><br><br><br><br> <h2 style="color: white">Analytics as your practise coach</h2><br>
                        <hr class="style-seven">
                        <p>Identify your strengths and areas of improvement</p>
                        <p>Track your progress as you practise using different quiz options.</p>
                        <p>Get subject and topic specific inputs/suggestions.</p>
                        <p>Personalised feedback mechanism and completely secure.</p>
                    </div>
                </div>
                <br><br>
  <!--              <div class="row row-centered">
                    <div class="col-md-4 text-center points">

                        <h2>For Students</h2><br>
                        <p>Add your own videos, quizzes, notes,mindmaps etc. </p>
                        <p>Organise materials as per your syllabus. </p>
                        <p>Use quizzes to prepare for your exams</p>
                    </div>
                    <div class="col-md-4 text-center points">

                        <h2>For Teachers</h2><br>
                        <p>Add, discover and share valuable learning resources</p>
                        <p>Making the whole learning experience more interactive and fun.</p>
                        <p>Better understanding of students performance using analytics</p>
                    </div>
                    <div class="col-md-4 text-center points">

                        <h2>For Parents</h2><br>
                        <p>Ability to participate in your child's learning process at your time and convenience.</p>
                        <p>Communicate directly with teacher and other parents group.</p>
                        <p> Contribute to your child's learning by adding resources at anytime and anywhere.</p>

                    </div>

                </div><br><br>-->

    </div>
<asset:javascript src="searchContents.js"/>

<g:render template="footer"></g:render>


<asset:javascript src="moment.min.js"/>
<asset:javascript src="topic.js"/>

<%
    String syllabusType=session.getAttribute("syllabusType")==null?"school":session.getAttribute("syllabusType");
    String country=session.getAttribute("country")==null?"India":session.getAttribute("country");
%>
<script>
    var serverPath= "${request.contextPath}";

    var syllabusType="${syllabusType}";
    var country ="${country}";
    function getTopicsMap(){
        <g:remoteFunction controller="funlearn" action="topicsMap"  onSuccess='initializeDataIndex(data);'
                params="'syllabusType='+syllabusType+'&country='+country" />
    }

    getTopicsMap();

    $(window).bind("pageshow", function(event) {
        if (event.originalEvent.persisted) {
            $(".lmodal").hide();
        }
    });



</script>
<g:if test="${flash.message}">
    <% def msg = flash.message;%>
    <div class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" id="statusWindow" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="close" data-dismiss="modal"  aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <b><p></p></b>
                    <br>
                    <div class="greytext" style="display: block" id="flashMessage" name="flashMessage"></div>
                </div>
                <div class="modal-footer">
                    <div class="row text-center">
                        <button type="button" data-dismiss="modal" class="btn btn-primary">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        document.getElementById("flashMessage").innerHTML=htmlDecode("${flash.message}")
        $("#statusWindow").modal("show");
    </script>
</g:if>
<% if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>
<asset:javascript src="analytics.js"/>
<% } %>

</body>
</html>
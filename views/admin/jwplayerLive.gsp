<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">

<style>
.dataTables_paginate ul.pagination .page-link {
    font-size: 15px;
}
.dataTables_paginate ul.pagination .page-link:not(:disabled):not(.disabled) {
    color: #212529;
}
.dataTables_paginate ul.pagination .page-link:focus {
    box-shadow: 0 0 0 0.1rem rgba(0,123,255,.25);
}
.dataTables_paginate ul.pagination .page-item.active .page-link {
    background-color: #007bff !important;
}
#jwVideoModalData p {
    font-size: 15px;
}
#jwVideoModalData p strong {
    font-weight: 500;
}
</style>

<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid my-5 px-5" style="min-height: calc(100vh - 160px);">
    <div class='row m-0 px-5'>
        <div class='col-md-12 main p-4' style=" margin: 40px auto; float: none;">
            <div id="content-books">
                <table id="videoData" class='table table-hover table-bordered w-100' style="display: none;">
                    <thead>
                    <tr class="bg-primary text-white">
                        <th>Res Id</th>
                        <th>Resource Name</th>
                        <th>Book Id</th>
                        <th>Chapter Id</th>
                        <th>Start Date</th>
                        <th>End Date</th>
                        <th width="20%" style="text-align: center">Action</th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="modal fade jw-video-modal" id="jwVideoModal" data-keyboard="false" data-backdrop="static" >
    <div class="modal-dialog">
        <div class="modal-content">
            <!-- Modal body -->
            <div class="modal-body">
                <div id="jwVideoModalData" class="px-3 pt-3">

                </div>
                <div class="text-center p-3">
                    <button type="button" class="btn btn-primary col-md-3" data-dismiss="modal">Okay</button>
                </div>
            </div>

        </div>
    </div>
</div>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
<script>

    function liveVideoSearch(){
        $('.loading-icon').removeClass('hidden');
        if ($.fn.dataTable.isDataTable('#videoData')) {
            $('#videoData').DataTable().destroy();
        }
        $('#videoData').show();
        $('#videoData').DataTable({
            // "processing": true,
            "serverSide": true,
            "bRetrieve": true,
            // "searching": false,
            "ordering":  false,
            'ajax': {
                'url': '/admin/jwplayerLive',
                'type': 'GET',
                'data': function (outData) {
                    outData.jwplayer = "yes"
                    return outData;
                },
                dataFilter: function (inData) {
                    return inData;
                },
                error: function (err, status) {
                    console.log(err);
                },
            },
            'columns': [
                {
                    'data': 'id',
                },
                {
                    'data': 'resourceName',
                },
                {
                    'data': 'bookId',
                },
                {
                    'data': 'chapterId',
                },
                {
                    'data': 'testStartDate',
                },
                {
                    'data': 'testEndDate',
                },
                {
                    'data': 'id',
                    'render': function (data, type, row) {
                        if(row.channelId==null && row.streamKey==null) {
                            return "<input type=\"button\" class='btn btn-sm btn-primary'  onclick='getChannelId(\"" + row.id + "\",\"" + row.bookId +"\");' value='Start'><td>   </td>";
                        }else {
                            return  "<input type=\"button\" class='btn btn-sm btn-secondary'  onclick='ShowSecretKey(\"" + row.streamKey + "\");' value='Show Stream Key'><td>   </td>" +
                                "<input type=\"button\"  class='btn btn-sm btn-success' onclick='getChannelDetailsByChannelId(\"" + row.channelId +"\",\"" + row.id +"\");' value='Go live'>";


                        }
                    }
                },
            ],
        })

        $('.loading-icon').addClass('hidden');
    }

    function getChannelId(id,bookId) {
        <g:remoteFunction controller="admin" action="createChannel"  params="'resId='+id+'&bookId='+bookId" onSuccess="channelCreated(data)"/>
        $(".loading-icon").removeClass("hidden");
    }

    var jwstreamKey;
    function  channelCreated(data){
        $(".loading-icon").addClass("hidden");
        var channelId=data.channelDetails.id;
        var streamKey=data.channelDetails.stream_key;
        var streamUrl="rtmps://global-live.mux.com/app";
        if(channelId!="" && channelId!=null && streamKey!="" && streamKey!=null) {
            jwstreamKey=streamKey;
            document.getElementById("jwVideoModalData").innerHTML = "<p><strong>Stream Key</strong></p>\n" +
                "<div class='d-flex align-items-center mb-3'><input type='text' id='jwStreamKey' class='form-control' value='"+streamKey+"' readonly>\n" +
                "<span onclick=\"copyContent('jwStreamKey')\" class=\"material-icons pl-2\" style='font-size: 35px;cursor: pointer;'>\n" +
                "description\n" +
                "</span></div>\n" +
                "<p><strong>Stream URL</strong></p>\n" +
                "<div class='d-flex align-items-center'><input type='text' id='jwStreamUrl' class='form-control' value='"+streamUrl+"' readonly>\n" +
                "<span onclick=\"copyContent('jwStreamUrl')\" class=\"material-icons pl-2\" style='font-size: 35px;cursor: pointer;'>\n" +
                "description\n" +
                "</span></div>";
            $("#jwVideoModal").modal("show");
            $('#jwVideoModal').on('hidden.bs.modal', function () {
                location.reload();
            });
        }else{
            document.getElementById("jwVideoModalData").innerHTML = "<p class='text-center'>Video not created. Please try again after couple of minutes.</p>";
            $("#jwVideoModal").modal("show");
        }

    }
    function copyContent(id) {
        var copyText = document.getElementById(id);
        copyText.select();
        copyText.setSelectionRange(0, 99999);
        document.execCommand("copy");
    }


    function getChannelDetailsByChannelId(channelId,resId){
        var channelId=channelId;
        <g:remoteFunction controller="admin" action="getChannelDetailsByChannelId"  params="'channelId='+channelId+'&resId='+resId" onSuccess="videoCreated(data)"/>
        $(".loading-icon").removeClass("hidden");
    }

    function videoCreated(data){
        $(".loading-icon").addClass("hidden");
        var recentEvents=data.channelVideoDetails.recent_events;
        if(recentEvents!="" && recentEvents!=null) {
            var mediaId=recentEvents[0].media_id;
            if(mediaId!=null  && mediaId!="") {
                document.getElementById("jwVideoModalData").innerHTML = "<p class='text-center'>Video created successfully!</p>";
                $("#jwVideoModal").modal("show");
                $('#jwVideoModal').on('hidden.bs.modal', function () {
                    location.reload();
                });
            }
        }else{
            document.getElementById("jwVideoModalData").innerHTML = "<p class='text-center'>Video not created. Please try again.</p>";
            $("#jwVideoModal").modal("show");
        }
    }

    function  ShowSecretKey(streamKey){
        jwstreamKey=streamKey;
        document.getElementById("jwVideoModalData").innerHTML = "<p><strong>Stream Key</strong></p><div class='d-flex align-items-center'><input type='text' id='jwStreamKey' class='form-control' value='"+streamKey+"' readonly><span onclick=\"copyContent('jwStreamKey')\" class=\"material-icons pl-2\" style='font-size: 35px;cursor: pointer;'>\n" +
            "description\n" +
            "</span></div>";
        $("#jwVideoModal").modal("show");

    }


    window.onload=liveVideoSearch();


</script>


</body>
</html>

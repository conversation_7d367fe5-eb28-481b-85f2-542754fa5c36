<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}
#batchUsers{
    width: 100%;
    overflow-x: scroll !important;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">User GPT activity log for ${params.name}</h3>
                    <div class="form-group">
                        <label>Normal requests</label>
                        <p>${userData.normalRequests}</p>

                        <label>Doubts</label>
                        <p>${userData.doubtRequests}</p>
                    </div>
                    <div class="form-group table-responsive" id="intrst-area">
                        <table>

                        </table>
                        <div id="showMore" style="display: none">
                            <BR><button class="btn btn-primary" onclick="getLogs()">Show More</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
<script>
    var pageNo=0;
    var pageOffset;
    var max=50;

    function getLogs(){
        pageOffset = pageNo*max;
        <g:remoteFunction controller="prompt" action="findGptLogsForUser" params="'username=${params.username}&max='+max+'&offset='+pageOffset" onSuccess = "displayLogs(data);"/>
    }

    function displayLogs(data){
        var logs = data.gptLogs;
        if(logs.length==0){
            $("#showMore").hide();
            return;
        }
        var html = "<tr>\n" +
            "                                <th>Book</th>\n" +
            "                                <th>Book ID</th>\n" +
            "                                <th>Chapter</th>\n" +
            "                                <th>Type</th>\n" +
            "                                <th>User Prompt</th>\n" +
            "                                <th>System Prompt</th>\n" +
            "                                <th>Response</th>\n" +
            "                                <th>Feedback Type</th>\n" +
            "                                <th>Feedback</th>\n" +
            "                                <th>Link</th>\n" +
            "                            </tr>";
        for(var i=0; i<logs.length; i++){
            var log = logs[i];
            html += "<tr>";
            html += "<td>"+(log.bookId || "")+"</td>";
            html += "<td>"+(log.bookTitle || "")+"</td>";
            html += "<td>"+(log.chapterName || "")+"</td>";
            html += "<td>"+(log.promptType || "")+"</td>";
            html += "<td>"+(log.userPrompt || "")+"</td>";
            html += "<td>"+(log.systemPrompt || "")+"</td>";
            if(log.response && log.response.length > 150) {
                html += "<td><span class='trimmed-response'>"+log.response.substring(0, 150)+"</span><a href='#' class='show-more'>...Show More</a><span class='full-response' style='display:none;'>"+log.response+"</span></td>";
            } else {
                html += "<td>"+(log.response || "")+"</td>";
            }
            html += "<td>"+(log.feedbackType || "")+"</td>";
            html += "<td>"+(log.feedback || "")+"</td>";
            html += "<td><a href='/prompt/showGptLogsForResId?resId="+log.readingMaterialResId+"' target='_blank'> "+(log.readingMaterialResId || "")+"</a></td>";
            html += "</tr>";
        }
        $("#intrst-area table").append(html);
        $("#showMore").show();
        pageNo++;

        // Add click event for "Show More" links
        $(".show-more").click(function(e) {
            e.preventDefault();
            $(this).hide();
            $(this).siblings(".trimmed-response").hide();
            $(this).siblings(".full-response").show();
        });
    }

    getLogs();
</script>


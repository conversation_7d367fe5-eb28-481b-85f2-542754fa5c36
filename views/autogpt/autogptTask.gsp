<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}

/* Enhanced styles for chapter selection and status display */
.chapter-selection-container {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
    background-color: #f9f9f9;
}

.chapter-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ccc;
    padding: 10px;
    background-color: white;
    border-radius: 3px;
}

.chapter-item {
    padding: 8px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
}

.chapter-item:last-child {
    border-bottom: none;
}

.chapter-item input[type="checkbox"] {
    margin-right: 10px;
}

.chapter-item label {
    margin: 0;
    cursor: pointer;
    flex-grow: 1;
}

.select-all-container {
    padding: 10px;
    background-color: #e9ecef;
    border-bottom: 1px solid #ccc;
    font-weight: bold;
}

.status-panel {
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 15px;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-header {
    padding: 12px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
    font-weight: bold;
    border-radius: 5px 5px 0 0;
}

.status-body {
    padding: 15px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.status-item:last-child {
    border-bottom: none;
}

.status-label {
    font-weight: 500;
}

.status-value {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    margin-right: 8px;
}

.status-pending {
    background-color: #6c757d;
    color: white;
}

.status-progress {
    background-color: #007bff;
    color: white;
}

.status-success {
    background-color: #28a745;
    color: white;
}

.status-error {
    background-color: #dc3545;
    color: white;
}

.timing-display {
    font-family: monospace;
    font-size: 11px;
    color: #666;
}

.progress-container {
    margin-top: 20px;
}

.overall-progress {
    margin-bottom: 20px;
}

.validation-error {
    color: #dc3545;
    font-size: 14px;
    margin-top: 10px;
    display: none;
}

.btn-submit {
    background-color: #007bff;
    border-color: #007bff;
    padding: 10px 30px;
    font-size: 16px;
}

.btn-submit:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: none;
    z-index: 9999;
}

.loading-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">AutoGPT V2</h3>
                    <div class="form-group table-responsive" id="intrst-area">
                        <div class="form-group">
                           <label for="chapterId">ChapterId</label><br>
                           <input type="number" class="w-100 form-control" id="chapterId" name="chapterId">
                           </div>

                           <button class="btn btn-primary"  onclick="startAutoGPT()">Submit</button>
                           <div id="results"></div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
<script>
    var resId
    function startAutoGPT(){
        var chapterId = $('#chapterId').val();
        if(chapterId==''){
            alert('Please enter the chapter id');
            return;
        }
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="prompt" action="storePdfVectors" params="'chapterId='+chapterId" onSuccess = "vectorStored(data);"/>
    }

    function vectorStored(data){
        $('.loading-icon').addClass('hidden');
        if(data.status=='OK'){
            document.getElementById('results').innerHTML='PDF Vectors stored successfully';
            resId = data.resId;
            storeMetadata();
        }else{
            document.getElementById('results').innerHTML='Error in storing PDF Vectors';
        }
    }

    function storeMetadata(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="autogpt" action="getChapterMetaData" params="'resId='+resId" onSuccess = "metadataStored(data);"/>
    }

    function metadataStored(data){
        $('.loading-icon').addClass('hidden');
        if(data.status=='OK'){
            document.getElementById('results').innerHTML+='<br>Metadata stored successfully';
            exerciseCollector();
        }else{
            document.getElementById('results').innerHTML+='<br> Error in storing metadata';
        }
    }

    function exerciseCollector(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="autogpt" action="exerciseCollector" params="'resId='+resId" onSuccess = "exercisesCollected(data);"/>
    }

    function exercisesCollected(data){
        $('.loading-icon').addClass('hidden');
        if(data.status=='OK'){
            document.getElementById('results').innerHTML+='<br>Exercises collected successfully';
            questionBankBuilder();
        }else{
            document.getElementById('results').innerHTML+='<br> Error in collecting exercises';
        }
    }

    function questionBankBuilder(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="autogpt" action="questionBankBuilder" params="'resId='+resId" onSuccess = "questionBankBuilt(data);"/>
    }

    function questionBankBuilt(data){
        $('.loading-icon').addClass('hidden');
        if(data.status=='OK'){
            document.getElementById('results').innerHTML+='<br>Question bank built successfully';
        }else{
            document.getElementById('results').innerHTML+='<br> Error in building question bank';
        }
    }


</script>

<script>
    var input = document.getElementById('excelFileInput');
    var tableContainer = document.getElementById('tableContainer');
    var table,errorTable;
    var failedRows = [];
    var mandatoryFields = ['Book_Title','Sell_Price','List_Price'];
    var columnNames;
    var originalColumnsCount;
    var finalColumnsCount;
    var isbnCellIndex=-1;
    var resId=-1;


    function handleFileUpload() {
        if (document.getElementById("publisherId").selectedIndex == 0) {
            alert("Select Publisher");
            document.getElementById("publisherId").focus();

        } else if (document.getElementById("bookType").selectedIndex == 0) {
            alert("Select Book Type");
            document.getElementById("bookType").focus();
        }else if(document.getElementById('excelFileInput').files.length==0){
            alert("Please select the file to upload.");
            document.getElementById('excelFileInput').focus();
        }
        else {
            failedRows = [];
            var fileInput = document.getElementById('excelFileInput');
            var file = fileInput.files[0];
            var reader = new FileReader();

            reader.onload = function (e) {
                var data = new Uint8Array(e.target.result);
                var workbook = XLSX.read(data, {type: 'array'});

                var sheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[sheetName];
                var jsonData = XLSX.utils.sheet_to_json(worksheet, {header: 1});

                columnNames = jsonData[0]; // Assuming the first row contains column names

                // Create the initial table with column names
                createTable(columnNames);


                // Start the API calling process
                callServerAPI(jsonData, columnNames, 1);
            }


            reader.readAsArrayBuffer(file);
        }
    }

    function createTable(columnNames) {
        document.getElementById("tableContainer").innerHTML="";
        var element = document.getElementById("statusTable");
        if (element !== null) {
            element.remove(); // Removes the element from the DOM
        }
        table = document.createElement('table');
        table.id='statusTable';
        table.classList.add('priceListTable');
        var thead = document.createElement('thead');
        var tr = document.createElement('tr');

        originalColumnsCount = columnNames.length;
        // Create table headers with column names
        for (var i = 0; i < columnNames.length; i++) {
            if(columnNames[i]=='Product') isbnCellIndex = i;
            if(columnNames[i]=='Book_Description') continue;
            var th = document.createElement('th');
            th.textContent = columnNames[i];
            tr.appendChild(th);
        }

        // Add an additional column for status updates
        var statusTh = document.createElement('th');
        statusTh.textContent = 'Status';
        tr.appendChild(statusTh);


        for (let i = 0; i < responseFields.length; i++) {
           var additionalField = document.createElement('th');
            additionalField.textContent = responseFields[i];
            tr.appendChild(additionalField);
        }
        thead.appendChild(tr);

        table.appendChild(thead);
        tableContainer.appendChild(table);
    }

    function updateRowStatus(rowIndex, status,information,additionalInformation) {
        var row = table.rows[rowIndex];


        // Clear existing status classes
        row.classList.remove('processing', 'ok', 'error');
        if (status === 'OK') {
            row.classList.add('ok');
            if(addAllRows) failedRows.push(row);
         } else {
             row.classList.add('error');
            failedRows.push(row);
        }

        // Update the status column text
       var statusCell = row.cells[originalColumnsCount];
        statusCell.textContent = information;
        if(additionalInformation!=null){
            for (let i = 0; i < responseFields.length; i++) {
                var additionInfoCell = row.cells[originalColumnsCount+i+1];
                additionInfoCell.textContent = additionalInformation[responseFields[i]];
            }

        }
    }

    function callServerAPI(jsonData, columnNames, rowIndex) {
        $("#exportButton").show();

        // Stop the process if the rowIndex exceeds the jsonData length
        if (rowIndex >= jsonData.length) {
            exportFailedRows();
             return;
        }

        var row = jsonData[rowIndex];
        var rowData = {};

        // Iterate through each cell in the row and build the row data object
        for (var j = 0; j < row.length; j++) {
            var columnName = columnNames[j];
            var cellValue = row[j];
            rowData[columnName] = cellValue;
        }


        // Check if all columns are empty in the current row
        var allColumnsEmpty = Object.values(rowData).every(cellValue => cellValue === "");

        // Stop the process if all columns are empty
        if (allColumnsEmpty) {
            exportFailedRows();
           return;
        }

        // Update the current row as processing
        var newRow = table.insertRow();
        newRow.classList.add('processing');
        var mandatoryFieldsPresent = true;
        var missingMandatoryFields= "";
        for (var i = 0; i < columnNames.length; i++) {
            if(columnNames[i]=='Book_Description') continue;
            var cell = newRow.insertCell();
            cell.textContent = row[i];
            if(mandatoryFields.indexOf(columnNames[i])>-1){
                if(!cell.textContent){
                    mandatoryFieldsPresent=false;
                    missingMandatoryFields +=" "+columnNames[i];
                }
            }
        }

        // Add the status cell for the processing row
        var statusCell = newRow.insertCell();
        for (let i = 0; i < responseFields.length; i++) {
            newRow.insertCell();
        }
        if(mandatoryFieldsPresent) {
            statusCell.textContent = 'Processing';

            // Call the server API with the row data
            callServerAPIInternal(rowData)
                .then(response => {
                    // Handle the server API response as needed
                    // Update the row status based on the API response
                    resId = response.additionalInformation.resId;
                    updateRowStatus(rowIndex, response.status,response.information,response.additionalInformation);

                    // Call the next API immediately
                    callServerAPI(jsonData, columnNames, rowIndex + 1);
                })
                .catch(error => {
                    // Handle any errors that occur during the API call
                    console.error('Error:', error);

                    // Update the row status as error
                    updateRowStatus(rowIndex, 'Error',response.information,response.additionalInformation);

                    // Call the next API immediately even if an error occurred
                    callServerAPI(jsonData, columnNames, rowIndex + 1);
                });
        }else{
            updateRowStatus(rowIndex, 'Error',missingMandatoryFields+" missing",null);
            callServerAPI(jsonData, columnNames, rowIndex + 1);
        }
    }

    function exportFailedRows() {
        createErrorTable(columnNames);
        var row;
        var isbnCell;
        var isbnCellContent;
       for(var i=0;i<failedRows.length;i++){
            row = failedRows[i];
            if(isbnCellIndex!=-1){
            isbnCell =  row.cells[isbnCellIndex];
            isbnCellContent = isbnCell.textContent;
                if(isbnCellContent.indexOf(',')>-1) {
                     row.cells[isbnCellIndex].textContent = isbnCellContent.replace(',',' , ');

                }

            }
           errorTable.appendChild(failedRows[i]);
        }

        var workbook = XLSX.utils.book_new();
        var worksheet = XLSX.utils.table_to_sheet(document.getElementById('errorStatusTable'));
        for (var cellAddress in worksheet) {
            if (worksheet.hasOwnProperty(cellAddress)) {
                var cell = worksheet[cellAddress];
                if (cell.t === 'n') {
                    cell.t = 's'; // Change the cell type to "s" (string)
                    cell.z = '@'; // Set the format to plain text
                }
            }
        }

        // Default column width
        var defaultColumnWidth = 20;

        // Set column widths and allow text to wrap
        var columnWidths = worksheet['!cols'] || [];

        for (var i = 0; i < finalColumnsCount; i++) {
            columnWidths.push({ wch: defaultColumnWidth, wrapText: true });
        }
        worksheet['!cols'] = columnWidths;





        XLSX.utils.book_append_sheet(workbook, worksheet, 'Processed Rows');
        XLSX.writeFile(workbook, 'results.xlsx');
    }

    var exportButton = document.getElementById('exportButton');
    exportButton.addEventListener('click', exportFailedRows);



    function createErrorTable(columnNames) {
        document.getElementById("errorTableContainer").innerHTML="";
        var element = document.getElementById("errorStatusTable");
        if (element !== null) {
            element.remove(); // Removes the element from the DOM
        }
        errorTable = document.createElement('table');
        errorTable.id='errorStatusTable';
        errorTable.classList.add('priceListTable');
        var thead = document.createElement('thead');
        var tr = document.createElement('tr');

        // Create table headers with column names
        for (var i = 0; i < columnNames.length; i++) {
            var th = document.createElement('th');
            th.textContent = columnNames[i];
            tr.appendChild(th);
        }

        // Add an additional column for status updates
        var statusTh = document.createElement('th');
        statusTh.textContent = 'Status';
        tr.appendChild(statusTh);
        finalColumnsCount = columnNames.length+1;
        for (let i = 0; i < responseFields.length; i++) {
            finalColumnsCount++;
            var additionalField = document.createElement('th');
            additionalField.textContent = responseFields[i];
            tr.appendChild(additionalField);
        }
        thead.appendChild(tr);
        errorTable.appendChild(thead);

        errorTableContainer.appendChild(errorTable);
    }
</script>

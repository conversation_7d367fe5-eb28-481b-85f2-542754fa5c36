package com.wonderslate.data

import com.wonderslate.cache.DataProviderService
import com.wonderslate.learn.FlowSegment
import com.wonderslate.log.GptDefaultCreateLog
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.Publishers
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonBuilder
import groovy.json.JsonSlurper
import grails.converters.JSON
import com.wonderslate.publish.PromptTemplateMst
import com.wonderslate.publish.PromptTemplateDtl

class AutogptController {
    DataProviderService dataProviderService
    def redisService
    PromptService promptService
    GptLogService gptLogService
    def springSecurityService
    ResourceCreatorService resourceCreatorService

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def index() {
        //list of prompts sort by parentPromptType,promptLabel
        List prompts = Prompts.findAll([sort: [promptLabel: 'asc']])
        if (redisService.("chapters_" + params.bookId) == null) {
            dataProviderService.getChaptersList(new Long(params.bookId));
        }
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        List chaptersList = new JsonSlurper().parseText(redisService.("chapters_" + params.bookId))
        String gptServerUrl = promptService.getGPTServerUrl(request)+"/retrieveDataAdmin"
        List promptTemplates = PromptTemplateMst.findAll([sort: [name: 'asc']])
        [prompts: prompts, chaptersList: chaptersList,title: "AutoGPT",booksMst:booksMst,gptServerUrl:gptServerUrl,
         username:springSecurityService.currentUser.username,promptTemplates:promptTemplates]
    }

    @Secured(['ROLE_USER']) @Transactional
    def setUpChapter(){
        String status
        //get the ResourceDtl for the given chapterId, resType="Notes, resLink ending with .pdf and gptResourceType is null"
        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResLinkLikeAndGptResourceTypeIsNull(new Integer(params.chapterId),"Notes","%.pdf")
        List<GptDefaultCreateLog> resources = null
        String namespace
        if(resourceDtl!=null){
            //first check if the chapter is already setup
            namespace = resourceDtl.chapterId+"_"+resourceDtl.id
            request.setAttribute("gptAdmin","true")
            status = "uploaded"

            List resList = GptDefaultCreateLog.findAllByReadingMaterialResId(resourceDtl.id)
            //collect promptType,id to resources list
            resources = resList.collect { resource ->
                return [id:resource.id,
                        promptType: resource.promptType,
                        promptLabel:resource.promptLabel,
                ]
            }
        }else{
            status= "Resource not found"
        }
        def json = [status:status,readingMaterialResId:resourceDtl!=null?resourceDtl.id:null,createdResources:resources,namespace: namespace]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def createAndUpdateGPTContent() {
        println("***** reallly did it not compile and lets try again")
        if(params.readingMaterialResId==null){
            ResourceDtl rd = ResourceDtl.findByChapterIdAndResTypeAndGptResourceTypeIsNull(new Integer(params.chapterId), "Notes")
            params.readingMaterialResId = ""+rd.id
            params.namespace= params.chapterId+"_"+rd.id
        }
        println("params: "+params)
        Prompts prompt = Prompts.findByPromptType(params.promptType)
        boolean createResource = true
        if ("true".equals(redisService.get("chapter_" + params.readingMaterialResId + "_" + params.promptType))) {
            def json = [response: "In process"]
            render json as JSON
        } else {
            //create a key in redis chaptername and prompt type and it should be there for 20 minutes
            if(prompt==null){
                def json = [response: "Prompt not found"]
                render json as JSON
            }
            else {
                if ("mcqs".equals(prompt.parentPromptType) || "mcq".equals(prompt.parentPromptType) || "qna".equals(prompt.parentPromptType) || "pns".equals(prompt.parentPromptType)) {
                    ResourceDtl readingMaterialResourceDtl = dataProviderService.getResourceDtl(new Integer(params.readingMaterialResId))
                    if(readingMaterialResourceDtl.mcqTypes!=null){
                        if(readingMaterialResourceDtl.mcqTypes.contains(prompt.promptType)){
                            createResource = false
                            def json = [response: "Created"]
                            render json as JSON
                        }
                    }
                    if(readingMaterialResourceDtl.qaTypes!=null){
                        if(readingMaterialResourceDtl.qaTypes.contains(prompt.promptType)){
                            createResource = false
                            def json = [response: "Created"]
                            render json as JSON
                        }
                    }

                }
                if (createResource) {
                    if ("zcleanup".equals(params.promptType)) {
                        String response = "MCQs:"
                        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndGptResourceTypeIsNotNull(new Integer(params.chapterId), "Multiple Choice Questions")
                        if (resourceDtl != null)
                            response += deleteDuplicateQuestions(resourceDtl) + "<br>QnA:"
                        resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndGptResourceTypeIsNotNull(new Integer(params.chapterId), "QA")
                        if (resourceDtl != null)
                            response += deleteDuplicateQuestions(resourceDtl)
                        def json = [response: response]
                        render json as JSON
                    } else {
                        String redisKey = "chapter_" + params.readingMaterialResId + "_" + params.promptType
                        redisService.set(redisKey, "true")
                        redisService.expire(redisKey, 1200)
                        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Integer(params.readingMaterialResId))
                        def returnValue = promptService.createAndUpdateGPTContent(params.namespace, params.readingMaterialResId, request, prompt, session)
                        redisService.set(redisKey, "done")
                        gptLogService.getGPTResources(new Long(params.readingMaterialResId))
                        def json = [response: returnValue]
                        render json as JSON
                    }
                }
            }
        }

    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def autoAddGptContent(){
        def requestBody = request.JSON
        String returnResponse = promptService.autoAddGptContent(session,requestBody.resType,requestBody.readingMaterialResId,request,requestBody,requestBody.promptLabel,requestBody.query,requestBody.resType)
        String redisKey = "chapter_" + requestBody.readingMaterialResId + "_" + requestBody.promptType
        redisService.set(redisKey, "done")
        gptLogService.getGPTResources(new Long(""+requestBody.readingMaterialResId))
        def json = [response:returnResponse]
        render json as JSON
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def promptTemplateManager(){
        List prompts = Prompts.findAll([sort: [parentPromptType: 'asc', promptLabel: 'asc']])
        List promptTemplates = PromptTemplateMst.findAll([sort: [name: 'asc']])
        [prompts: prompts,title: "Prompt Manager",promptTemplates:promptTemplates]
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def addPromptTemplate(){
        PromptTemplateMst promptTemplateMst = new PromptTemplateMst(name:params.templateName,siteId:new Integer(""+session["siteId"]))
        promptTemplateMst.save(flush:true)
        String [] promptIds = params.promptIds.split(",")
        promptIds.each { promptId ->
            Prompts prompt = Prompts.findByPromptType(promptId)
            PromptTemplateDtl promptTemplateDtl = new PromptTemplateDtl(templateId:promptTemplateMst.id,promptType: promptId)
            promptTemplateDtl.save(flush:true)
        }
        def json = [response:"success"]
        render json as JSON
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def editPromptTemplate(){
        PromptTemplateMst promptTemplateMst = PromptTemplateMst.get(new Integer(params.templateId))
        promptTemplateMst.name = params.templateName
        promptTemplateMst.save(flush:true)
        PromptTemplateDtl.executeUpdate("delete from PromptTemplateDtl where templateId = ?",new Integer(""+promptTemplateMst.id))
        String [] promptIds = params.promptIds.split(",")
        promptIds.each { promptId ->
            PromptTemplateDtl promptTemplateDtl = new PromptTemplateDtl(templateId:promptTemplateMst.id,promptType:promptId)
            promptTemplateDtl.save(flush:true)
        }
        def json = [response:"success"]
        render json as JSON
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def deletePromptTemplate(){
        PromptTemplateMst promptTemplateMst = PromptTemplateMst.get(new Integer(params.templateId))
        PromptTemplateDtl.executeUpdate("delete from PromptTemplateDtl where templateId = ?",new Integer(""+promptTemplateMst.id))
        promptTemplateMst.delete(flush:true)
        def json = [response:"success"]
        render json as JSON
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def getTemplateDetails(){
        PromptTemplateMst promptTemplateMst = PromptTemplateMst.get(new Integer(params.templateId))
        List promptTemplateDtls = PromptTemplateDtl.findAllByTemplateId(new Integer(params.templateId))
        //get the comma separated promptType
        String prompts = promptTemplateDtls.collect { promptTemplateDtl ->
            return promptTemplateDtl.promptType
        }.join(",")
        def json = [templateName:promptTemplateMst.name,prompts:prompts]
        render json as JSON
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def copyChapters(){
        String[] bookIds = params.bookIds.split(",")
        println("bookIds: "+bookIds)
        //loop bookIds
        bookIds.each { bookId ->
            println("bookId: "+bookId)
            List chapters = ChaptersMst.findAllByBookId(new Integer(bookId))
            chapters.each {chapter ->
                resourceCreatorService.copyChapter(""+chapter.id,""+params.destBookId,new Integer(""+session["siteId"]))
            }
            resourceCreatorService.copyGptContents(""+params.destBookId)
            }
        def json = [response:"success"]
        render json as JSON
        }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def deleteDuplicateQuestions(ResourceDtl resourceDtl){
        try {
            Prompts prompts = Prompts.findByPromptType("zcleanup")
            List questions = ObjectiveMst.findAllByQuizId(new Integer("" + resourceDtl.resLink))
            int initialQuestions = questions.size()
            def file = new File("duplicateQuestions" + resourceDtl.resLink + ".txt");
            file.append("[")
            //loop through the questions
            questions.each { question ->
                //get the question
                String questionText = question.question
                file.append("\"" + question.id + "~" + questionText.replace('\"', '') + "\",")
            }
            //remove the last comma
            file.text = file.text.substring(0, file.text.length() - 1)
            file.append("]")

            URL url = new URL(promptService.getGPTServerUrl(request) + "/retrieveDataAdminText")
            HttpURLConnection conn = (HttpURLConnection) url.openConnection()
            conn.setRequestMethod("POST")

            String boundary = "===" + System.currentTimeMillis() + "==="
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary)
            conn.setDoOutput(true)

            String prompt = prompts.basePrompt
            try {
                OutputStream output = conn.getOutputStream();
                PrintWriter writer = new PrintWriter(new OutputStreamWriter(output, "UTF-8"), true)
                writer.append("\r\n").flush()
                writer.append("--" + boundary).append("\r\n")
                writer.append("Content-Disposition: form-data; name=\"prompt\"").append("\r\n")
                writer.append("Content-Type: text/plain; charset=UTF-8").append("\r\n")
                writer.append("\r\n").flush()
                output.write(prompt.getBytes("UTF-8"))
                output.flush()

                // Send file.
                writer.append("\r\n").flush()
                writer.append("--" + boundary).append("\r\n")
                writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"file.txt\"").append("\r\n")
                writer.append("Content-Type: text/plain; charset=UTF-8").append("\r\n")
                writer.append("\r\n").flush()
                output.write(file.text.getBytes("UTF-8"))
                output.flush()

                // End of multipart/form-data.
                writer.append("\r\n").flush()
                writer.append("--" + boundary + "--").append("\r\n").flush()
            }
            catch (Exception e) {
                e.printStackTrace()
            }

            String response = conn.getInputStream().getText()

            def json = new JsonSlurper().parseText(response)
            String responseAnswer = json.response
            responseAnswer = responseAnswer.replaceAll("`", "")
            responseAnswer = responseAnswer.replaceAll("json", "")
            responseAnswer = responseAnswer.replaceAll("\n", "")
            responseAnswer = responseAnswer.replaceAll("\r", "")
            //next i want to replace ][ with comma
            responseAnswer = responseAnswer.replaceAll("\\]\\[", ",")
            // replace },] with }]
            responseAnswer = responseAnswer.replaceAll("},]", "}]")

            //replace  .^ with blank
            responseAnswer = responseAnswer.replaceAll("\\.\\^", "")


            def questionsList = new JsonSlurper().parseText(responseAnswer)

            String outputQuestions = ""
            //loop questionsList
            questionsList.each { question ->
                println(question)
                //get the question id
                String[] questionIds = question.ids.split(",")
                if (questionIds.size() > 1) {
                    //loop questionIds
                    outputQuestions += questionIds[0] + ","
                    String ids = question.ids.substring(question.ids.indexOf(",") + 1)
                    ObjectiveMst.executeUpdate("delete from ObjectiveMst where id in (" + ids + ")")
                }
            }

            //delete the file
            file.delete()
            redisService.("quiz_" + resourceDtl.id) = null
            questions = ObjectiveMst.findAllByQuizId(new Integer("" + resourceDtl.resLink))
            return "Original Questions: " + initialQuestions + "\nLeft Questions: " + questions.size()
        }catch (Exception e){
            return "Exception happened: "+e.getMessage()
        }
    }

    def duplicateFixer(){

    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def updateMissingResources(){
        String bookId = params.bookId
        List chapters = ChaptersMst.findAllByBookId(new Integer(bookId))
        int count=0
        chapters.each {chapter ->
            ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndGptResourceTypeIsNull(new Integer(""+chapter.id),"Notes")
            if(resourceDtl!=null){
                List resources = ResourceDtl.findAllByChapterIdAndGptResourceTypeIsNotNull(new Integer(""+chapter.id))
                resources.each{resource ->
                    if(resource.parentId!=null) {
                        GptDefaultCreateLog newGptLog = GptDefaultCreateLog.findByResId(resource.id)
                        if(newGptLog==null) {
                            GptDefaultCreateLog gptLog = GptDefaultCreateLog.findByResId(resource.parentId)
                            //create new GptDefaultCreateLog and copy the content
                            if(gptLog!=null) {
                                newGptLog = new GptDefaultCreateLog(username: gptLog.username, prompt: gptLog.prompt, response: gptLog.response, response2: gptLog.response2, promptType: gptLog.promptType, resId: resource.id, readingMaterialResId: resourceDtl.id, promptLabel: gptLog.promptLabel)
                                newGptLog.save(flush: true)
                                count++
                            }
                        }
                    }
                }
                gptLogService.getGPTResources(new Long(""+resourceDtl.id))
            }

        }
        render "total "+count
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def getChapterDetails(){
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        if(redisService.("allChapterDetails_"+params.bookId)==null) {
            dataProviderService.getAllChapterDetails(new Long(params.bookId))
        }
        List chaptersList = new JsonSlurper().parseText(redisService.("allChapterDetails_" +params.bookId))
        List chapterDetails = chaptersList.collect{it->
            if(it !=null){
                if(it.resType =="Notes" && it.link.endsWith(".pdf") && it.link !="blank"){
                    return it
                }
            }
        }.findAll { it != null }
        chapterDetails = chapterDetails.sort { it.chapterId }
        def json = [bookTitle:booksMst.title,chapterDetails:chapterDetails]
        render json as JSON
    }

    @Secured(['ROLE_GPT_MANAGER'])
    def kindleTemplate(){

    }

    @Secured(['ROLE_USER']) @Transactional
    def getTeachingCoachLevel2(){
        String chapterId = params.chapterId
        ResourceDtl readingMaterialResourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndGptResourceType(new Integer(chapterId),"teaching_coach")
        String response
        if(resourceDtl!=null){
            FlowSegment flowSegment = FlowSegment.findByResourceDtlIdAndSlug(new Long(""+resourceDtl.id),request.getParameter("slug"))
            if(flowSegment==null){
                Prompts prompt = Prompts.findByPromptType("teaching_coach_slug")
                String query = prompt.basePrompt.replace("slug_type",request.getParameter("slug"))
               def jsonResponse =   promptService.runPrompt(readingMaterialResourceDtl.resLink,params.chapterId+"_"+params.resId, query,"teaching_coach_slug",request)
                 flowSegment = new FlowSegment(resourceDtlId: resourceDtl.id, slug: request.getParameter("slug"), detailJson: jsonResponse.answer)
                 response = jsonResponse.answer
                 flowSegment.save(failOnError: true, flush: true)
            }
            else{
                response = flowSegment.detailJson
            }
        }
        // if response is not null and if it is enclosed with ```json and ``` then remove it
        if(response!=null){
            if(response.startsWith("```json")&&response.endsWith("```")){
                response = response.substring(8,response.length()-3)
            }
        }
        def json = [response:response]
        render json as JSON
    }

    @Transactional
    def exerciseCollector() {
        String allResponse = ""
        try {
            // Check if resId parameter is provided
            if (!params.resId) {
                def json = [status: "ERROR", message: "resId parameter is required"]
                render json as JSON
                return
            }

            // Get ResourceDtl record using resId
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
            ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
            //check if there is instance of resourceDtl for given chapterId and resourceName starting with "Exercise"
            ResourceDtl exerciseResourceDtl = ResourceDtl.findByChapterIdAndResourceNameLike(new Integer(chaptersMst.id),"Exercise%")
            if(exerciseResourceDtl!=null) {
                BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
                if (!resourceDtl) {
                    def json = [status: "ERROR", message: "Resource not found for resId: " + params.resId]
                    render json as JSON
                    return
                }

                // Check if extractPath is not empty
                if (!resourceDtl.extractPath || resourceDtl.extractPath.trim().isEmpty()) {
                    def json = [status: "ERROR", message: "extractPath is empty for this resource"]
                    render json as JSON
                    return
                }

                // Construct full file path
                String filePath = grailsApplication.config.grails.basedir.path + "/" + resourceDtl.extractPath
                File textFile = new File(filePath)

                if (!textFile.exists()) {
                    def json = [status: "ERROR", message: "Text file not found at path: " + filePath]
                    render json as JSON
                    return
                }

                // Read file content in chunks of 20000 characters
                def chunkSize = 20000
                def chunkNumber = 1

                ResourceDtl resourceDtlInstance = null
                textFile.withReader('UTF-8') { reader ->
                    char[] buffer = new char[chunkSize]
                    StringBuilder currentChunk = new StringBuilder()
                    int charsRead

                    while ((charsRead = reader.read(buffer, 0, chunkSize)) != -1) {
                        currentChunk.append(buffer, 0, charsRead)

                        // If we have read exactly chunkSize characters, look for next paragraph break
                        if (charsRead == chunkSize) {
                            // Read additional characters until we find a paragraph break
                            int nextChar
                            while ((nextChar = reader.read()) != -1) {
                                currentChunk.append((char) nextChar)

                                // Check for paragraph break (double newline)
                                String content = currentChunk.toString()
                                if (content.endsWith("\n\n") || content.endsWith("\r\n\r\n")) {
                                    break
                                }
                            }
                        }


                        def response = examplesAndExercisesExtractor(resourceDtl, currentChunk.toString())

                        String question, answer
                        //sometimes response is "[]\n[]\n" we have check for that also
                        if (response != null && response.size() > 0 && !"[]\n[]\n".equals(response)) {
                            //for each response get the question element and print it
                            if (resourceDtlInstance == null) {
                                QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
                                quizIdGenerator.save()

                                resourceDtlInstance = new ResourceDtl()
                                resourceDtlInstance.resLink = quizIdGenerator.id
                                resourceDtlInstance.createdBy = springSecurityService.currentUser.username
                                resourceDtlInstance.resType = "QA"
                                resourceDtlInstance.chapterId = resourceDtl.chapterId
                                resourceDtlInstance.resourceName = "Exercise Solutions"
                                resourceDtlInstance.save(failOnError: true, flush: true)


                            }
                            try {
                                response.each {
                                    question = it.question
                                    def jsonAnswer = getSolution(resourceDtl, it.question, chaptersMst.id, booksMst.id)
                                    def json = new JsonSlurper().parseText(jsonAnswer.answer)
                                    answer = json.solution
                                    //add the question and answer to ObjectiveMst
                                    ObjectiveMst om = new ObjectiveMst(quizId: new Integer(resourceDtlInstance.resLink), quizType: "QA", question: it.heading + " " + it.question,
                                            answer: json.solution,
                                            difficultylevel: json.difficultyLevel, qType: json.questionType, answerDescription: json.explanation, bloomType: json.bloomLevel)
                                    om.save(failOnError: true, flush: true)
                                }
                            } catch (Exception e) {
                                println("Exception in exerciseCollector: " + e.message)
                            }

                        }

                        chunkNumber++
                        currentChunk.setLength(0) // Clear the buffer for next chunk
                    }
                }


                dataProviderService.getChaptersList(chaptersMst.bookId);
                def json = [status: "OK", message: "File processed successfully"]
                render json as JSON
            }else{
                def json = [status: "OK", message: "Exercises already created"]
                render json as JSON
            }
        } catch (Exception e) {
            println("Exception in exerciseCollector: " + e.message)
            e.printStackTrace()
            def json = [status: "ERROR", message: "Error processing file: " + e.message]
            render json as JSON
        }
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def examplesAndExercisesExtractor(ResourceDtl resourceDtl,String inputText){
        try {
            Prompts prompts = Prompts.findByPromptType("exerciseExtractor")
            String responseAnswer = getLLMResponse(resourceDtl,inputText,prompts.basePrompt)

            List responseList =null
            try {
                responseList = new JsonSlurper().parseText(responseAnswer)
            }catch (Exception e){
                println("Exception in parsing responseAnswer "+e.getMessage())
            }
            return responseList
        }catch (Exception e){
            return "Exception happened: "+e.getMessage()
        }
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def getSolution(ResourceDtl resourceDtl,question,chapterId,bookId){
        def requestBody = request.JSON
        Prompts prompt = Prompts.findByPromptType("solutionCreator")
        String customPrompt = prompt.basePrompt

        requestBody.put("namespace",resourceDtl.vectorStored)
        requestBody.put("resType","userInput")
        requestBody.put("query",question)
        requestBody.put("chatHistory","")
        requestBody.put("chapterId",chapterId)
        requestBody.put("bookId",bookId)
        requestBody.put("customPrompt", customPrompt)


        URL url = new URL(promptService.getGPTServerUrl(request)+"/retrieveData")

        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
        writer.write(requestBody.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()
        if(responseCode==200){
            def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
            def response = reader.readLine()
            def jsonSlurper = new JsonSlurper()
            def jsonResponse = jsonSlurper.parseText(response)
            return jsonResponse
        }else{  return null}

    }

     def removeExtraCommas(String json) {
        // This regex will find one or more commas (,) followed by zero or more whitespace characters (\s*)
        // and then another comma (,), and replace them with a single comma.
        // It's designed to specifically target consecutive commas.
        // The first pattern `[,]+\s*,` handles cases like `,,` or `,,,`
        // The second pattern `,\s*]` handles cases where a comma might be followed by a closing bracket (e.g., `},,]`)
        // The third pattern `,\s*` would catch any stray comma followed by whitespace, but the above two are more specific to the issue.

        // Let's refine the regex for better handling of your specific problem:
        // We want to match one or more commas that are not immediately followed by a '{' or a ']' or end of string.
        // And replace multiple commas with a single comma, while also cleaning up commas before ']'
        String cleaned = json.replaceAll(",\\s*,+", ","); // Replace multiple commas with a single comma
        cleaned = cleaned.replaceAll(",\\s*]", "]");      // Remove commas just before a closing bracket

        return cleaned;
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def getChapterMetaData(){
        // Get ResourceDtl record using resId
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
        BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
        if (!resourceDtl) {
            def json = [status: "ERROR", message: "Resource not found for resId: " + params.resId]
            render json as JSON
            return
        }

        // Check if extractPath is not empty
        if (!resourceDtl.extractPath || resourceDtl.extractPath.trim().isEmpty()) {
            def json = [status: "ERROR", message: "extractPath is empty for this resource"]
            render json as JSON
            return
        }
        String folderPath = resourceDtl.extractPath.substring(0, resourceDtl.extractPath.lastIndexOf("/"))
        File metadataFile = new File(grailsApplication.config.grails.basedir.path + "/"+folderPath+"/chapterMetadata"+ chaptersMst.id + ".txt")
        if(!metadataFile.exists())  {
            // Construct full file path
            String filePath = grailsApplication.config.grails.basedir.path + "/" + resourceDtl.extractPath
            File textFile = new File(filePath)
            //get the full content of the file to a string
            String fileContent = textFile.text

            Prompts prompts = Prompts.findByPromptType("chapterMetadataExtractor")
            Date start = new Date()
            String response = getLLMResponse(resourceDtl, fileContent, prompts.basePrompt)
            Date end = new Date()
            metadataFile.write(response)

        }
        def json = [status:"OK"]
        render json as JSON
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def questionBankBuilder(){
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
        //find if there is a resourceDtl instance for the given chapterId and resourceName starting with "QuestionBank"
        ResourceDtl questionBankResourceDtl = ResourceDtl.findByChapterIdAndResourceNameLike(new Integer(params.chapterId),"QuestionBank%")
        if(questionBankResourceDtl!=null) {


            List responseList = null
            String allQuestions = ""
            int totalQuestions = 0
            ResourceDtl qaResourceDtl, mcqResourceDtl
            try {
                //get the metadata from file
                String folderPath = resourceDtl.extractPath.substring(0, resourceDtl.extractPath.lastIndexOf("/"))
                File metadataFile = new File(grailsApplication.config.grails.basedir.path + "/" + folderPath + "/chapterMetadata" + chaptersMst.id + ".txt")
                def json = new JsonSlurper().parseText(metadataFile.text)

// Now access the subtopics list
                def subtopics = json.metadata.subtopics

                Prompts prompts = Prompts.findByPromptType("questionBankBuilder")
                // Construct full file path, get the path till the file name in the resourceDtl.extractPath
                String filePath = grailsApplication.config.grails.basedir.path + "/" + resourceDtl.extractPath

                File textFile = new File(filePath)
                String fileContent = textFile.text
                String question, answer

                subtopics.each { subtopic ->

                    String promptText = prompts.basePrompt
                    promptText = promptText.replaceAll("SUBTOPICMETADATA", "" + subtopic)
                    String responseAnswer = getLLMResponse(resourceDtl, fileContent, promptText)
                    try {
                        responseList = new JsonSlurper().parseText(responseAnswer)
                        println("The number of questions in the subtopic " + responseList.size())
                        responseList.each {
                            question = it.question
                            def jsonAnswer = getSolution(resourceDtl, it.question, chaptersMst.id, chaptersMst.bookId)
                            json = new JsonSlurper().parseText(jsonAnswer.answer)
                            println("****** The jsonAnswer is " + jsonAnswer.answer)
                            answer = json.solution
                            allQuestions += question + "<br>" + answer + "<br><br>"
                            if (json.questionType == "MCQ") {
                                if (mcqResourceDtl == null) {
                                    QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
                                    quizIdGenerator.save()

                                    mcqResourceDtl = new ResourceDtl()
                                    mcqResourceDtl.resLink = quizIdGenerator.id
                                    mcqResourceDtl.createdBy = springSecurityService.currentUser.username
                                    mcqResourceDtl.resType = "Multiple Choice Questions"
                                    mcqResourceDtl.chapterId = resourceDtl.chapterId
                                    mcqResourceDtl.resourceName = "QuestionBank MCQs"
                                    mcqResourceDtl.gptResourceType = "mcq"
                                    mcqResourceDtl.save(failOnError: true, flush: true)

                                    GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(resourceDtl.id, "mcq")
                                    if (gptDefaultCreateLog == null) {
                                        gptDefaultCreateLog = new GptDefaultCreateLog(resId: mcqResourceDtl.id, promptType: "mcq", prompt: "Create MCQs (Multiple Choice Questions)", response: "MCQ",
                                                readingMaterialResId: resourceDtl.id, username: springSecurityService.currentUser.username, promptLabel: "Create MCQs (Multiple Choice Questions)")
                                        gptDefaultCreateLog.save(failOnError: true, flush: true)
                                    } else {
                                        gptDefaultCreateLog.resId = mcqResourceDtl.id
                                        gptDefaultCreateLog.save(failOnError: true, flush: true)
                                    }
                                }
                                ObjectiveMst om = new ObjectiveMst(quizId: new Integer(mcqResourceDtl.resLink), quizType: "MCQ", question: json.questionText,
                                        answer: json.solution,
                                        difficultylevel: json.difficultyLevel, qType: json.questionType, answerDescription: json.explanation,
                                        bloomType: json.bloomLevel, subTopic: it.heading,
                                        option1: json.option1,
                                        option2: json.option2,
                                        option3: json.option3,
                                        option4: json.option4,
                                        answer1: json.correctAnswer.equals("option1") ? "Yes" : null,
                                        answer2: json.correctAnswer.equals("option2") ? "Yes" : null,
                                        answer3: json.correctAnswer.equals("option3") ? "Yes" : null,
                                        answer4: json.correctAnswer.equals("option4") ? "Yes" : null)
                                om.save(failOnError: true, flush: true)

                            } else {
                                if (qaResourceDtl == null) {
                                    QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
                                    quizIdGenerator.save()

                                    qaResourceDtl = new ResourceDtl()
                                    qaResourceDtl.resLink = quizIdGenerator.id
                                    qaResourceDtl.createdBy = springSecurityService.currentUser.username
                                    qaResourceDtl.resType = "QA"
                                    qaResourceDtl.chapterId = resourceDtl.chapterId
                                    qaResourceDtl.resourceName = "QuestionBank QnA"
                                    qaResourceDtl.gptResourceType = "qna"
                                    qaResourceDtl.save(failOnError: true, flush: true)
                                    GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(resourceDtl.id, "qna")
                                    if (gptDefaultCreateLog == null) {
                                        gptDefaultCreateLog = new GptDefaultCreateLog(resId: qaResourceDtl.id, promptType: "qna", prompt: "Create Question & Answers", response: "QA",
                                                readingMaterialResId: resourceDtl.id, username: springSecurityService.currentUser.username, promptLabel: "Create Question & Answers")
                                        gptDefaultCreateLog.save(failOnError: true, flush: true)
                                    } else {
                                        gptDefaultCreateLog.resId = qaResourceDtl.id
                                        gptDefaultCreateLog.save(failOnError: true, flush: true)
                                    }
                                }
                                ObjectiveMst om = new ObjectiveMst(quizId: new Integer(qaResourceDtl.resLink), quizType: "QA", question: question,
                                        answer: json.solution,
                                        difficultylevel: json.difficultyLevel, qType: json.questionType, answerDescription: json.explanation,
                                        bloomType: json.bloomLevel, subTopic: it.heading)
                                om.save(failOnError: true, flush: true)
                            }
                            totalQuestions++

                        }

                    } catch (Exception e) {
                        println("Exception in parsing responseAnswer1 " + e.getMessage())
                    }
                }

            } catch (Exception e) {
                println("Exception in parsing responseAnswer2 " + e.getMessage())
            }
            def json = [status: "OK", totalQuestions: totalQuestions]
            render json as JSON
        }else {
            def json = [status: "OK", message: "Question bank already created", totalQuestions: "Not Counted"]
            render json as JSON
        }
    }



    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def getLLMResponse(ResourceDtl resourceDtl,String inputText,String basePrompt){
        try {
            def file = new File("extractData" + resourceDtl.id + ".txt");
            if(file.exists()) file.delete()
            file.createNewFile()
            file.append(inputText);
            URL url = new URL(promptService.getGPTServerUrl(request) + "/retrieveDataAdminText")
            HttpURLConnection conn = (HttpURLConnection) url.openConnection()
            conn.setRequestMethod("POST")

            String boundary = "===" + System.currentTimeMillis() + "==="
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary)
            conn.setDoOutput(true)

            String prompt = basePrompt
            try {
                OutputStream output = conn.getOutputStream();
                PrintWriter writer = new PrintWriter(new OutputStreamWriter(output, "UTF-8"), true)
                writer.append("\r\n").flush()
                writer.append("--" + boundary).append("\r\n")
                writer.append("Content-Disposition: form-data; name=\"prompt\"").append("\r\n")
                writer.append("Content-Type: text/plain; charset=UTF-8").append("\r\n")
                writer.append("\r\n").flush()
                output.write(prompt.getBytes("UTF-8"))
                output.flush()

                // Send file.
                writer.append("\r\n").flush()
                writer.append("--" + boundary).append("\r\n")
                writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"file.txt\"").append("\r\n")
                writer.append("Content-Type: text/plain; charset=UTF-8").append("\r\n")
                writer.append("\r\n").flush()
                output.write(file.text.getBytes("UTF-8"))
                output.flush()

                // End of multipart/form-data.
                writer.append("\r\n").flush()
                writer.append("--" + boundary + "--").append("\r\n").flush()
            }
            catch (Exception e) {
                e.printStackTrace()
            }
            String response = conn.getInputStream().getText()
            def json = new JsonSlurper().parseText(response)
            String responseAnswer = json.response
            responseAnswer = responseAnswer.replaceAll("`", "")
            responseAnswer = responseAnswer.replaceAll("json", "")
            responseAnswer = responseAnswer.replaceAll("\n", "")
            responseAnswer = responseAnswer.replaceAll("\r", "")
            //next i want to replace ][ with comma
            responseAnswer = responseAnswer.replaceAll("\\]\\[", ",")
            // replace },] with }]
            responseAnswer = responseAnswer.replaceAll("},]", "}]")

            //replace  .^ with blank
            responseAnswer = responseAnswer.replaceAll("\\.\\^", "")
            responseAnswer = removeExtraCommas(responseAnswer)
            return responseAnswer

            //delete the file
            file.delete()
            return response
        }catch (Exception e){
            return "Exception happened: "+e.getMessage()
        }
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def autogptTask(){
        List availableChapters = ChaptersMst.findAllByBookId(new Integer(params.bookId))
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        [availableChapters:availableChapters, bookTitle:booksMst.title, title:"AutoGPT Task Manager"]
    }


}

.preview-book-container {
  min-height: calc(100vh - 204px);
  margin: 24px auto;
}
.wonderslate-breadcrumb {
  font-size: $font-size-small;
  background: transparent;
  padding: 0 15px;
  margin-bottom: 0;
  li {
    color: $black-color-darker;
    font-weight: 300;
    letter-spacing: 0.023em;
    a {
      color: $black-color-darker;
      &:hover {
        color: $orange-color;
      }
    }
    &.active {
      color: $black-color-darker;
      font-weight: 500; 
    }
    &:before {
      content: '';
      color: $black-color-darker;
      padding: 0;
    }
  }
  li+li:before {
    content: '';
    color: $black-color-darker;
    padding: 0;
  }
}
.preview-book-wrapper {
  padding: 0 40px;
  margin-top: 40px;
}
.preview-book-image {
  max-width: 160px;
  padding: 0;
  img {
    border-radius: 4px;
    box-shadow: $book-shadow;
  }
}
.book-preview-detail {
  padding: 8px 24px;
}
.preview-book-name {
  font-family: $font-family-base;
  font-size: $font-size-xl;
  font-weight: 500;
  margin: 0 0 16px 0;
}
.author-name {
  font-size: $font-size-medium;
  font-weight: 300;
  color: $black-color-light-rgba;
  line-height: 21px;
  margin: 0 0 16px 0;
}
.offer-price {
  font-size: 20px;
  font-weight: 400;
  color: $maroon-color;
  letter-spacing: 0.01em;
}
.preview-book-btns {
  margin-top: 24px;
}

.btn-book-preview {
  color: $orange-color;
  font-weight: 500;
  line-height: normal;
  text-align: center;
  letter-spacing: 0.01em;
  background: $white-color;
  border: $button-border;
  border-radius: 4px;
  padding: 11px 0;
  margin-bottom: 16px;
  &:hover {
    color: $orange-color;
    box-shadow: $book-shadow;
  }
  &:focus {
    color: $orange-color;
  }
  &:active {
    color: $orange-color;
  }
}
.btn-book-buy {
  color: $white-color;
  font-weight: 500;
  line-height: normal;
  text-align: center;
  letter-spacing: 0.01em;
  background: -webkit-linear-gradient(right, #30C465 0%, #3AE878 100%);
  background: -o-linear-gradient(right, #30C465 0%, #3AE878 100%);
  background: linear-gradient(to left, #30C465 0%, #3AE878 100%);
  border: 0;
  border-radius: 4px;
  padding: 11px 0;
  &:hover {
    color: $white-color;
    box-shadow: $book-shadow;
    background: -webkit-linear-gradient(right, #30C465 0%, #3AE878 100%);
    background: -o-linear-gradient(right, #30C465 0%, #3AE878 100%);
    background: linear-gradient(to left, #30C465 0%, #3AE878 100%);
  }
  &:focus {
    color: $white-color;
  }
  &:active {
    color: $white-color;
  }
}
.preview-book-desc {
  width: 100%;
  background-color: $white-color !important;
//  background: url('../images/wonderslate/book-desc-pattern.png');
  background-repeat: no-repeat;
  background-position: 64px 51px;
  background-size: 90%;
  padding: 40px 25px;
  margin-top: -40px;
  margin-left: 0;
  border-radius: 4px;
}
.book-preview-desc {
  padding-left: 24px;
  margin-top: -22px;
  margin-bottom: 8px;
}
.book-desc-legend {
  font-family: $font-family-abril;
  font-size: $font-size-xl;
  letter-spacing: 0.01em;
  margin: 0 0 8px 0;
}
.book-desc {
  font-family: $font-family-work-sans;
  font-size: $font-size-medium;
  line-height: 21px;
  max-height: 166px;
  overflow: hidden;
}
.show-full-desc {
  position: absolute;
  bottom: 0;
  font-size: 24px;
  text-align: center;
  width: 100%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) -25.75%, rgba(255, 255, 255, 0.91) 36.14%, #FFFFFF 78.54%);
  .icon-back {
    display: inline-block;
    -webkit-transform: rotate(-90deg);
       -moz-transform: rotate(-90deg);
        -ms-transform: rotate(-90deg);
         -o-transform: rotate(-90deg);
            transform: rotate(-90deg);
  }
  &:hover {
    text-decoration: none;
    color: #444;
  }
  &:focus {
    text-decoration: none;
    color: #444;
  }
  &:active {
    text-decoration: none;
    color: #444;
  }
}
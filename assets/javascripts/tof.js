function showNextQuestionTOF(index){

    var questionDiv = document.createElement('div');
    var questionNumberDiv = document.createElement('div');
    quiz.currentIndex = index;
    questionNumberDiv.innerHTML ="<b>"+ (index+1)+" / "+(quiz.questions.length)+"</b>";
    questionDiv.innerHTML= "<div class='col-md-1' style='width:4%;padding-right:0;padding-left:2px;'><b>"+(index+1)+"."+"</b>"+"</div>"+"<span class='col-md-11' style='padding-left:0;'><b>"+quiz.questions[index].ps;+"</b></span>";

    $("#tofModal").find('.question').text(""); // to remove old ones
    $("#tofModal").find('#question')[0].appendChild(questionDiv);
    resetTOFInput(index);
    MathJax.Hub.Queue(["Typeset",MathJax.Hub]);


}

function saveAnswerTOF(index) {
    if($('#radio-btn-true').is(':checked')){
        userAnswers[index]['ans'] = "true";
        return true;
    }else if($('#radio-btn-false').is(':checked')){
        userAnswers[index]['ans'] = "false";
        return true;
    }else {
        userAnswers[index]['ans'] = "skipped";
        return false;
    }

}
function getScoreTOF(answers) {
    var length = userAnswers.length;
    var score={};
    score.correctAnswers = 0;
    score.wrongAnswers=0;
    score.skipped=0;
    score.totalQuesions=answers.length;
    for(var i = 0; i < length; ++i){
        if(userAnswers[i]['ans'] == "skipped") {
            score.skipped++;
            userAnswers[i]['correctAnswer']="skipped";
        }
       else if(userAnswers[i]['ans'] === answers[i].ans1){
            score.correctAnswers++;
            userAnswers[i]['correctAnswer']="true";
        }
        else {
           score.wrongAnswers++;
            userAnswers[i]['correctAnswer']="false";
        }

        userAnswers[i]['id']=answers[i]['id'];
        //console.log( "the answers[i]['id']="+answers[i]['id']+" and userAnswers[i]['id']="+userAnswers[i]['id']);
    }
    return score;
}
function scoreAndShowAnswersTOF(data){
    var readMode=false;
    var msgStr;
    quiz.answers=data.results;
    quiz.score = 0;
    if(quiz.mode == "read"){
        readMode = true;
        quiz.questions = data.results;
        quiz.userAnswers = quiz.answers;
        $("#tofModal").modal('show');
        modalName = "tofModal";
    }else {
        var score = getScoreTOF(quiz.answers);
        quiz.score = score.correctAnswers;
        msgStr = scoreMessage(score,quiz.questions.length);
        quiz.userAnswers = userAnswers;
        updateWithQuizAnswers(userAnswers,score);
    }
    var scoreDiv = document.createElement('div');
    var answerDiv = document.createElement('div');


    $("#tofModal").find('.questionsection').css("display","none");
    $("#tofModal").find('.answersection').css("display","block");
    $("#tofModal").find('.score-container').text(""); // to remove old ones
    if(!readMode) {
        scoreDiv.innerHTML = msgStr;
        $("#tofModal").find('#score-container')[0].appendChild(scoreDiv);
    }
    var answerStr="";
    var answerClass="";
    var imageName="";
    var userAnswer="";

    for(var index = 0; index < quiz.questions.length; ++index) {
        if(!readMode) {
            userAnswer = quiz.userAnswers[index]['ans'];
			
            if(userAnswer=='skipped'){
                userAnswer='Question Skipped';
                answerClass='wrong-answer-by-user';
                imageName='';
            } else {
                if (userAnswers[index]['ans'].toLowerCase() == "true") answerClass = "true-selected-box";
                else  answerClass = "wrong-answer-by-user";

                if (quiz.answers[index].ans1.toLowerCase() == userAnswers[index]['ans'].toLowerCase()) {
                    imageName = "correct.png";
                } else {
                    imageName = "wrong.png";
                }
            }
        } else {
			if(quiz.answers[index].ans1.toLowerCase()=="true") answerClass = "true-selected-box";
			else  answerClass = "wrong-answer-by-user";

            userAnswer = quiz.answers[index].ans1;
        }
		
		//sage specific
		if(siteId==4 && userAnswer=='') continue;	

        if(siteId !==6) {
            quiz.questions[index].ps = quiz.questions[index].ps.replace('<p>', '');
            quiz.questions[index].ps = quiz.questions[index].ps.replace('</p>', '');
            answerStr += "<div class='col-md-12' id='sum-question' style='padding-left: 0;'>"+"<b>"+(index+1)+") "+"</b>"+quiz.questions[index].ps+"</div>"+
            "<div class='correct-answer-learn col-md-12' style='border-bottom: 0; margin-bottom:0;'>"+
                "<p class='correct-answer "+answerClass+"'>"+"Your Answer:"+"</p>"+
                "<p class='correct-answer "+answerClass+"'>"+userAnswer+"</p>"+
                "</div>"+
                "<div class='correct-answer-learn col-md-12'>"+
                "<p class='correct-answer-label correct-answer-by-user'>"+"Correct Answer:"+"</p>"+
                "<p class='correct-answer correct-answer-by-user'>"+quiz.answers[index].ans1+"</p>"+
            "</div>";
        }

        
            
        // if(!readMode&&!imageName=='') {
        //     if(siteId!==6) {
        //         answerStr += "&nbsp;<img style='height: 33px;' src='/assets/" + imageName + "'>" + "</span>";
        //     }
        // }
            
        answerStr +=  "</span>"+"</div>";
            
        if(!readMode) {
            if(siteId ==6) {
                answerStr +="<div class='row' style='position:relative; margin-top: 30px; border-bottom: 1px solid #eeeeee;'>"+
                    "<div class='col-md-9' style='margin-bottom: 20px; margin-top: 10px; border-right: 2px solid #8a8988;'>"+
                    "<div class='col-md-1' style='width:4%;padding-right:0;padding-left:2px;'><b>"+(index+1)+"."+"</b>"+"</div>" +
                    "<span class='col-md-11' style='padding-left:0; margin-bottom: 20px;'>"+quiz.questions[index].ps+"</span>"+
                    "<img style='height: 33px; margin-left:23px;' src='/assets/" + imageName + "'>"+
                    "<span style='position:absolute; margin-left: 40px;' class='"+answerClass+"'>&nbsp;</span>"+
                    "<span style='text-transform: capitalize; margin-left: 80px; font-weight: 700;'>"+userAnswer+"</span>"+
                    "</div>"+
                    "<div class='col-md-3 text-center correct-answers' style='margin-top: 10px;'>" +
                    "<p style='font-weight: 700;'>Correct Answer</p>" +
                    "<p style='text-transform: capitalize;'>" + quiz.answers[index].ans1 + "</p>" +
                    "</div>"+ 
                    "</div>";

            }
            
        }
        if(siteId !==6) {
          answerStr += "</div>"+"<hr class='quiz-hr'>";
        }
    }

    answerDiv.innerHTML=answerStr;
    $("#tofModal").find('#score-container').text(""); // to remove old ones
    $("#tofModal").find('#score-container')[0].appendChild(answerDiv);
    //initializing stuff..if the same quiz is loaded again
    quiz.currentIndex=0;
    quiz.mode="play";
    resetUserAnswers();
    MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
}

function resetTOFInput(index){

    var ua = "";
    if(userAnswers[index]) ua=userAnswers[index]['ans']
    if(ua=="true") {
        $('#radio-btn-true').prop('checked', true);
    }
    else if(ua=="false") {
        $('#radio-btn-false').prop('checked', true);
    }
    else{
        $('#radio-btn-true').prop('checked', false);
        $('#radio-btn-false').prop('checked', false);

    }
}


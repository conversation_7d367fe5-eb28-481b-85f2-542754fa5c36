let controller = null;
let langtext = "English"
function interactWithTest(actionObj, actionType, actionId, userQuery, selectedLanguageGlobal, languageVal){
    isResponseComplete = false;
    langtext = languageVal
    let quizActionType = "";
    switch (actionType) {
        case "hint":
            inputMessageLabel = "Give Hint";
            quizActionType = "hint"
            break;
        case "explain":
            inputMessageLabel = "Explain MCQ";
            quizActionType = "explain"
            break;
        case "similar":
            inputMessageLabel = "Create Similar MCQs";
            quizActionType = "similarMCQ"
            break;
        case "userInput":
            inputMessageLabel = userQuery;
            quizActionType = "userInput"
            break;
        default:
            inputMessageLabel = "Explain MCQ";
            quizActionType = "explain"
            break;
    }
    showUserMessage(inputMessageLabel)
    isTyping.style.display='flex'
    conversation.scrollTop = conversation.scrollHeight

    const {mcqQuestionStr, hasImage} = constructQuestion(actionObj, selectedLanguageGlobal, languageVal)
    getMCQChat(mcqQuestionStr, actionObj.id, quizActionType, userQuery, hasImage, languageVal)
}

function constructQuestion(questionObjInstance, langIndex, languageVal) {
    const baseUrl = window.location.origin;
    let hasImage = false;
    function prependBaseUrlIfNeeded(text) {
        if (/<img.*?src=['"]([^'"]+)['"]/g.test(text)) {
            hasImage = true;
        }
        return text.replace(/src='([^']+)'/g, (match, p1) => {
            if (p1.startsWith('/')) {
                return "src='" + baseUrl + p1 + "'";
            }
            return match;
        });
    }
    let mcqQuestionStr = "MCQ:\n" + prependBaseUrlIfNeeded(questionObjInstance.question) + "\n";

    for (let i = 1; i <= 5; i++) {
        const optionKey = "option" + i;
        if (questionObjInstance[optionKey]) {
            let optionText = prependBaseUrlIfNeeded(questionObjInstance[optionKey]);
            mcqQuestionStr += "Option " + i + ":\n" + optionText + "\n";
        }
    }

    mcqQuestionStr += "Correct answer: \n";

    for (let i = 1; i <= 5; i++) {
        const answerKey = "answer" + i;
        const optionKey = "option" + i;
        if (questionObjInstance[answerKey] === "Yes" && questionObjInstance[optionKey]) {
            let correctAnswerText = prependBaseUrlIfNeeded(questionObjInstance[optionKey]);
            mcqQuestionStr += correctAnswerText + "\n";
            break;
        }
    }

    return { mcqQuestionStr, hasImage };
}

async function getMCQChat(query,id, quizActionType, userQuery, hasImage, languageVal){
    if (controller) {
        controller.abort();
    }
    controller = new AbortController();
    const { signal } = controller;
    try {
        document.getElementById('chatInput').value=''
        let response = await fetch("/prompt/quizGptInteraction", {
            method: "POST",
            body: JSON.stringify({
                query:query,
                chatHistory:history_for_llm,
                mcq:true,
                qId:id,
                type:quizActionType,
                userQuery,
                hasImage:hasImage,
                language:languageVal
            }),
            headers: {
                "Content-Type": "application/json"
            },
            signal
        });

        const answer = await response.json()
        answer.resType = "userInput"
        history_for_llm.push({
            user: query,
            ai: answer.answer,
            imgLink:answer.imgLink ? answer.imgLink : ""
        });
        currImgLink = answer.imgLink ? answer.imgLink : null
        showAnswer(answer,query,true)
        if(quizActionType!="similarMCQ"){
            storeQuizInteraction({response:answer.answer,qId : id,type:quizActionType  })
        }
        globalChatHistory =[]
        isdoubtInputEntered = false
        isSnipQuestion = false
    }catch (e){
        console.log(e)
    }finally {
        controller = null;
    }
}

async function storeQuizInteraction(reqObj) {
    try {

        const apiUrl = '/prompt/storeQuizGptContents';
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(reqObj)
        });

        if (!response.ok) {
            throw new Error("HTTP error! status: "+response.status);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error during API call:', error);
        throw error;
    }
}
function extractText(text, languageIndex) {
    if(text==null){
        return ""
    }
    const parts = text.split('~~');

    const firstPart = parts[0].trim();
    const secondPart = parts[1] ? parts[1].trim() : '';
    return languageIndex == 0 ? firstPart : secondPart;
}


function showBuyPopup(){
    if(!isResponseComplete){
        pauseShowingAnswer()
        return
    }
    const buyPopup = document.getElementById('buyPopup')
    let buyHtml=""
    if(!checkTokens()){
        buyHtml = "<p id=\"buySubText\">Oops! Looks like you're out of tokens. Recharge now to keep the conversation going.</p>\n" +
            "                <button class=\"gptAskBuyNowBtn\" id=\"gptAskBuyNowBtn\" onclick=\"openRechargePage()\">Recharge Now</button>"
    }
    popupOpt.innerHTML = buyHtml

    setTimeout(()=>{
        buyPopup.style.transform = 'translateY(-30px)'
        buyPopup.style.display = 'block'
    },50)
}

function checkTokens(){
    if((freeTokenCount!==0 && freeTokenCount!==''&& freeTokenCount!=='0'  && freeTokenCount>0) ||
        (paidTokenCount !==0 && paidTokenCount!=='' && paidTokenCount!=='0' && paidTokenCount>0)){
        return true
    }else {
        return false
    }
}
function hideBuyPopup(){
    const buyPopup = document.getElementById('buyPopup')
    setTimeout(()=>{
        buyPopup.style.transform = 'translateY(300%)'
        buyPopup.style.display = 'none'
    },30)
}

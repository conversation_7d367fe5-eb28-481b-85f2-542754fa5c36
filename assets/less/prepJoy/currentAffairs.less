.loading-icon {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  background: rgba(68, 68, 68, 0.9);
  z-index: 9999;
  overflow: hidden;
}
.load-wrapper {
  position: absolute;
  top: 35%;
  width: 100%;
}
.loader-wrapper {
  width: 139px;
  height: 65px;
  background-color: #FFFFFF;
  position: relative;
  top: 50% !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
  margin: 0 auto;
  border-radius: 4px;
}
.loader,
.loader:before,
.loader:after {
  border-radius: 50%;
  width: 2.5em;
  height: 2.5em;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation: load7 1.8s infinite ease-in-out;
  animation: load7 1.8s infinite ease-in-out;
}
.loader {
  color: #F05A2A;
  font-size: 9px;
  margin: 0 auto;
  position: relative;
  text-indent: -9999em;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}
.loader:before,
.loader:after {
  content: '';
  position: absolute;
  top: 0;
}
.loader:before {
  left: -3.5em;
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}
.loader:after {
  left: 3.5em;
}
@-webkit-keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
@keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}

.prepJoyBannerBg {
  border-radius: 10px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center bottom;
  height: 250px;
}
.button-wrapper {
  width:100%;
  display:flex;
  justify-content:center;
  align-items:center;
}
div#ui-datepicker-div {
  background:white;
  border:1px solid red;
  box-shadow: 0px 4px 10px 0px red;
  margin-left:-5%;
}
@media (max-width:575px) {

  div#ui-datepicker-div{
    margin-left:-13%;
  }
  .date-present h5{
    font-size:15px !important;
  }
  input#datepicker {
    text-align:center !important;
    float:left !important;
    width: 100px !important;
  }
  .img-wrapper
  {
    width:50px !important;
    display:flex;
  }
  .parent-wrapper-btn
  {
    display:block;
    width:200px;
    margin:auto;
  }
  table.ui-datepicker-calendar td {
    width: 35px !important;
    height: 35px !important;
  }

}
span.ui-icon.ui-icon-circle-triangle-e {
  float:right;
}
input::placeholder {
  text-align:center;
  font-family: Poppins !important;
  font-size: 14px !important;
  font-style: normal !important;
  font-weight: 700 !important;
  line-height: 21px !important;
  letter-spacing: -0.015em !important;
  color:black !important;

}
div#ui-datepicker-div a{
  cursor: pointer;
}
td.ui-datepicker-unselectable.ui-state-disabled {
  color:rgba(232, 232, 232, 1);
}

table.ui-datepicker-calendar td.ui-datepicker-unselectable.ui-state-disabled:hover
{
  background:none !important;
}
table.ui-datepicker-calendar td
{
  text-align: center;
  width: 40px;
  height: 40px;
  border-radius: 50px;
  font-weight: 600;
}
table.ui-datepicker-calendar td:hover{
  background:red;
  padding:2px;
  border-radius:50%;


}
table.ui-datepicker-calendar td:hover a{
  text-decoration: none !important;
  color:white;
}
.slider {
  max-width: 1110px;
  margin: 0 auto;
}

/* the slides */
.slick-slide {
  margin: 0 55px;
}
.slick-list.draggable {
  padding:0px 0px !important
}
@media (max-width:575px)
{
  .slick-slide {
    margin: 0 40px;
  }
  .slick-track {
    padding-top: 10%;
    padding-bottom: 10%;
  }
  .slick-list.draggable {
    padding:0px 60px !important
  }
  .slick-track {
    padding-top: 15% !important;
    padding-bottom: 15% !important;
  }
}
.slick-track {
  padding-top: 5%;
  padding-bottom: 5%;
}

.slider-card {
  background: white;
  height:150px;
}
.modal-body {
  text-align:center;
}
.date-present h5{
  font-family: Righteous;
  font-size: 30px;
  font-style: normal;
  font-weight: 400;
  line-height: 45px;
  letter-spacing: 0em;
  text-align: center;
  color: rgba(192, 59, 136, 1);
}
.slider-card.slick-slide {
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0.1;
  border-radius:10px;
}
.slick-slide {
  text-align: center;
  transition: transform 0.3s ease-in-out;
}
.slick-slide.slick-current {
  transform: scale(1.35);
  position: relative;
  z-index: 1;
  opacity: 1;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.23);
}

.slick-next:before, .slick-prev:before
{
  display:none !important;
}
@media (min-width:575px ){
  .slick-next{
    right:27% !important;
  }
  .slick-prev{
    left:27% !important;
  }
}
button.slick-prev.slick-arrow ,.slick-next{
  width:48px;
  height:48px !important;
  border-radius:50%;
  z-index:100;
}
button.slick-prev.slick-arrow img{
  -webkit-transform: scaleX(-1);
  transform: scaleX(-1);
}
button.slick-prev.slick-arrow {
  background:white !important;
}
button.slick-next.slick-arrow {
  background:white !important;
}
.ui-datepicker-title {
  position:absolute;
  top:1%;
  left:32% ;
}
input#datepicker {
  text-align:center;

}
button.prev.slick-arrow ,button.next.slick-arrow{
  border:none;
  background:none;

}
button:focus
{
  outline:none;
}
input#datepicker {
  border: none;
  height: 100%;
}
div#ui-datepicker-div{

  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001a;
  border-radius: 10px;
  font-family: 'Poppins', sans-serif !important;
}
input#datepicker:focus {
  outline: none;
}

input#datepicker {
  color: transparent;
  text-shadow: 0 0 0 #2196f3;

}
#LanguageModal button.btn.btn-secondary {
  background:none;
  color: #C633A2;
  border-color: #C633A2;
  margin:10px;

}
button#youtubeclose {
  outline:none;
}
.ui-datepicker-header.ui-widget-header.ui-helper-clearfix.ui-corner-all {
  padding:2% 5%;
}

table.ui-datepicker-calendar th {
  text-align:center
}

input#datepicker:focus-visible
{
  outline:none !important;
}

.parent-wrapper-btn {

  border-radius: 30px;
  background: rgba(255, 255, 255, 1);
  /*    border:1px solid;*/
  height:40px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.23);
  align-items:center;
  padding: 0px 1%;

}
button.prev.slick-arrow {

  height:100%;
}

button.next.slick-arrow {

  height: 100%;
}
input#datepicker {

  border-radius:0;
  height:60%;
}
.slider-card.slick-slide.slick-cloned.slick-active .date-present {
  visibility:hidden;
}
.date-present {
  cursor:pointer;
}
div#ui-datepicker-div {
  z-index:999 !important;
}
.slider-card.slick-slide.slick-active .date-present {
  pointer-events:none;
}
.slider-card.slick-slide.slick-current.slick-active.slick-center .date-present
{
  pointer-events: all;
}
@media (max-width: 575px) {
  .mdl-layout__container .mdl-layout__header-row {
    padding-top: 0% !important;
  }
  body.custom-fix .mdl-layout__header-row {
    margin-top: 5% !important;
  }
  .mdl-layout__drawer-button {
    margin-top: 16px;
  }

}
.modal-content input::placeholder{
  color:#C7C7C7 !important;
  text-align:left;
  font-style:italic !important;
  font-weight:300 !important;
}
.parent-wrapper-btn,input#datepicker{
  cursor:pointer;
}
.slick-slide.slick-current:focus
{
  border:none !important;
  outline:none !important;
}
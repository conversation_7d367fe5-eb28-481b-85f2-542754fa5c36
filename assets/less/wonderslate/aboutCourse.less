@color-green:#555555;
@color-red: #ff4d7e;
@color-light-green: #32c5d2;

#mainSection{
  overflow-x: hidden !important;
}

.certiicate-div {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1337' height='560' preserveAspectRatio='none' viewBox='0 0 1337 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1703%26quot%3b)' fill='none'%3e%3cpath d='M111.011%2c239.349C156.852%2c241.652%2c197.966%2c214.178%2c222.388%2c175.315C248.662%2c133.505%2c260.623%2c81.61%2c237.581%2c37.936C213.162%2c-8.348%2c163.34%2c-36.219%2c111.011%2c-35.789C59.394%2c-35.365%2c9.788%2c-6.805%2c-12.009%2c39.986C-31.389%2c81.588%2c-6.634%2c125.961%2c17.467%2c165.019C39.883%2c201.346%2c68.379%2c237.207%2c111.011%2c239.349' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M37.36%2c98.666C58.47%2c97.976%2c75.763%2c83.92%2c86.428%2c65.689C97.218%2c47.244%2c101.616%2c24.925%2c91.514%2c6.095C80.901%2c-13.687%2c59.805%2c-25.347%2c37.36%2c-25.774C14.128%2c-26.216%2c-8.835%2c-16.141%2c-20.483%2c3.965C-32.158%2c24.118%2c-29.736%2c49.213%2c-17.596%2c69.089C-5.974%2c88.117%2c15.075%2c99.394%2c37.36%2c98.666' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M35.033%2c102.125C59.629%2c102.113%2c84.91%2c92.191%2c96.065%2c70.27C106.501%2c49.762%2c95.217%2c26.853%2c83.377%2c7.122C72.009%2c-11.822%2c57.1%2c-30.377%2c35.033%2c-31.439C11.4%2c-32.576%2c-9.919%2c-18.236%2c-22.103%2c2.046C-34.687%2c22.994%2c-38.218%2c49.108%2c-26.102%2c70.33C-13.896%2c91.71%2c10.414%2c102.137%2c35.033%2c102.125' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M1366.595%2c310.204C1422.358%2c311.09%2c1460.614%2c260.258%2c1487.402%2c211.343C1512.866%2c164.844%2c1526.959%2c110.205%2c1501.623%2c63.636C1475.305%2c15.262%2c1421.665%2c-10.497%2c1366.595%2c-10.709C1311.135%2c-10.922%2c1255.486%2c13.461%2c1229.756%2c62.591C1205.432%2c109.036%2c1226.9%2c161.718%2c1252.259%2c207.606C1278.805%2c255.642%2c1311.719%2c309.332%2c1366.595%2c310.204' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float1'%3e%3c/path%3e%3cpath d='M1358.196%2c276.874C1412.032%2c280.17%2c1465.885%2c257.614%2c1494.453%2c211.864C1524.676%2c163.463%2c1529.346%2c100.197%2c1498.491%2c52.196C1469.566%2c7.198%2c1411.681%2c-1.019%2c1358.196%2c-0.111C1306.752%2c0.762%2c1251.031%2c11.919%2c1225.914%2c56.823C1201.201%2c101.005%2c1221.984%2c153.391%2c1248.066%2c196.779C1273.132%2c238.476%2c1309.636%2c273.901%2c1358.196%2c276.874' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M1243.22%2c68.838C1260.808%2c68.129%2c1275.598%2c57.237%2c1284.615%2c42.12C1293.894%2c26.563%2c1298.636%2c7.138%2c1289.27%2c-8.367C1280.134%2c-23.492%2c1260.888%2c-26.649%2c1243.22%2c-26.409C1226.084%2c-26.176%2c1208.729%2c-21.447%2c1199.287%2c-7.145C1188.859%2c8.651%2c1186.135%2c29.322%2c1195.609%2c45.708C1205.075%2c62.079%2c1224.324%2c69.6%2c1243.22%2c68.838' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M53.47%2c574.659C82.916%2c575.424%2c116.21%2c571.706%2c130.826%2c546.132C145.375%2c520.674%2c131.611%2c490.183%2c115.898%2c465.427C101.581%2c442.87%2c80.187%2c424.26%2c53.47%2c424.257C26.749%2c424.254%2c4.751%2c442.492%2c-8.979%2c465.415C-23.172%2c489.111%2c-30.934%2c518.307%2c-17.226%2c542.286C-3.44%2c566.403%2c25.7%2c573.938%2c53.47%2c574.659' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float2'%3e%3c/path%3e%3cpath d='M137.503%2c743.592C190.204%2c744.189%2c227.825%2c698.501%2c253.534%2c652.493C278.457%2c607.892%2c293.147%2c554.636%2c267.914%2c510.21C242.435%2c465.351%2c189.023%2c445.517%2c137.503%2c448.205C90.462%2c450.66%2c51.51%2c481.472%2c28.711%2c522.692C6.707%2c562.475%2c5.852%2c609.13%2c25.938%2c649.915C48.933%2c696.606%2c85.46%2c743.002%2c137.503%2c743.592' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float1'%3e%3c/path%3e%3cpath d='M63.198%2c595.899C94.374%2c595.272%2c124.319%2c582.47%2c140.834%2c556.021C158.377%2c527.926%2c163.328%2c491.616%2c145.9%2c463.45C129.169%2c436.409%2c94.987%2c429.753%2c63.198%2c430.546C33.078%2c431.297%2c3.709%2c442.106%2c-12.653%2c467.405C-30.532%2c495.049%2c-36.781%2c530.907%2c-20.045%2c559.258C-3.527%2c587.239%2c30.712%2c596.553%2c63.198%2c595.899' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float2'%3e%3c/path%3e%3cpath d='M1286.016%2c596.003C1314.761%2c594.7%2c1333.836%2c569.851%2c1348.361%2c545.011C1363.08%2c519.839%2c1376.292%2c490.534%2c1362.807%2c464.68C1348.562%2c437.369%2c1316.806%2c424.593%2c1286.016%2c425.481C1256.756%2c426.325%2c1231.233%2c443.587%2c1216.534%2c468.901C1201.766%2c494.334%2c1198.938%2c525.339%2c1213.045%2c551.145C1227.724%2c577.997%2c1255.445%2c597.389%2c1286.016%2c596.003' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float2'%3e%3c/path%3e%3cpath d='M1265.556%2c549.857C1290.229%2c551.032%2c1317.265%2c546.91%2c1330.459%2c526.028C1344.349%2c504.045%2c1340.856%2c475.054%2c1326.533%2c453.351C1313.514%2c433.624%2c1289.182%2c428.354%2c1265.556%2c427.682C1240.268%2c426.962%2c1211.174%2c427.949%2c1198.257%2c449.701C1185.182%2c471.719%2c1196.263%2c499.062%2c1210.545%2c520.317C1222.987%2c538.833%2c1243.273%2c548.796%2c1265.556%2c549.857' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float1'%3e%3c/path%3e%3cpath d='M1250.426%2c526.861C1271.323%2c527.262%2c1293.431%2c522.372%2c1304.56%2c504.68C1316.305%2c486.009%2c1314.979%2c461.607%2c1303.205%2c442.954C1292.146%2c425.433%2c1271.145%2c418.925%2c1250.426%2c418.918C1229.692%2c418.911%2c1208.318%2c425.178%2c1197.588%2c442.92C1186.534%2c461.198%2c1188.398%2c484.591%2c1199.777%2c502.668C1210.46%2c519.639%2c1230.377%2c526.476%2c1250.426%2c526.861' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1703'%3e%3crect width='1337' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3cstyle%3e %40keyframes float1 %7b 0%25%7btransform: translate(0%2c 0)%7d 50%25%7btransform: translate(-10px%2c 0)%7d 100%25%7btransform: translate(0%2c 0)%7d %7d .triangle-float1 %7b animation: float1 5s infinite%3b %7d %40keyframes float2 %7b 0%25%7btransform: translate(0%2c 0)%7d 50%25%7btransform: translate(-5px%2c -5px)%7d 100%25%7btransform: translate(0%2c 0)%7d %7d .triangle-float2 %7b animation: float2 4s infinite%3b %7d %40keyframes float3 %7b 0%25%7btransform: translate(0%2c 0)%7d 50%25%7btransform: translate(0%2c -10px)%7d 100%25%7btransform: translate(0%2c 0)%7d %7d .triangle-float3 %7b animation: float3 6s infinite%3b %7d %3c/style%3e%3c/defs%3e%3c/svg%3e");
}
.future-ready{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1422' height='560' preserveAspectRatio='none' viewBox='0 0 1422 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1143%26quot%3b)' fill='none'%3e%3crect width='1422' height='560' x='0' y='0' fill='rgba(244%2c 244%2c 244%2c 1)'%3e%3c/rect%3e%3cpath d='M0%2c499.828C115.516%2c499.362%2c239.033%2c585.462%2c338.469%2c526.668C437.596%2c468.057%2c441.723%2c327.453%2c476.954%2c217.817C508.13%2c120.802%2c544.426%2c24.644%2c532.307%2c-76.534C520.015%2c-179.157%2c480.554%2c-279.87%2c408.467%2c-353.939C338.33%2c-426.005%2c234.189%2c-442.077%2c140.639%2c-478.971C36.697%2c-519.964%2c-60.185%2c-596.522%2c-171.033%2c-582.483C-287.211%2c-567.769%2c-406.791%2c-504.343%2c-464.337%2c-402.351C-520.496%2c-302.818%2c-453.269%2c-180.387%2c-462.188%2c-66.453C-470.306%2c37.253%2c-535.078%2c132.926%2c-514.219%2c234.836C-490.879%2c348.868%2c-443.693%2c476.085%2c-340.227%2c529.404C-236.945%2c582.629%2c-116.189%2c500.296%2c0%2c499.828' fill='%23e8e8e8'%3e%3c/path%3e%3cpath d='M1422 1040.155C1516.712 1030.864 1622.607 1047.1680000000001 1696.711 987.4580000000001 1769.531 928.783 1770.009 821.446 1802.339 733.6949999999999 1831.873 653.532 1893.4189999999999 578.478 1878.372 494.384 1863.416 410.801 1787.968 354.255 1724.377 297.989 1667.704 247.84500000000003 1598.338 222.69600000000003 1531.408 187.389 1445.561 142.103 1370.719 42.184999999999945 1275.545 61.221000000000004 1181.781 79.97500000000002 1153.858 201.171 1093.146 275.046 1034.379 346.554 953.7860000000001 400.68100000000004 924.4300000000001 488.46 892.039 585.314 889.037 691.7529999999999 921.393 788.619 955.719 891.385 1013.509 996.335 1110.3220000000001 1044.98 1205.085 1092.595 1316.454 1050.509 1422 1040.155' fill='white'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1143'%3e%3crect width='1422' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
}
.modal{
  z-index: 99999;
}
.modal-content{
  border: none;
  border-radius: 15px;

}
.testimonial2 {
  color: @color-green;
  h5 {
    line-height: 22px;
    font-size: 18px;
    font-weight: 400;
  }
  .font-weight-medium {
    font-weight: 500;
  }
  .bg-light {
    background-color: #f4f8fa !important;
  }
  .subtitle {
    color: @color-green;
    line-height: 24px;
  }

  .testi2  {
    .image-thumb{
      background: url(https://www.wrappixel.com/demos/ui-kit/wrapkit/assets/images/testimonial/greadint-bg.png) no-repeat top center;
      text-align: center;
      padding: 10% 0;

      img {
        width: 200px;
        height: 200px;
        object-fit: cover;
      }
    }

    .owl-dots {
      display: inline-block;
      position: relative;
      top: -100px;
    }

    .owl-dot {
      border-radius: 100%;
      width: 70px;
      height: 70px;
      background-size: cover;
      margin-right: 10px;
      opacity: 0.4;
      cursor: pointer;

      span {
        display: none;
      }

    }

  }
  .btn-md {
    padding: 18px 0px;
    width: 60px;
    height: 60px;
    font-size: 20px;
  }

  .btn-danger {
    background: @color-red !important;
    border: 1px solid @color-red !important;
  }
}

.testimonial2 h1,
.testimonial2 h2,
.testimonial2 h3,
.testimonial2 h4,
.testimonial2 h5,
.testimonial2 h6 {
  color: #3e4555;
}




.testimonial2 .testi2 .owl-dots .owl-dot
.testimonial2 .testi2 .owl-dots .owl-dot.active,
.testimonial2 .testi2 .owl-dots .owl-dot:hover {
  opacity: 1;
}

.modal-backdrop{
  &.show{
    opacity: 0;
  }
}
.modal{
  z-index: 10000;
}

.threeStep {
  ul {
    list-style-image: url('../../images/wslibrary/correction.svg');

    li {
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      padding-bottom: 5px;
      padding-top: 10px;
      list-style: none;
    }
  }
}

.price-agenda{
  padding-left: 0px;
}

.register input,
.register textarea{
  border: none !important;
}

a{
  text-decoration: none!important;
}
.tasks-list-group {
  li{
    list-style: none;

    i{
      color: #17a2b8;
    }
  }

}


.gifts{
  li{
    list-style: none;
    font-size: 17px;
  }

}

.banner{
  min-height: 500px;
}
.card {
  box-shadow: 0px 4px 8px 0px #BDBDBD;
  border-radius: 8px !important;
}
.color-green{
  background: @color-light-green;
}
.description-content {
  p{
    line-height: 1.6 !important;
  }
}
.enroll{
  border-radius: 10px;
}
.stars {
  i{
    color: yellow;
  }

}

.ws-des {
  a{
    color: #00FE00;
    text-decoration: none;
  }

}
.description-cont{
  border-radius: 10px !important;
}

.task-content{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1337' height='560' preserveAspectRatio='none' viewBox='0 0 1337 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1703%26quot%3b)' fill='none'%3e%3cpath d='M111.011%2c239.349C156.852%2c241.652%2c197.966%2c214.178%2c222.388%2c175.315C248.662%2c133.505%2c260.623%2c81.61%2c237.581%2c37.936C213.162%2c-8.348%2c163.34%2c-36.219%2c111.011%2c-35.789C59.394%2c-35.365%2c9.788%2c-6.805%2c-12.009%2c39.986C-31.389%2c81.588%2c-6.634%2c125.961%2c17.467%2c165.019C39.883%2c201.346%2c68.379%2c237.207%2c111.011%2c239.349' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M37.36%2c98.666C58.47%2c97.976%2c75.763%2c83.92%2c86.428%2c65.689C97.218%2c47.244%2c101.616%2c24.925%2c91.514%2c6.095C80.901%2c-13.687%2c59.805%2c-25.347%2c37.36%2c-25.774C14.128%2c-26.216%2c-8.835%2c-16.141%2c-20.483%2c3.965C-32.158%2c24.118%2c-29.736%2c49.213%2c-17.596%2c69.089C-5.974%2c88.117%2c15.075%2c99.394%2c37.36%2c98.666' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M35.033%2c102.125C59.629%2c102.113%2c84.91%2c92.191%2c96.065%2c70.27C106.501%2c49.762%2c95.217%2c26.853%2c83.377%2c7.122C72.009%2c-11.822%2c57.1%2c-30.377%2c35.033%2c-31.439C11.4%2c-32.576%2c-9.919%2c-18.236%2c-22.103%2c2.046C-34.687%2c22.994%2c-38.218%2c49.108%2c-26.102%2c70.33C-13.896%2c91.71%2c10.414%2c102.137%2c35.033%2c102.125' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M1366.595%2c310.204C1422.358%2c311.09%2c1460.614%2c260.258%2c1487.402%2c211.343C1512.866%2c164.844%2c1526.959%2c110.205%2c1501.623%2c63.636C1475.305%2c15.262%2c1421.665%2c-10.497%2c1366.595%2c-10.709C1311.135%2c-10.922%2c1255.486%2c13.461%2c1229.756%2c62.591C1205.432%2c109.036%2c1226.9%2c161.718%2c1252.259%2c207.606C1278.805%2c255.642%2c1311.719%2c309.332%2c1366.595%2c310.204' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float1'%3e%3c/path%3e%3cpath d='M1358.196%2c276.874C1412.032%2c280.17%2c1465.885%2c257.614%2c1494.453%2c211.864C1524.676%2c163.463%2c1529.346%2c100.197%2c1498.491%2c52.196C1469.566%2c7.198%2c1411.681%2c-1.019%2c1358.196%2c-0.111C1306.752%2c0.762%2c1251.031%2c11.919%2c1225.914%2c56.823C1201.201%2c101.005%2c1221.984%2c153.391%2c1248.066%2c196.779C1273.132%2c238.476%2c1309.636%2c273.901%2c1358.196%2c276.874' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M1243.22%2c68.838C1260.808%2c68.129%2c1275.598%2c57.237%2c1284.615%2c42.12C1293.894%2c26.563%2c1298.636%2c7.138%2c1289.27%2c-8.367C1280.134%2c-23.492%2c1260.888%2c-26.649%2c1243.22%2c-26.409C1226.084%2c-26.176%2c1208.729%2c-21.447%2c1199.287%2c-7.145C1188.859%2c8.651%2c1186.135%2c29.322%2c1195.609%2c45.708C1205.075%2c62.079%2c1224.324%2c69.6%2c1243.22%2c68.838' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M53.47%2c574.659C82.916%2c575.424%2c116.21%2c571.706%2c130.826%2c546.132C145.375%2c520.674%2c131.611%2c490.183%2c115.898%2c465.427C101.581%2c442.87%2c80.187%2c424.26%2c53.47%2c424.257C26.749%2c424.254%2c4.751%2c442.492%2c-8.979%2c465.415C-23.172%2c489.111%2c-30.934%2c518.307%2c-17.226%2c542.286C-3.44%2c566.403%2c25.7%2c573.938%2c53.47%2c574.659' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float2'%3e%3c/path%3e%3cpath d='M137.503%2c743.592C190.204%2c744.189%2c227.825%2c698.501%2c253.534%2c652.493C278.457%2c607.892%2c293.147%2c554.636%2c267.914%2c510.21C242.435%2c465.351%2c189.023%2c445.517%2c137.503%2c448.205C90.462%2c450.66%2c51.51%2c481.472%2c28.711%2c522.692C6.707%2c562.475%2c5.852%2c609.13%2c25.938%2c649.915C48.933%2c696.606%2c85.46%2c743.002%2c137.503%2c743.592' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float1'%3e%3c/path%3e%3cpath d='M63.198%2c595.899C94.374%2c595.272%2c124.319%2c582.47%2c140.834%2c556.021C158.377%2c527.926%2c163.328%2c491.616%2c145.9%2c463.45C129.169%2c436.409%2c94.987%2c429.753%2c63.198%2c430.546C33.078%2c431.297%2c3.709%2c442.106%2c-12.653%2c467.405C-30.532%2c495.049%2c-36.781%2c530.907%2c-20.045%2c559.258C-3.527%2c587.239%2c30.712%2c596.553%2c63.198%2c595.899' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float2'%3e%3c/path%3e%3cpath d='M1286.016%2c596.003C1314.761%2c594.7%2c1333.836%2c569.851%2c1348.361%2c545.011C1363.08%2c519.839%2c1376.292%2c490.534%2c1362.807%2c464.68C1348.562%2c437.369%2c1316.806%2c424.593%2c1286.016%2c425.481C1256.756%2c426.325%2c1231.233%2c443.587%2c1216.534%2c468.901C1201.766%2c494.334%2c1198.938%2c525.339%2c1213.045%2c551.145C1227.724%2c577.997%2c1255.445%2c597.389%2c1286.016%2c596.003' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float2'%3e%3c/path%3e%3cpath d='M1265.556%2c549.857C1290.229%2c551.032%2c1317.265%2c546.91%2c1330.459%2c526.028C1344.349%2c504.045%2c1340.856%2c475.054%2c1326.533%2c453.351C1313.514%2c433.624%2c1289.182%2c428.354%2c1265.556%2c427.682C1240.268%2c426.962%2c1211.174%2c427.949%2c1198.257%2c449.701C1185.182%2c471.719%2c1196.263%2c499.062%2c1210.545%2c520.317C1222.987%2c538.833%2c1243.273%2c548.796%2c1265.556%2c549.857' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float1'%3e%3c/path%3e%3cpath d='M1250.426%2c526.861C1271.323%2c527.262%2c1293.431%2c522.372%2c1304.56%2c504.68C1316.305%2c486.009%2c1314.979%2c461.607%2c1303.205%2c442.954C1292.146%2c425.433%2c1271.145%2c418.925%2c1250.426%2c418.918C1229.692%2c418.911%2c1208.318%2c425.178%2c1197.588%2c442.92C1186.534%2c461.198%2c1188.398%2c484.591%2c1199.777%2c502.668C1210.46%2c519.639%2c1230.377%2c526.476%2c1250.426%2c526.861' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1703'%3e%3crect width='1337' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3cstyle%3e %40keyframes float1 %7b 0%25%7btransform: translate(0%2c 0)%7d 50%25%7btransform: translate(-10px%2c 0)%7d 100%25%7btransform: translate(0%2c 0)%7d %7d .triangle-float1 %7b animation: float1 5s infinite%3b %7d %40keyframes float2 %7b 0%25%7btransform: translate(0%2c 0)%7d 50%25%7btransform: translate(-5px%2c -5px)%7d 100%25%7btransform: translate(0%2c 0)%7d %7d .triangle-float2 %7b animation: float2 4s infinite%3b %7d %40keyframes float3 %7b 0%25%7btransform: translate(0%2c 0)%7d 50%25%7btransform: translate(0%2c -10px)%7d 100%25%7btransform: translate(0%2c 0)%7d %7d .triangle-float3 %7b animation: float3 6s infinite%3b %7d %3c/style%3e%3c/defs%3e%3c/svg%3e");

}

//.card{
//  border-radius: 8px !important;
//}

.testimonials-v-2 {
  padding: 100px 0;
  overflow: hidden;
}

.testi-slide {
  text-align: center;

  img{
    width: 92px;
    height: 92px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    border-radius: 50%;
    object-fit: cover;
  }

  p{
    margin: 20px 0;
    font-size: 16px;
    font-weight: 400;
    line-height: 30px;
    font-style: italic;
  }

  i{
    color: #32c5d2;
    margin-right: 10px;
  }
  h4{
    font-weight: 400;
    font-size: 16px;
    font-family: "Lato", sans-serif !important;
    font-style: italic;
  }

  .flex-control-paging{
    li {
      a{
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        -ms-box-shadow: none;
        box-shadow: none;
        background: transparent !important;
        border: 2px solid #ccc;
        width: 8px;
        height: 8px;
      }
    }
  }
}


.testi-slide .flex-control-paging li a.flex-active {
  background: transparent !important;
  border: 2px solid @color-light-green;
}

.quote {
  position: relative;

  div {
    padding: 0px;
    border: 0;
    margin: 0;
    font-size: 14px;
    font-style: italic;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -ms-border-radius: 8px;
    border-radius: 8px;

    p{
      color: #fff;
      padding-top: 25px;
      padding-bottom: 45px;
      padding-left: 30px;
      padding-right: 30px;
    }

  }
}


.quote.green div {
  background-color: @color-light-green;
}


.quote.dark div {
  background-color: #555;
}


.quote-footer {
  margin: 10px 0;
}

.quote-footer .quote-author-img img {
  float: left;
  max-width: 90px;
  width: 90px;
  height: 90px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  margin-left: -5px;
  margin-top: -40px;
  position: relative;
  z-index: 1;
  padding: 5px;
  background-color: #fff;
}

.quote-footer h4 {
  font-size: 14px;
  margin-bottom: 4px;
}

.quote-footer p {
  font-weight: 400;
  font-style: italic;
  font-size: 14px;
}


.certiicate-div {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1337' height='560' preserveAspectRatio='none' viewBox='0 0 1337 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1703%26quot%3b)' fill='none'%3e%3cpath d='M111.011%2c239.349C156.852%2c241.652%2c197.966%2c214.178%2c222.388%2c175.315C248.662%2c133.505%2c260.623%2c81.61%2c237.581%2c37.936C213.162%2c-8.348%2c163.34%2c-36.219%2c111.011%2c-35.789C59.394%2c-35.365%2c9.788%2c-6.805%2c-12.009%2c39.986C-31.389%2c81.588%2c-6.634%2c125.961%2c17.467%2c165.019C39.883%2c201.346%2c68.379%2c237.207%2c111.011%2c239.349' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M37.36%2c98.666C58.47%2c97.976%2c75.763%2c83.92%2c86.428%2c65.689C97.218%2c47.244%2c101.616%2c24.925%2c91.514%2c6.095C80.901%2c-13.687%2c59.805%2c-25.347%2c37.36%2c-25.774C14.128%2c-26.216%2c-8.835%2c-16.141%2c-20.483%2c3.965C-32.158%2c24.118%2c-29.736%2c49.213%2c-17.596%2c69.089C-5.974%2c88.117%2c15.075%2c99.394%2c37.36%2c98.666' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M35.033%2c102.125C59.629%2c102.113%2c84.91%2c92.191%2c96.065%2c70.27C106.501%2c49.762%2c95.217%2c26.853%2c83.377%2c7.122C72.009%2c-11.822%2c57.1%2c-30.377%2c35.033%2c-31.439C11.4%2c-32.576%2c-9.919%2c-18.236%2c-22.103%2c2.046C-34.687%2c22.994%2c-38.218%2c49.108%2c-26.102%2c70.33C-13.896%2c91.71%2c10.414%2c102.137%2c35.033%2c102.125' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M1366.595%2c310.204C1422.358%2c311.09%2c1460.614%2c260.258%2c1487.402%2c211.343C1512.866%2c164.844%2c1526.959%2c110.205%2c1501.623%2c63.636C1475.305%2c15.262%2c1421.665%2c-10.497%2c1366.595%2c-10.709C1311.135%2c-10.922%2c1255.486%2c13.461%2c1229.756%2c62.591C1205.432%2c109.036%2c1226.9%2c161.718%2c1252.259%2c207.606C1278.805%2c255.642%2c1311.719%2c309.332%2c1366.595%2c310.204' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float1'%3e%3c/path%3e%3cpath d='M1358.196%2c276.874C1412.032%2c280.17%2c1465.885%2c257.614%2c1494.453%2c211.864C1524.676%2c163.463%2c1529.346%2c100.197%2c1498.491%2c52.196C1469.566%2c7.198%2c1411.681%2c-1.019%2c1358.196%2c-0.111C1306.752%2c0.762%2c1251.031%2c11.919%2c1225.914%2c56.823C1201.201%2c101.005%2c1221.984%2c153.391%2c1248.066%2c196.779C1273.132%2c238.476%2c1309.636%2c273.901%2c1358.196%2c276.874' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M1243.22%2c68.838C1260.808%2c68.129%2c1275.598%2c57.237%2c1284.615%2c42.12C1293.894%2c26.563%2c1298.636%2c7.138%2c1289.27%2c-8.367C1280.134%2c-23.492%2c1260.888%2c-26.649%2c1243.22%2c-26.409C1226.084%2c-26.176%2c1208.729%2c-21.447%2c1199.287%2c-7.145C1188.859%2c8.651%2c1186.135%2c29.322%2c1195.609%2c45.708C1205.075%2c62.079%2c1224.324%2c69.6%2c1243.22%2c68.838' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M53.47%2c574.659C82.916%2c575.424%2c116.21%2c571.706%2c130.826%2c546.132C145.375%2c520.674%2c131.611%2c490.183%2c115.898%2c465.427C101.581%2c442.87%2c80.187%2c424.26%2c53.47%2c424.257C26.749%2c424.254%2c4.751%2c442.492%2c-8.979%2c465.415C-23.172%2c489.111%2c-30.934%2c518.307%2c-17.226%2c542.286C-3.44%2c566.403%2c25.7%2c573.938%2c53.47%2c574.659' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float2'%3e%3c/path%3e%3cpath d='M137.503%2c743.592C190.204%2c744.189%2c227.825%2c698.501%2c253.534%2c652.493C278.457%2c607.892%2c293.147%2c554.636%2c267.914%2c510.21C242.435%2c465.351%2c189.023%2c445.517%2c137.503%2c448.205C90.462%2c450.66%2c51.51%2c481.472%2c28.711%2c522.692C6.707%2c562.475%2c5.852%2c609.13%2c25.938%2c649.915C48.933%2c696.606%2c85.46%2c743.002%2c137.503%2c743.592' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float1'%3e%3c/path%3e%3cpath d='M63.198%2c595.899C94.374%2c595.272%2c124.319%2c582.47%2c140.834%2c556.021C158.377%2c527.926%2c163.328%2c491.616%2c145.9%2c463.45C129.169%2c436.409%2c94.987%2c429.753%2c63.198%2c430.546C33.078%2c431.297%2c3.709%2c442.106%2c-12.653%2c467.405C-30.532%2c495.049%2c-36.781%2c530.907%2c-20.045%2c559.258C-3.527%2c587.239%2c30.712%2c596.553%2c63.198%2c595.899' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float2'%3e%3c/path%3e%3cpath d='M1286.016%2c596.003C1314.761%2c594.7%2c1333.836%2c569.851%2c1348.361%2c545.011C1363.08%2c519.839%2c1376.292%2c490.534%2c1362.807%2c464.68C1348.562%2c437.369%2c1316.806%2c424.593%2c1286.016%2c425.481C1256.756%2c426.325%2c1231.233%2c443.587%2c1216.534%2c468.901C1201.766%2c494.334%2c1198.938%2c525.339%2c1213.045%2c551.145C1227.724%2c577.997%2c1255.445%2c597.389%2c1286.016%2c596.003' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float2'%3e%3c/path%3e%3cpath d='M1265.556%2c549.857C1290.229%2c551.032%2c1317.265%2c546.91%2c1330.459%2c526.028C1344.349%2c504.045%2c1340.856%2c475.054%2c1326.533%2c453.351C1313.514%2c433.624%2c1289.182%2c428.354%2c1265.556%2c427.682C1240.268%2c426.962%2c1211.174%2c427.949%2c1198.257%2c449.701C1185.182%2c471.719%2c1196.263%2c499.062%2c1210.545%2c520.317C1222.987%2c538.833%2c1243.273%2c548.796%2c1265.556%2c549.857' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float1'%3e%3c/path%3e%3cpath d='M1250.426%2c526.861C1271.323%2c527.262%2c1293.431%2c522.372%2c1304.56%2c504.68C1316.305%2c486.009%2c1314.979%2c461.607%2c1303.205%2c442.954C1292.146%2c425.433%2c1271.145%2c418.925%2c1250.426%2c418.918C1229.692%2c418.911%2c1208.318%2c425.178%2c1197.588%2c442.92C1186.534%2c461.198%2c1188.398%2c484.591%2c1199.777%2c502.668C1210.46%2c519.639%2c1230.377%2c526.476%2c1250.426%2c526.861' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1703'%3e%3crect width='1337' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3cstyle%3e %40keyframes float1 %7b 0%25%7btransform: translate(0%2c 0)%7d 50%25%7btransform: translate(-10px%2c 0)%7d 100%25%7btransform: translate(0%2c 0)%7d %7d .triangle-float1 %7b animation: float1 5s infinite%3b %7d %40keyframes float2 %7b 0%25%7btransform: translate(0%2c 0)%7d 50%25%7btransform: translate(-5px%2c -5px)%7d 100%25%7btransform: translate(0%2c 0)%7d %7d .triangle-float2 %7b animation: float2 4s infinite%3b %7d %40keyframes float3 %7b 0%25%7btransform: translate(0%2c 0)%7d 50%25%7btransform: translate(0%2c -10px)%7d 100%25%7btransform: translate(0%2c 0)%7d %7d .triangle-float3 %7b animation: float3 6s infinite%3b %7d %3c/style%3e%3c/defs%3e%3c/svg%3e");
}
.future-ready{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1422' height='560' preserveAspectRatio='none' viewBox='0 0 1422 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1143%26quot%3b)' fill='none'%3e%3crect width='1422' height='560' x='0' y='0' fill='rgba(244%2c 244%2c 244%2c 1)'%3e%3c/rect%3e%3cpath d='M0%2c499.828C115.516%2c499.362%2c239.033%2c585.462%2c338.469%2c526.668C437.596%2c468.057%2c441.723%2c327.453%2c476.954%2c217.817C508.13%2c120.802%2c544.426%2c24.644%2c532.307%2c-76.534C520.015%2c-179.157%2c480.554%2c-279.87%2c408.467%2c-353.939C338.33%2c-426.005%2c234.189%2c-442.077%2c140.639%2c-478.971C36.697%2c-519.964%2c-60.185%2c-596.522%2c-171.033%2c-582.483C-287.211%2c-567.769%2c-406.791%2c-504.343%2c-464.337%2c-402.351C-520.496%2c-302.818%2c-453.269%2c-180.387%2c-462.188%2c-66.453C-470.306%2c37.253%2c-535.078%2c132.926%2c-514.219%2c234.836C-490.879%2c348.868%2c-443.693%2c476.085%2c-340.227%2c529.404C-236.945%2c582.629%2c-116.189%2c500.296%2c0%2c499.828' fill='%23e8e8e8'%3e%3c/path%3e%3cpath d='M1422 1040.155C1516.712 1030.864 1622.607 1047.1680000000001 1696.711 987.4580000000001 1769.531 928.783 1770.009 821.446 1802.339 733.6949999999999 1831.873 653.532 1893.4189999999999 578.478 1878.372 494.384 1863.416 410.801 1787.968 354.255 1724.377 297.989 1667.704 247.84500000000003 1598.338 222.69600000000003 1531.408 187.389 1445.561 142.103 1370.719 42.184999999999945 1275.545 61.221000000000004 1181.781 79.97500000000002 1153.858 201.171 1093.146 275.046 1034.379 346.554 953.7860000000001 400.68100000000004 924.4300000000001 488.46 892.039 585.314 889.037 691.7529999999999 921.393 788.619 955.719 891.385 1013.509 996.335 1110.3220000000001 1044.98 1205.085 1092.595 1316.454 1050.509 1422 1040.155' fill='white'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1143'%3e%3crect width='1422' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
}

.modal-content{
  border: none;
  border-radius: 15px;
}
.testimonial2 {
  color: @color-green;

}

.testimonial2 h1,
.testimonial2 h2,
.testimonial2 h3,
.testimonial2 h4,
.testimonial2 h5,
.testimonial2 h6 {
  color: #3e4555;
}

.testimonial2 h5 {
  line-height: 22px;
  font-size: 18px;
  font-weight: 400;
}

.testimonial2 .font-weight-medium {
  font-weight: 500;
}

.testimonial2 .bg-light {
  background-color: #f4f8fa !important;
}

.testimonial2 .subtitle {
  color: @color-green;
  line-height: 24px;
}

.testimonial2 .testi2 .image-thumb {
  background: url(https://www.wrappixel.com/demos/ui-kit/wrapkit/assets/images/testimonial/greadint-bg.png) no-repeat top center;
  text-align: center;
  padding: 10% 0;
}

.testimonial2 .testi2 .image-thumb img {
  width: 200px;
  height: 200px;
  object-fit: cover;
}

.testimonial2 .testi2 .owl-dots {
  display: inline-block;
  position: relative;
  top: -100px;
}

.testimonial2 .testi2 .owl-dots .owl-dot {
  border-radius: 100%;
  width: 70px;
  height: 70px;
  background-size: cover;
  margin-right: 10px;
  opacity: 0.4;
  cursor: pointer;
}

.testimonial2 .testi2 .owl-dots .owl-dot span {
  display: none;
}

.testimonial2 .testi2 .owl-dots .owl-dot.active,
.testimonial2 .testi2 .owl-dots .owl-dot:hover {
  opacity: 1;
}

.testimonial2 .btn-md {
  padding: 18px 0px;
  width: 60px;
  height: 60px;
  font-size: 20px;
}

.testimonial2 .btn-danger {
  background: #ff4d7e !important;
  border: 1px solid #ff4d7e !important;
}
.modal-backdrop.show{
  opacity: 0;
}
.modal{
  z-index: 10000;
}

.threeStep ul {
  list-style-image: url('../../images/wslibrary/correction.svg');
}
.threeStep ul li {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 5px;
  padding-top: 10px;
  list-style: none;
}
.price-agenda{
  padding-left: 0px;
}

.register input,
.register textarea{
  border: none !important;
}

@media (max-width: 760px){
  .banner-img {
    width: 220px;
    position: relative;
    left: 50%;
    top: 60px;
    transform: translate(-50%,-50%);
    bottom:0;
    min-height: 100%;
  }
}
@media (max-width: 760px){
  .htext{
    position: relative;
    top: 30px;
    font-size: 20px;
  }
  .banner-img {
    width: 220px;
    position: relative;
    left: 50%;
    top: 126px;
    transform: translate(-50%,-50%);
    bottom:0;
    min-height: 100%;
  }
}
@media screen and (max-width: 999px) {

  .next,
  .prev {
    transform: scale(1);
    opacity: 1
  }

  .item {
    display: block !important
  }
}
@media only screen and (max-width: 767px) {
  .header{
    height: 100%;
  }
  .tasks-list-group{
    width: 100%;
  }
.test-header{
  margin-top: 20px;
}
.testim h2{
  margin-top: 1rem;
}
  .testim{
    padding-bottom: 2rem !important;
    padding-top: 1.5rem !important;
  }
.three-step{
  padding: 10px !important;
}
}


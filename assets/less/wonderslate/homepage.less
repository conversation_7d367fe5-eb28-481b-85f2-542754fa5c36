@import "../variables/responsive.less";
@import "color.less";
@import "common.less";
.banner{
  position: relative;
  z-index: 999;
  @media @iPhone {
    margin-top: 0;
  }
}
.screen{
  //background: url("../../images/ws/screen.png") center no-repeat;
  //background-size: contain;
  //width:960px;
  //height:645px;
  //margin: 0 auto;
  //@media @iPhone,@iPad-portrait{
  //  background: none;
  //  height: 100%;
  //  width: 100%;
  //}

  .dropdown-menu.show {
    @media @iPhone,@iPad-pro {
      width: 250px;
    }
    a{
      &:hover{
        text-decoration: none;
      }
    }
    p{
      color: rgba(113, 3, 114, 0.9);
      font-size: 12px;
      margin-top: 10px;
    }
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
    border:none;
  }
}
.card-content{
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  box-shadow: 0px 4px 10px rgba(215, 0, 102, 0.15);
  border-radius: 10px;
  width: 400px;
  min-height: 100px;
  padding: 10px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
  -webkit-appearance:none;
  @media @iPhone,@iPad-portrait{
    margin: 0 auto;
  }
  &:hover{
    margin-top: -5px;
    cursor: pointer;
  }
  &.blue {
    h2 {
      color: rgba(0, 160, 239, 1);
    }
  }
  &.orange{
    h2 {
      color: rgba(255, 102, 0, 1);
    }
  }
  &.green{
    h2 {
      color: rgba(0, 215, 118, 1);
    }
  }
  @media @iPhone{
    width: 100%;
  }
  h2{
    color:rgba(215, 0, 102, 1);
    font-size: 24px;
    text-align: right;
    font-weight: 700;
    &:hover{
      cursor: pointer;
    }
  }
  p{
    color:rgba(68, 68, 68, 0.48);
    font-weight: 500;
    font-size: 12px;
    text-align: right;
    &:hover{
      cursor: pointer;
    }
  }
  >div>img.content-hero{
     width: 50px;
    height: 78px;
  }
  a{
    text-decoration: none;
  }
  &.dropdown-toggle::after{
    display: none;
  }
}
.mt-6{
  margin-top: 5rem;
  @media @iPhone{
    margin-top: 1.5rem;
  }
}
.counter-footer{
  background: radial-gradient(315.57% 3680.42% at -24.64% -139.82%, #FFD700 0%, #FF5200 100%);
  box-shadow: inset 0px 4px 10px rgba(0, 0, 0, 0.15);
  min-height: 165px;
  margin:0 auto;
  margin-top: 5rem;
  //width: 862px;
  @media @iPhone{

  }
  div{
    img{
      margin: 0 auto;
    }
    h3{
      color:#fff;
      text-align: center;
      font-size: 20px;
      margin-bottom: 0;
      margin-top: 10px;
    }
    p{
      color:#444444;
      text-align: center;
      font-size: 12px;
    }
  }
}
.moreforyou{
  display: none;
}
.social-icons{
  display: flex;
  justify-content: center;
  padding: 0;
  margin: 0;
  li{
    list-style-type: none;
    padding: 10px;
    padding-top: 0;
    a{
      text-decoration: none;
      i{
        color:#fff;
        font-size: 10px;
      }
    }
  }
}
.pt-8 {
  padding-top: 4rem;
}
footer{
  background:#212121;
  min-height: 344px;
  margin: 1.5px;
  border-radius: 8px;
  position: relative;
  margin-top: 4rem;
  @media screen and (max-width: 991px) {
    min-height: 400px;
    z-index: inherit;
  }
  @media @iPhone{
    min-height: 100%;
    padding-bottom: 1rem;
    margin-top: 3rem !important;
  }
  img{
    width: auto;
    height: auto;
  }
}
.logo-wrapper{
  text-align: center;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  .footer-logo{
    position: absolute;
    margin: 0 auto;
  }
}
.social-icons{
  display: flex;
  justify-content: center;
  padding: 0;
  margin: 0;
  li{
    list-style-type: none;
    padding: 10px;
    padding-top: 0;
    a{
      text-decoration: none;
      i{
        color:#fff;
        font-size: 10px;
      }
    }
  }
}
[class^="flaticon-"]:before, [class*=" flaticon-"]:before, [class^="flaticon-"]:after, [class*=" flaticon-"]:after{
  margin-left: 0;
}
.support-link {
  p {
    font-weight: 300;
  }
  a {
    text-decoration: underline;
    color: @white;
    font-weight: 500;
    &:hover {
      color: #F79420;
    }
  }
}
#download-app-btn-container{
  background-color: @white;
  position: fixed;
  bottom: 80px;
  right: 0px;
  border-top-left-radius:10px;
  border-bottom-left-radius:10px;
  z-index: 99;
}

.new-download-app-btn {
  padding: 3px 5px;
  border-top-left-radius:10px;
  border-bottom-left-radius:10px;
  border: 2px solid #AE3591;
  border-right-color: transparent;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
  display: none;
  font-size: 12px;

  @media @iPhone {
    display: block;
  }
}
.mt-8{
  margin-top:3rem;
}
.searchShowHide{
  input::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
    color:rgba(68, 68, 68, 0.48) !important;
    font-size: 12px !important;
    font-style: normal !important;
  }

  input:-ms-input-placeholder { /* Internet Explorer 10-11 */
    color:rgba(68, 68, 68, 0.48) !important;
    font-size: 12px !important;
    font-style: normal !important;
  }

  input::-ms-input-placeholder { /* Microsoft Edge */
    color:rgba(68, 68, 68, 0.48) !important;
    font-size: 12px !important;
    font-style: normal !important;
  }
  .form-inline{
    width: 400px;
    display: flex;
    flex-wrap: nowrap;
    position: relative;
    margin: 0 auto;
    margin-top: 1rem;
    @media @iPhone{
      width: 300px;
    }
  }
  h2{
    text-align: center;
    font-weight: 700;
    color:@purple;
    font-size: 30px;
  }
}
.home-search {
  position: absolute;
  right: 0px;
  top: 1px;
  background-color: transparent !important;
  &:focus{
    outline:none !important;
  }
  &:active{
    outline:none !important;
  }
}
.btn-call{
  background: #FF8C04;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-decoration: none !important;
  i{
    margin-right: 10px;
  }
  &:hover{
    color:#fff !important;
  }
}

.circle-tr{
  width: 500px;
  height: 500px;
  position: absolute;
  background: radial-gradient(82.18% 82.18% at 26.55% -4.71%, #8600BF 0%, rgba(212, 112, 255, 0) 100%);
  top: -25px;
  left: -171px;
  border-radius: 50%;
  opacity: 0.8;
}
.sidebar{
  height: 100%;
  width: 0;
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  background-color: #fff;
  overflow-x: hidden;
  transition: 0.5s;
  padding-top: 60px;
  box-shadow: 3px 0 5px 0px rgba(68,68,68,0.5);
  .media{
    img{
      width: 30px;
    }
  }
}
.sidebar a {
  padding: 8px 8px 8px 32px;
  text-decoration: none;
  font-size: 18px;
  color: #818181;
  display: block;
  transition: 0.3s;
}
.sidebar a.nav-link{
  img{
    width: 42px;
  }
}
.sidebar a:hover {
  color: #f1f1f1;
}

.sidebar .closebtn {
  position: absolute;
  top: 0;
  right: 25px;
  font-size: 36px;
  margin-left: 50px;
}


.app-store{
  img{
    width: 120px;
  }
}
.header-balon{

}
@-webkit-keyframes float_left_right {
  0% {
    -webkit-transform:translateX(-10px);
    transform:translateX(-10px);
  }
  50% {
    -webkit-transform:translateX(10px);
    transform:translateX(10px);
  }
  100% {
    -webkit-transform:translateX(-10px);
    transform:translateX(-10px);
  }
}
@keyframes float_left_right {
  0% {
    -webkit-transform:translateX(-10px);
    transform:translateX(-10px);
  }
  50% {
    -webkit-transform:translateX(10px);
    transform:translateX(10px);
  }
  100% {
    -webkit-transform:translateX(-10px);
    transform:translateX(-10px);
  }
}
.login-btns{
  position: relative;
  top: -1.3rem;
  right: 20px;
  .dropdown{
    &:before {
      content: '';
      position: absolute;
      right: -110px;
      top: -20px;
      background-image: url('../../images/ws/baloon.png');
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      width: 120px;
      height: 100px;
      transition: all 0.5s linear;
      -webkit-transition: all 0.5s linear;
      -moz-transition: all 0.5s linear;
      -ms-transition: all 0.5s linear;
      animation: float_left_right 10s infinite;
      -webkit-animation: float_left_right 10s infinite;
      -moz-animation: float_left_right 10s infinite;
      -ms-animation: float_left_right 10s infinite;
      @media @iPhone,@iPad-portrait{
        height: 85px;
      }
    }

    &:after {
      content: '';
      position: absolute;
      right: -10px;
      top: -10px;
      background-image: url('../../images/ws/smallbaloon.svg');
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      width: 40px;
      height: 45px;
      transition: all 0.5s linear;
      -webkit-transition: all 0.5s linear;
      -moz-transition: all 0.5s linear;
      -ms-transition: all 0.5s linear;
      animation: float_left_right 10s infinite;
      -webkit-animation: float_left_right 9s infinite;
      -moz-animation: float_left_right 9s infinite;
      -ms-animation: float_left_right 9s infinite;
    }
  }
  .btn-default{
    background: none;
    border: 1.25px solid #FFFFFF;
    box-sizing: border-box;
    filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.1));
    border-radius: 10px;
    font-size: 12px;
    color:#fff;
  }
  @media @iPhone{
    position: absolute;
    top: 20px;
    right: -8px;
    padding: 0;
  }
}
#search-book{
  background: #FFFFFF;
  box-shadow: inset 0px 2px 10px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
}
.banner {
  .navbar-dark .navbar-brand {
     position: relative;
    z-index: 9999;
  }
}
.footer-menus {
  p {
    color: #fff;
  }
  #download-app-btn-container {
    z-index: 999;
  }
}
.mdl-layout__container,
.modal-backdrop {
  z-index: 9991;
}

.db-main {
  .card a {
    cursor: pointer;
  }
}
.more-options-title {
  img {
    width: 25px;
  }
  h5 {
    color: #26EDB4;
    margin-bottom: 0;
  }
}
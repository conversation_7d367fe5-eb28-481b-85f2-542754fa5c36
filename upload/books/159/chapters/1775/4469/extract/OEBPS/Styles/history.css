body {
font-family:"Times New Roman", Times, serif;
font-size:100%;
line-height:110%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}

.image {
text-align:center;
}
.author {
text-align:right;
}

.chapterHeading {
	color:#00AEEF;
	
}

.chapterNumber {
	font-size: 125%;
	font-family: "Times New Roman", Times, serif;
}

.subHeading {
	font-size:1.3em;
	color:#00AEEF;       
	margin-bottom:1%;
	font-weight:bold;
}

.center {
	text-align: center;
	font-family: "Times New Roman", Times, serif;
}

.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}

.box{
border:2px solid #6699FF;
border-radius: 5px;
padding: 15px;
margin: 15px;

}

.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:50%;
line-height:110%;
font-weight:bold;

font-size:180%;

color:#fff;

}

div.chapter_pos div

{

background:#CC0000;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.activitybox2{
background-color:#F4A460;
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.lining_box
{
	border:1px solid #3399FF;
	padding:5px;
	border-radius: 10px;
}
img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}
/* Chapter name */
h2
	{
        color:#fff;
        font-size:1.5em;
        background:#00AEEF; 
        padding:10px;
		font-weight:bold;
	}


h4
{
color:#00AEEF;   
font-size:1.3em;
}

.footer

{

display:none;

}
table {
    border-collapse: collapse;
}

table td

{

padding:10px;
border-collapse: collapse;

}

.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:40%;
font-size:1em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
.mainHeading {
	text-transform:uppercase;
	color:rgb(0, 174, 239);
	margin:0.7% 0;
	font-weight:bold;
}
.Heading {
	color:rgb(0, 174, 239);
	margin:0.7% 0;
	font-weight:bold;
	font-size: 1.2em'
}

p

{

margin-top:10px;

}

/* Chapter name */
h2
	{
        color:#fff;
        font-size:1.5em;
        background:#00AEEF; 
        padding:10px;
		font-weight:bold;
	}

h4
{
color:#00AEEF;   
font-size:1.3em;
}
}
body {
font-family:"Arial";
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}
#prelims
{
	line-height:200%;
}
#prelims .char-style-override-16, .char-style-override-18
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color:#00aeef;
}
#prelims .char-style-override-2
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:#00aeef;
}

/* Activity Box */
.activity_box {
	border:2px solid #91BDE8;
	padding:5px;
	border-radius: 15px;
	background-color: #91BDE8;
	}	

.ebox
{
	border:2px solid #00AEEF;
	padding:5px;
	border-radius: 15px;
}
.ibox {
	background:#00AEEF;
	padding:5px;
	color:#FFFFFF;
	text-transform: uppercase;
	text-align: center;
	}
.tbox{
	background:#C5EFFD;
	padding:5px;
	}
.abox{
	background:#C5EFFD;
	border:2px solid #C5EFFD;
	padding:15px;
	text-align: center;
	border-radius: 5px;
	}
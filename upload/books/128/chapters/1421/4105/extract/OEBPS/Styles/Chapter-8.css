body {
font-family:"Arial";
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}

.image {
text-align:center;
}
.author {
text-align:right;
}
char-style-override-6
{
font-size:1em;
color:#00aeef;
}
.char-style-override-23
{
}
 .char-style-override-19, .char-style-override-16
{
font-size:0.7em;
vertical-align:sub;

}
 .char-style-override-27, .char-style-override-26
{
font-size:0.7em;
vertical-align:super;

}

.chapterHeading {
font-size:160%;
color: gray;
margin-bottom:20px;
}

.chapterNumber {
font-size: 125%;
font-family: "Arial";
}

.subHeading {
color:#ce1337;
font-size:125%;
}

.center {
text-align: center;
}

.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}

.box{
background-color:#CCEFFC;
padding:15px;
font-size:0.9em;
line-height:120%;
}
.box1
{
background-color:#CCEFFC;
border:4px solid #003399;
padding:15px;
}

.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
p.heading-1 {
	font-size:1em;
	font-weight:bold;
}
p.para-style-override-1 {
	-epub-hyphens:none;
	color:#00aeef;
	font-size:2em;
	margin-bottom:6px;
}
.ConceptHeading{
	color:#00aeef;
font-size:1.3em;
font-weight:bold;
margin-top:40px;
}
.SubHeading {
	color:#00aeef;
font-size:1.1em;
font-weight:bold;
}
p.Definition {
	color:#000000;
	font-size:0.917em;
}

span.char-style-override-7, .char-style-override-17,.char-style-override-14  {
	color:#00aeef;
	font-size:0.917em;
}
.char-style-override-12
{
	color:#00aeef;
	font-weight:bold;
	font-size:100%;
}
.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding:15px;

}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:80%;
}
.img_left
{
margin-left:0px;
}
ul
{
margin-left:45px;
}
.caption  
{
font-style: italic; 
font-size: 0.83em; 
color: #4D4D4D;
text-align:center;
}
p
{
margin-top:10px;
}
h2
{
color:#fff;
font-size:1.5em;
background:#00aeef;
padding:10px;
}
h4
{
color:#00aeef;
font-size:1.3em;
}
.footer
{
display:none;
}
table td
{
padding:10px;
}
.conc
{
color:#006699;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;
width: 96%;
position:absolute;
top:40%;
font-weight:bold;
font-size:28px;
color:#fff;

}

div.chapter_pos div

{

background:#003366;
padding:10px;
width:40%;
margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{
font-size:0.7em;
color:#aeaeae;
line-height:200%;
font-weight:normal;

}

.lining_box
{
border:2px solid #000;
padding:15px;
border-radius:15px;
}
.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:60%;
font-size:1em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}

@media only screen and (max-width: 767px) {

div.chapter_pos

{

font-size:0.8em;
line-height:120%;
}

div.chapter_pos div span

{

font-size:0.5em;

}
   
}
#prelims
{
	line-height:150%;
}
#prelims .para-style-override-22, #prelims .char-style-override-22
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color:#af7b0a;
}
#prelims .char-style-override-7, .char-style-override-8
{
	font-style:italic;
	color:#000;
	font-size:1em;
	font-weight:normal;
	margin-top:0px;
}
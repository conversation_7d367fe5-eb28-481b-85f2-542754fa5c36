@charset "utf-8";

body {
	font-family:Arial, Helvetica, sans-serif;
	font-size:100%;
	line-height:130%;
	padding:2%;
	text-align:justify;
}

* {
	margin:0;
	padding:0;
}

.images {
	text-align:center;
}

.unitHeading {
	text-transform:uppercase;
	font-size:130%;
	text-align:center;
	margin:1% 0;
}

.author {
	text-align:right;
}

.unitNotes {
	text-align:center;
	font-size:115%;
}

.chapterNo {
	text-transform:uppercase;
	
	font-weight:bold;
}



.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.ConceptHeading
{
color:#d1640f;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
.subHeading {
color:#9A3334;
font-size:1.1em;
font-weight:bold;
}
.sub{
color:#d1640f;
font-size:1.2em;
font-weight:bold;
}
.topicHeading {
	margin:0.5% 0;
}

.exercise {
	text-transform:uppercase;
	text-align:center;
	margin:1.5% 0;
}
table
{
    width:100%;
    border:1px solid #000;
    border-collapse:collapse;
}
td
{
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}
.infoBox {
	width: 50%;
	margin: 2% auto;
	padding: 2%;
    border:4px solid #C4C5C8; 
}

.infoBox .head {
color: #d1640f;
font-weight: bold;
font-size:1.2em;
}
.ex
{
font-size:1.2em;
color:#9A3334;
font-weight: bold;
}

table {
	 width:100%;
    border:1px solid #000;
    border-collapse:collapse;
}

table tr:first-child {
	background: #C4C5C8;
	text-align: center;
	font-weight: bold;
	font-style: italic;
}

td { 
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}

.exerciseBox {
	border:2px solid #9A3334;
padding:15px;
border-radius:15px;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:50%;
line-height:110%;
font-weight:bold;

font-size:180%;

color:#fff;

}

div.chapter_pos div

{

background:#222;

padding:10px;

width:35%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.activitybox2{
background-color:#F4A460;
padding: 15px;

}
.lining_box
{
border:1px solid #000;
padding:5px;
}
img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:60%;
}
.img_wid
{
	
width:10%;
display:inline;
}
ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}

h2

{

color:#fff;
font-size:1.5em;
background:#9A3334;
padding:10px;

}

h4

{

color:#d1640f;
font-size:1.3em;

}

.footer

{

display:none;

}

table td

{

padding:10px;

}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;

}
.box{
background-color:#C4C5C8;
padding: 15px;
border:4px solid #999999; 

}
.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:40%;
font-size:1em;
}
div.chapter_pos div
{
width:80%;
}
.cover_img_small
{
width:90%;
}
}

#prelims .char-style-override-15, #prelims .char-style-override-19
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color:#af7b0a;
}
.char-style-override-2
{
	font-style:italic;
}
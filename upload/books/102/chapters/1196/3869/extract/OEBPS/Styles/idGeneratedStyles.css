html, body {

font-family:Arial, Helvetica, sans-serif;

}


body {

font-size:100%;

line-height:150%;

padding:2%;

text-align:justify;

}


* {

margin:0;

padding:0;

}

.box{

background-color:#e6e6e6;

padding: 15px;

font-size:0.9em;

line-height:150%;

}

img

{

max-width:100%;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

/* Chapter Name */

h1

{

color:#fff;

font-size:1.5em;

background:#FF8000;

padding:10px;

}

/* Chapter number */

h2

{

color:#3399ff;

font-size:1.3em;

}

/* Concept Heading */

h3

{

color:#CC0000;

font-size:1.3em;

font-weight:bold;

margin-top:20px;

}

/* Sub Heading */

h4

{

color:#000000;

font-size:1.1em;

font-weight:bold;

margin-top:40px;

}

/* Sub Heading 2*/

h5

{

color:#CC0000;

font-size:1.1em;

font-weight:bold;

}

.clear

{

clear:both;

}


.lining_box

{

border:2px solid #000;

padding:15px;

border-radius:15px;

}

.note

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

}

p

{

margin-top:10px;

}

table

{

width:100%;

border:1px solid #000;

border-collapse:collapse;

}

td

{

padding:10px;

border:1px solid #000;

border-collapse:collapse;

}

.cover_img_small

{

width:50%;

}

div.layout



{



text-align: center;



}



div.chapter_pos




{




text-align: center;




width: 96%;




position:absolute;




top:70%;




font-weight:bold;




font-size:28px;




color:#fff;




}




div.chapter_pos div




{




background:#B9881B;




padding:10px;




width:30%;




margin:auto;



opacity:0.9;




}




div.chapter_pos div span




{




font-size:0.7em;




color:#eaeaea;




font-weight:normal;




}



@media only screen and (max-width: 767px) {




div.chapter_pos




{




font-size:0.8em;



line-height:120%;



top:50%;



}




div.chapter_pos div span




{




font-size:0.7em;




}



}

div.layout



{



text-align: center;



}



div.chapter_pos




{




text-align: center;




width: 96%;




position:absolute;




top:70%;




font-weight:bold;




font-size:28px;




color:#fff;




}




div.chapter_pos div




{




background:#B9881B;




padding:10px;




width:31%;




margin:auto;



opacity:0.9;




}




div.chapter_pos div span




{




font-size:0.7em;




color:#eaeaea;




font-weight:normal;




}



@media only screen and (max-width: 767px) {




div.chapter_pos




{




font-size:0.8em;



line-height:120%;



top:50%;



}




div.chapter_pos div span




{




font-size:0.7em;




}



}






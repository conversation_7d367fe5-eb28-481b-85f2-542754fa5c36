@font-face {

font-family:"Walkman-Chanakya-905";

font-style:normal;

font-weight:bold;

src : url("../Fonts/wcb.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:italic;

font-weight:bold;

src : url("../Fonts/wcbi.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:oblique;

font-weight:bold;

src : url("../Fonts/wcbi.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:normal;

font-weight:normal;

src : url("../Fonts/wcn.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:italic;

font-weight:normal;

src : url("../Fonts/wcni.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:oblique;

font-weight:normal;

src : url("../Fonts/wcni.ttf");

}


html, body {

font-family:"Walkman-Chanakya-905";

}


body {

font-size:120%;

line-height:150%;

padding:2%;

text-align:justify;

}


* {

margin:0;

padding:0;

}
.asterik
{
font-size:0.83em;
color:rgb(0, 174, 239);
font-style:italic;

}

.image {

text-align:center;

}

.author {

text-align:right;

}


.chapterHeading {

font-size:160%;

color: gray;

margin-bottom:20px;

}


.chapterNumber {

font-size: 125%;

}


.subHeading {

color:#ce1337;

font-size:125%;

}

.activity {
font-size:1.2em;
color:rgb(0, 174, 239);
font-weight:bold;
}
.activity1 {
font-size:1.0em;
color:rgb(0, 174, 239);
font-weight:bold;
}
.center {

text-align: center;

}


.excercise {

text-transform:uppercase;

font-weight:bold;

margin:1% 0%;

}
.pink_text
{
color:#FF0080;
}

/* Hightlisght Boxes */
.NewWordBox{
background-color:#FFE4B8;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.activityBox{
background-color:#C7EAFB;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.box_b
{
background-color:#00AEEF;
color:#fff;
padding: 15px;
font-size:1.2em;
line-height:120%;
}
.box_y{
background-color:#FFF9AE;
padding: 15px;
font-size:1.2em;
line-height:120%;
}
.box_p{
background-color:#D9C0DD;
padding: 15px;
font-size:1.5em;
line-height:120%;
border:2px solid #AA0078;
}
.lining_boxp
{
border:2px solid #AA0078;
border-top:0px solid #AA0078;
padding:15px;
}
.lining_boxo
{
background-color:#FFE4B8;
border:2px solid #FF9900;
border-radius:15px;
padding:15px;
font-size:0.9em;
}
.lining_boxol
{
background-color:#FFE4B8;
border:2px solid #FF9900;
padding:15px;
font-size:0.9em;
}
.blue_box
{
background-color:#C7EAFB;
padding:15px;
font-size:0.9em;
}
.orange_text1
{
font-size:1.2em;
color:#FF9900;
}
.orange_text
{
color:#fff;
font-size:1.2em;
background:#FF9900;
padding:10px;
}
.pinky_text
{
color:#fff;
font-size:1.2em;
background:#F5A3C7;
padding:5px;
border-radius:10px;
z-index:1000;
position:absolute;
top:-10px;

}
.lining_boxpink
{
background-color:#FCDFEC;
border:2px solid #FF0080;
border-radius:15px;
padding:15px;
position:relative;
z-index:100;
padding-top:30px;
}
#exercise_green .pinky_text
{
background:#67C18C;

}
#exercise_green .lining_boxpink
{
background-color:#CCE7D3;
border:2px solid #000;
}
#exercise_orange .pinky_text
{
background:#FF9900;

}
#exercise_orange .lining_boxpink
{
background-color:#FFE4B8;
border:2px solid #FF9900;
}
/* Hightlight Boxes Heading : CSS given directly to <b> tag*/
.NewWordBox b, .activityBox b, .box b 
{
	font-weight:bold;
	font-size:1.2em;
}
/* Hightlight Boxes Sub Heading */
.NewWordBox .Subheading, .activityBox .Subheading, .box .Subheading 
{
	font-weight:bold;
	font-size:1em;
}
.img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
ul
{
	margin-left:45px;
}
.caption
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
	text-align:center;
}
/* Chapter Name */
h2
{
color:#fff;
font-size:1.5em;
background:#00AEEF;
padding:10px;
}
/* Chapter number */
h4
{
color:#AA0078;
font-size:1.3em;
}
/* Concept Heading */
.ConceptHeading
{
color:#AA0078;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
line-height:150%;
}
/* Sub Heading */
.SubHeading
{
color:#666666;
font-size:1.1em;
line-height:150%;
font-weight:bold;
}
/* Sub Heading 2*/
.SubHeading2
{
color:#d1640f;
font-size:1em;
font-weight:bold;
}
/* if Mathematics or science book use */
#MathSc img
{
	position:relative;
	top:10px;
}
#MathSc .img1
{
	position:relative;
	top:18px;
}
#MathSc .small img
{
    position:relative;
    top:5px;
    height:18px;
   
}
#MathSc .img2
{
	position:relative;
	top:30px;
}
#MathSc .img3 img
{
	position:relative;
	top:15px;
height:40px;
}
#MathSc .img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:95%;
}
.clear
{
	clear:both;
}

.lining_box
{
border:2px solid #000;
padding:15px;
border-radius:15px;
}

.img_wid

{

margin-left: auto;

margin-right: auto;

display: block;

width:80%;

}

ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}



.footer

{

display:none;

}

table td

{

padding:10px;

}

.conc

{

color:#006699;

}


.right

{

display:inline;

float:right;

clear:both;

}

.bold

{

font-size:1.0em;

font-family: Walkman-Chanakya-905;

font-weight:bold;

}

.italic

{

font-weight:bold;

font-size:100%;

color:#03C;

}

.center

{

text-align:center;

}

.right

{

text-align:right;

}

.background

{

background:#999;

font-weight:bold;

}

.superscript{

position:relative;

top:-15%;

font-size: 85%;

font-family:Arial, Helvetica, sans-serif;

}


.subscript{

position:relative;

bottom:-25%;

font-size: 85%;

font-family:Arial, Helvetica, sans-serif;

}

.work

{

font-size:105% ;

}

div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:40%;

font-weight:bold;

font-size:1.5em;

color:#fff;

}

div.chapter_pos div

{

background:#421C52;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.subjectHead {
	font-size:115%;
	text-align:left;
	font-weight:bold;
	color:#F0F;
}

.chapterHeading {
	text-align:left;
	font-weight:bold;
	font-size:180%;
	color:#0CF;
}

.chapterSubheading {
	text-align:left;
	font-weight:bold;
	font-size:140%;
	color:#0000A6;
}

.subheadingBlue {
	font-size:130%;
	font-weight:bold;
	color:#0CF;
}
.subheadingBlue1 {
	font-size:100%;
	font-weight:bold;
	color:#0CF;
}
.bold {
	font-weight:bold;
}

.subheading {
	font-size:115%;
	font-weight:bold;
	color:#306;
}
.subheading1 {
	font-size:115%;
	font-weight:bold;
	color:#669;
}

.chapterImage {
	height:100px;
	width:100px;
	float:right;
	margin-left:1%;
}

.chapter {
	text-align:left;
	font-weight:bold;
	font-size:130%;
	color:#C85C44;
}

.chapterNumber {
	text-align:left;
	font-weight:bold;
	font-size:150%;
	color:#606;
}

.chapterImage:after {
	clear:both;
}

.image {
	text-align: center;
}

.center {
	text-align: center;
	font-family: "Walkman-Chanakya-905";
}

.left {
	float:left;
}

.right {
	float:right;
}

.numbers {
	text-align:center;
	font-size:120%;
	font-weight:bold;
}

.englishMeaning
{
	font-family:Arial, Helvetica, sans-serif;
	font-size:0.8em;
	}
.superscript
{
   position:relative;
   top:-15%;
   font-size: 95%;
}

.subscript
{
   position:relative;
   bottom:-10%;
   font-size: 90%;
}
.question
{
	font-weight:bold;
	font-size:140%;
	text-align:center;
	}
.DoYouKnow
{
	font-size:110%;
	font-weight:bold;
	color:#F60;
}
.arrow
{
	position:relative;
top:15px;
left:-80px;
}
.cover_img_small

{

width:50%;

}
@media only screen and (max-width: 767px) {


div.chapter_pos


{

top:30%;

font-size:1em;

}

div.chapter_pos div


{

width:70%;



}

.cover_img_small

{

width:90%;

}

}
.underline_txt

{

font-decoration:underline;

}

.bold_txt

{

font-weight:bold;

}

.center_element

{

margin:auto;

}

.italics_txt

{

font-style:italic;

}

.block_element

{

display:block;

}

.img_rt

{

float:right;

clear:both;

}

.img_lft

{

float:left;

}
table
{
    width:100%;
    border:1px solid #000;
    border-collapse:collapse;
}
td
{
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}

#prelims .char-style-override-27
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-3
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:rgb(236, 0, 140); 
}
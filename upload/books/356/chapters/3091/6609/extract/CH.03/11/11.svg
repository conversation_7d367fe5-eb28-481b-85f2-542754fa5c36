<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg viewBox="0 0 935 1210" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
<style type="text/css"><![CDATA[
.g0_11{
fill: #231F20;
}
.g1_11{
fill: none;
stroke: #231F20;
stroke-width: 1.4666667;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g2_11{
fill: none;
stroke: #939598;
stroke-width: 6.05;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g3_11{
fill: #EFEEEE;
}
.g4_11{
fill: #FFFFFF;
}
.g5_11{
fill: #EAF0F0;
}
.g6_11{
fill: #D2D9D8;
}
.g7_11{
fill: #536161;
}
.g8_11{
fill: #F8F1DE;
}
.g9_11{
fill: #D5222C;
}
.g10_11{
fill: #EE2229;
}
.g11_11{
fill: #AD2930;
}
.g12_11{
fill: #BFC8C7;
}
.g13_11{
fill: #F15D59;
}
.g14_11{
fill: #E93233;
}
]]></style>
</defs>
<path d="M55,1150.9h50.3V1126H55v24.9Z" class="g0_11" />
<path d="M55,1151.7H825" class="g1_11" />
<path d="M265.6,79.2V1088.5" class="g2_11" />
<path fill-rule="evenodd" d="M162.9,208.5L139.5,113.2L129.4,103.1L71.3,82L53.2,173.3l109.7,35.2Z" class="g3_11" />
<path fill-rule="evenodd" d="M161.8,204.4L139,116.3L128.6,105.6L74.9,82.5L57,171.3l104.8,33.1Z" class="g4_11" />
<path fill-rule="evenodd" d="M139,113.5l.3,.4l21.9,87.7l-.7,-.3L138.3,113.8l.3,.4l.4,-.7Z" class="g0_11" />
<path fill-rule="evenodd" d="M128.6,103.7l.1,.1l10.3,9.7l-.4,.8l-10.4,-9.8l.3,.2l.1,-1Z" class="g0_11" />
<path fill-rule="evenodd" d="M73.6,82.8l55,20.9l-.1,1L73.5,83.9l.1,-1.1Z" class="g0_11" />
<path fill-rule="evenodd" d="M56.7,168.6L73.6,83.2l1.1,.5L58,168.9l-1.3,-.3Z" class="g0_11" />
<path fill-rule="evenodd" d="M161.3,201.7L57.7,168.8v-1l103.4,33.1l.2,.8Z" class="g0_11" />
<path fill-rule="evenodd" d="M138.8,114l-.6,-.5c-3,-2.8,-6.1,-5.7,-9.1,-8.5l-.6,-.7l.1,.8c.2,1.6,.6,2.8,.7,4.5l.3,.9l.6,.2c2.6,1,5.3,2.1,8,3.1l.6,.2Z" class="g5_11" />
<path fill-rule="evenodd" d="M127.9,104.2l.8,-.1c3.3,2.9,6.4,6,9.7,9l.7,.6l-.5,.7l-.7,-.6c-3,-2.8,-6.1,-5.8,-9.2,-8.5l-.5,-.7l.8,-.1l-1.1,-.3Z" class="g0_11" />
<path fill-rule="evenodd" d="M129.5,111.1l-.4,-.6c-.4,-1.7,-.8,-3.7,-1,-5.4l-.2,-.9l1.1,.3l.1,.6c.3,1.6,.4,3.2,.8,4.8l.1,.6l-.4,-.4l-.1,1Z" class="g0_11" />
<path fill-rule="evenodd" d="M139.1,113.7l-.4,.9l-.6,-.2c-2.6,-1,-5.3,-2.1,-8,-3.1l-.6,-.2l.1,-1l.6,.2c2.7,1,5.3,2,8,3l.6,.3l-.2,.8l.5,-.7Z" class="g0_11" />
<path fill-rule="evenodd" d="M135.7,122L79.2,100.4l-.5,2.3l57.6,22l-.6,-2.7Z" class="g6_11" />
<path fill-rule="evenodd" d="M137.3,129.6L78,107l-.3,2.6l60.3,23l-.7,-3Z" class="g6_11" />
<path fill-rule="evenodd" d="M139.1,138.1L76.9,114.3l-.4,2.9l63.3,24.2l-.7,-3.3Z" class="g6_11" />
<path fill-rule="evenodd" d="M141,147.4l-65.3,-25l-.6,3.2L141.6,151l-.6,-3.6Z" class="g6_11" />
<path fill-rule="evenodd" d="M143,157.6L74.2,131.3l-.6,3.6l70.3,26.9l-.9,-4.2Z" class="g6_11" />
<path fill-rule="evenodd" d="M145.3,169L72.7,141.3l-.6,4l74.1,28.3l-.9,-4.6Z" class="g6_11" />
<path fill-rule="evenodd" d="M107.3,113.1l-1.2,1.8L104,111.7v-.2l-.1,-.2v-.2l.1,-.1l.2,-.1l.1,-.2l.2,.1l.3,-.2l3.6,.7l-1.1,1.8Z" class="g7_11" />
<path fill-rule="evenodd" d="M106.1,114.9l5.8,9.2l4.7,-4.7l2.5,-6.8l-10.7,-1.3l-2.3,3.6Z" class="g8_11" />
<path fill-rule="evenodd" d="M117.3,115.9l-.4,.1c-2.3,.5,-3.1,2.5,-2.9,4.6v.3l38.7,24.6l3.1,-5L117.3,115.9Z" class="g9_11" />
<path fill-rule="evenodd" d="M117.2,115.9v-.3l-.2,-.3v-.7l.2,-.3l.1,-.3v-.3l.2,-.3l.3,-.2l.1,-.2l.2,-.1l.3,-.1l.1,-.1l.3,-.2l.3,.1h.2l38.6,24.5l-2.1,3.4L117.2,115.9Z" class="g10_11" />
<path fill-rule="evenodd" d="M112.1,124.1l-.2,-.3v-.3c-.3,-.4,-.2,-1,.1,-1.4l.2,-.3l.1,-.1l.2,-.3l.3,-.1l.1,-.2l.4,-.1l.3,-.1l.3,-.1h.3l38.6,24.5l-2.1,3.4L112.1,124.1Z" class="g11_11" />
<path fill-rule="evenodd" d="M157.9,137.1l-7.2,11.6l7,4.4l7.2,-11.5l-7,-4.5Z" class="g5_11" />
<path fill-rule="evenodd" d="M151.9,146.6l-1.3,2.1l6.4,3.9l1.4,-2l-6.5,-4Z" class="g12_11" />
<path fill-rule="evenodd" d="M170.6,148.2l-4.6,7.2c-.7,1,-2,1.7,-3.1,1l-5.2,-3.3L165,141.6l5.1,3.3h.2l.1,.2l.2,.1l.1,.2v.3l.2,.1v.5l.2,.1v.3l-.2,.3v.5l-.1,.2l-.2,.2v.3Z" class="g13_11" />
<path fill-rule="evenodd" d="M159.2,150.9l4.9,3.3c.7,.3,1.8,.8,2.5,.1l.3,-.1l-.9,1.3l-.3,.5l-.5,.2l-.5,.3l-.4,.1l-.5,.1l-.5,-.1l-.4,-.2l-.3,-.3l-4.9,-3l1.5,-2.2Z" class="g14_11" />
<path fill-rule="evenodd" d="M165.2,155.5H165l2.7,-4.1l.8,.5l-2.5,4.2l-.2,.2l-.6,-.8Z" class="g0_11" />
<path fill-rule="evenodd" d="M163.2,156.2L163,156l.2,.2c.5,0,1.1,-.2,1.7,-.5l.3,-.2l.6,.8l-.3,.2c-1,.5,-1.7,.7,-2.8,.5l-.1,-.2l.6,-.6Z" class="g0_11" />
<path fill-rule="evenodd" d="M112.4,124l-.2,-.1l51,32.3l-.6,.6L111.8,124.7l-.2,-.3l.8,-.4Z" class="g0_11" />
<path fill-rule="evenodd" d="M104.5,111.5l7.9,12.5l-.8,.4l-8,-12.5l.9,-.4Z" class="g0_11" />
<path fill-rule="evenodd" d="M104.5,111.3v.2l-.9,.4v-.2l-.2,-.2V111l.1,-.2v-.1l1,.6Z" class="g0_11" />
<path fill-rule="evenodd" d="M104.6,111.2h-.1v.1l-1,-.6l.2,-.2l.3,-.1l.3,-.1h.3v.9Z" class="g0_11" />
<path fill-rule="evenodd" d="M119,113l.3,.2l-14.7,-2v-.9l14.7,1.8l.3,.2l-.6,.7Z" class="g0_11" />
<path fill-rule="evenodd" d="M169.8,145.1l.1,.2L119,113l.6,-.7l50.8,32.2l.2,.1l-.8,.5Z" class="g0_11" />
<path fill-rule="evenodd" d="M170.3,147.4h-.1l.1,-.4c0,-.7,.1,-1,-.3,-1.5l-.2,-.4l.8,-.5l.1,.3c.6,.9,.7,1.9,.5,2.9l-.1,.1l-.8,-.5Z" class="g0_11" />
<path fill-rule="evenodd" d="M167.7,151.4l2.6,-4l.8,.5l-2.6,4l-.8,-.5Z" class="g0_11" />
<path fill-rule="evenodd" d="M147.1,181.8L70.7,152.6l-.6,4.2L148,186.6l-.9,-4.8Z" class="g6_11" />
<path d="M157.7,190.1h99m-204.6,0h51.5" class="g1_11" />
<image preserveAspectRatio="none" x="275" y="79" width="550" height="29" xlink:href="/funlearn/downloadEpubImage?source=upload/books/356/chapters/3091/6609/extract/CH.03/11/img/1.png" />
</svg>
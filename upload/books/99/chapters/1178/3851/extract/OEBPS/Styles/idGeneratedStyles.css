
html, body {
font-family:Arial, Helvetica, sans-serif;
}

body {
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}
.boxgreen{
background-color:#DFB957;
padding: 15px;
font-size:1.em;
line-height:150%;
}
.boxgreen1{
background-color:#A99B54;
padding: 15px;
font-size:1.em;
line-height:150%;
}
.boxblue{
background:#3BC7D1;
padding: 15px;
color:#FFFFFF;
font-size:0.9em;
line-height:150%;
}

.boxbrown{
background-color:#FFF2D4;
padding: 15px;
color:black;
font-size:1.em;
line-height:150%;
}
.boxred{
background-color:#FFF2D4;
padding: 15px;
font-size:1.em;
line-height:150%;
}
.boxyellow{
background-color:#FFE6AA;
padding: 15px;
font-size:0.9em;
border:6px solid #FFD15A;
line-height:150%;
}

.boxpurple{

background:#8E96CD;
padding: 15px;
color:#FFFFFF;
font-size:1.3em;
line-height:150%;
}
.boxpurple2{

background:#8E96CD;
padding: 15px;
color:black;
line-height:150%;
}

.boxpurple1{
background-color:#D4D7EC;
padding: 15px;
color:black;
line-height:150%;
}


.box21{
background-color:#D4D7EC;
font-size:0.9em;
line-height:150%;
padding: 15px;
}
.white{
background-color:#FFFFF;
border:2px solid black;
font-size:1.em;
line-height:150%;
padding: 15px;
}
.white1{
background-color:#F5F5F5;
border:2px solid black;
font-size:1.em;
line-height:150%;
padding: 15px;
}
.box3{
background-color:#8E96CD;
padding: 15px;
font-size:1.1em;
line-height:150%;
}

.box4{
background:#D4D7EC;
padding: 15px;
font-size:0.9em;
line-height:150%;
}
img
{
	max-width:100%;
}
h4,.caption
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
	text-align:center;
}
/* Chapter Name */
h1
{
color:#FFFFFF;
font-size:1.3em;
background:#8E96CD;
padding:10px;
}
/* Chapter number */
h2
{
color:black;
font-size:1.3em;

width:auto;
}
.underline{
color:#85868A;
font-size:1.3em;
text-decoration: none;
 border-bottom: 3px solid #F9E05E;
width:auto;
}
/* Concept Heading */
h3
{
color:#7C868B;
font-size:1.1em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */

/* Sub Heading 2*/
h5
{
color:#CC0000;
font-size:1.1em;
font-weight:bold;
}
.clear
{
	clear:both;
}

.lining_box
{
border:2px solid #000;
padding:15px;
border-radius:15px;
}
.note
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
}
p
{
	margin-top:10px;
}
table
{
background:#FFFFFF;
	width:100%;
	border:1px solid #6370BA;
	border-collapse:collapse;
}
td
{
	padding:10px;
	border:1px solid #6370BA;
	border-collapse:collapse;
}

p.resize img, .resize img
{

position:relative;
top:15px;
}
.cover_img_small
{
width:50%;
}
div.layout

{

text-align: center;

}

div.chapter_pos


{


text-align: center;


width: 96%;


position:absolute;


top:70%;


font-weight:bold;


font-size:28px;


color:#fff;


}


div.chapter_pos div


{


background:#993300;


padding:10px;


width:40%;


margin:auto;

opacity:0.9;


}


div.chapter_pos div span


{


font-size:0.7em;


color:#eaeaea;


font-weight:normal;


}

@media only screen and (max-width: 767px) {


div.chapter_pos


{


font-size:0.8em;

line-height:120%;

top:50%;

}


div.chapter_pos div span


{


font-size:0.7em;


}

}
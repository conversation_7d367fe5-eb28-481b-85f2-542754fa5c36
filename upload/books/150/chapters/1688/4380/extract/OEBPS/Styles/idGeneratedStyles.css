html, body {
font-family:Arial, Helvetica, sans-serif;
}

body {
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}
p.para-style-override-2 {
	color:#00aeef;
}
.image {
text-align:center;
}

.subjectHead {
text-align:right;
text-transform:uppercase;
font-size:150%;
margin-bottom:3%;
color:rgb(222,118,28);
}

.chapterText {
font-size:130%;
}
p.resize img, .resize img
{
height:60px;
position:relative;
top:5px;
}
p.resize1 img, .resize1 img
{
position:relative;
top:15px;
}
p.resize2 img, .resize2 img, img.resize2
{

position:relative;
top:15px;
}
img{
max-width:90%;
}
p.resize3 img, .resize3 img
{
height:75px;
position:relative;
top:25px;
} 
.mainHead {
font-size:120%;
font-weight:bold;
margin:2% 0;
}
.lining_border
{
	border:1px solid #333;
	padding:7px;
}
.activity {
font-size:120%;
color:rgb(0, 174, 239);
margin:2% 0;
}
span.char-style-override-3 {
	font-style:italic;
	
}

p.heading {
	color:#005476;
	font-size:1.167em;
	font-style:normal;
}
span.char-style-override-6 {
	color:#005476;
	font-size:1.143em;
	font-style:italic;
	font-weight:600;
}
.endnote {
font-size:95%;
padding:2%;
}

.questions {
font-size:125%;
margin:2% 0;
color:rgb(222,118,28);
}

.exercises {
color:rgb(46, 49, 146);
font-size:115%;
margin:2% 0;
}
.center {
	text-align: center;
}

.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}

.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

ul
{
	margin-left:45px;
}
.caption
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
	text-align:center;
}
.note
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
}
p
{
	margin-top:10px;
}
h2
{
color:#fff;
font-size:1.5em;
background:#089de3;
padding:10px;
}
h2.Sub-Heading
{
color:#089de3;
font-size:1.1em;
background:#fff;
padding-left:0px;
}

h4

{

color:#089de3;
font-size:1.3em;
margin-top:40px;
margin-bottom:40px;

}
.box_gb
{
background:#A5C9D8;
padding:15px;
}
.lining_box
{
border:2px solid #00aeef;
padding:15px;
border-radius:15px;
}
.footer
{
	display:none;
}
table td
{
	padding:10px;
}
.conc
{
	color:#006699;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;
line-height:150%;
top:50%;

font-weight:bold;

font-size:1.5em;

color:#fff;

}

div.chapter_pos div

{

background:#444;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
max-width:100%;
}

.cover_img_small

{

width:50%;

}

@media only screen and (max-width: 767px) {


div.chapter_pos


{

top:50%;

font-size:1em;

}

div.chapter_pos div


{

width:80%;

}

.cover_img_small

{

width:90%;

}

}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:80%;
}
.bold
{
	font-weight:bold;
}
body {
font-family:"Arial";
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}
#prelims
{
	line-height:200%;
}
#prelims .char-style-override-16
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color:#00aeef;
}
#prelims .char-style-override-1
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:#00aeef;
}





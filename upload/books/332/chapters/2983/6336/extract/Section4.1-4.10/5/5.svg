<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg viewBox="0 0 935 1210" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
<style type="text/css"><![CDATA[
.g0_5{
fill: none;
stroke: #000000;
stroke-width: 3.4375;
stroke-linecap: butt;
stroke-linejoin: round;
}
.g1_5{
fill: #BADFE2;
}
.g2_5{
fill: none;
stroke: #000000;
stroke-width: 1.1458334;
stroke-linecap: butt;
stroke-linejoin: round;
}
]]></style>
</defs>
<path d="M384.6,358.8v0" class="g0_5" />
<path fill-rule="evenodd" d="M294.8,282.3l-6,.3l-5.6,1.1l-5.4,1.8l-5.2,2.3l-4.8,2.9l-4.3,3.5l-4,3.9l-3.4,4.5l-3,4.7l-2.3,5.1l-1.8,5.5l-1,5.7l-.4,5.9l.4,5.9l1,5.8l1.8,5.4l2.3,5.1l3,4.8l3.4,4.4l4,3.9l4.3,3.5l4.8,2.9l5.2,2.3l5.4,1.8l5.6,1.1l6,.3l5.9,-.3l5.7,-1.1l5.4,-1.8l5.2,-2.3l4.8,-2.9l4.3,-3.5l4,-3.9l3.4,-4.4l3,-4.8l2.3,-5.1l1.7,-5.4l1.1,-5.8l.4,-5.9l-.4,-5.9l-1.1,-5.7l-1.7,-5.5l-2.3,-5.1l-3,-4.7l-3.4,-4.5l-4,-3.9l-4.3,-3.5L317,287.8l-5.2,-2.3l-5.4,-1.8l-5.7,-1.1l-5.9,-.3Z" class="g1_5" />
<path d="M294.8,282.3l-6,.3l-5.6,1.1l-5.4,1.8l-5.2,2.3l-4.8,2.9l-4.3,3.5l-4,3.9l-3.4,4.5l-3,4.7l-2.3,5.1l-1.8,5.5l-1,5.7l-.4,5.9l.4,5.9l1,5.8l1.8,5.4l2.3,5.1l3,4.8l3.4,4.4l4,3.9l4.3,3.5l4.8,2.9l5.2,2.3l5.4,1.8l5.6,1.1l6,.3l5.9,-.3l5.7,-1.1l5.4,-1.8l5.2,-2.3l4.8,-2.9l4.3,-3.5l4,-3.9l3.4,-4.4l3,-4.8l2.3,-5.1l1.7,-5.4l1.1,-5.8l.4,-5.9l-.4,-5.9l-1.1,-5.7l-1.7,-5.5l-2.3,-5.1l-3,-4.7l-3.4,-4.5l-4,-3.9l-4.3,-3.5L317,287.8l-5.2,-2.3l-5.4,-1.8l-5.7,-1.1l-5.9,-.3Z" class="g2_5" />
<path d="M474.4,358.8h44.9" class="g0_5" />
<path fill-rule="evenodd" d="M564.2,282.3l-6,.3l-5.6,1.1l-5.4,1.8l-5.2,2.3l-4.8,2.9l-4.3,3.5l-4,3.9l-3.4,4.5l-3,4.7l-2.3,5.1l-1.7,5.5l-1.1,5.7l-.4,5.9l.4,5.9l1.1,5.8l1.7,5.4l2.3,5.1l3,4.8l3.4,4.4l4,3.9l4.3,3.5l4.8,2.9l5.2,2.3l5.4,1.8l5.6,1.1l6,.3l6,-.3l5.6,-1.1l5.4,-1.8l5.2,-2.3l4.8,-2.9l4.3,-3.5l4,-3.9l3.4,-4.4l3,-4.8l2.3,-5.1l1.8,-5.4l1,-5.8l.4,-5.9l-.4,-5.9l-1,-5.7l-1.8,-5.5l-2.3,-5.1l-3,-4.7l-3.4,-4.5l-4,-3.9l-4.3,-3.5l-4.8,-2.9l-5.2,-2.3l-5.4,-1.8l-5.6,-1.1l-6,-.3Z" class="g1_5" />
<path d="M564.2,282.3l-6,.3l-5.6,1.1l-5.4,1.8l-5.2,2.3l-4.8,2.9l-4.3,3.5l-4,3.9l-3.4,4.5l-3,4.7l-2.3,5.1l-1.7,5.5l-1.1,5.7l-.4,5.9l.4,5.9l1.1,5.8l1.7,5.4l2.3,5.1l3,4.8l3.4,4.4l4,3.9l4.3,3.5l4.8,2.9l5.2,2.3l5.4,1.8l5.6,1.1l6,.3l6,-.3l5.6,-1.1l5.4,-1.8l5.2,-2.3l4.8,-2.9l4.3,-3.5l4,-3.9l3.4,-4.4l3,-4.8l2.3,-5.1l1.8,-5.4l1,-5.8l.4,-5.9l-.4,-5.9l-1,-5.7l-1.8,-5.5l-2.3,-5.1l-3,-4.7l-3.4,-4.5l-4,-3.9l-4.3,-3.5l-4.8,-2.9l-5.2,-2.3l-5.4,-1.8l-5.6,-1.1l-6,-.3Z" class="g2_5" />
<path d="M429.5,326.2v0" class="g0_5" />
<path fill-rule="evenodd" d="M429.5,184.5l-5.9,.3l-5.8,1.1l-5.4,1.7l-5.1,2.4l-4.8,2.9l-4.4,3.5l-3.9,3.9l-3.5,4.4l-2.9,4.8l-2.3,5.1l-1.8,5.4l-1.1,5.7l-.4,6l.4,5.9l1.1,5.7l1.8,5.4l2.3,5.1l2.9,4.9l3.5,4.3l3.9,4l4.4,3.4l4.8,2.9l5.1,2.4l5.4,1.7l5.8,1.1l5.9,.4l5.9,-.4l5.7,-1.1l5.5,-1.7l5.1,-2.4l4.8,-2.9l4.4,-3.4l3.9,-4l3.5,-4.3l2.9,-4.9l2.3,-5.1l1.8,-5.4l1.1,-5.7l.3,-5.9l-.3,-6L475.3,220l-1.8,-5.4l-2.3,-5.1l-2.9,-4.8l-3.5,-4.4l-3.9,-3.9l-4.4,-3.5L451.7,190l-5.1,-2.4l-5.5,-1.7l-5.7,-1.1l-5.9,-.3Z" class="g1_5" />
<path d="M429.5,184.5l-5.9,.3l-5.8,1.1l-5.4,1.7l-5.1,2.4l-4.8,2.9l-4.4,3.5l-3.9,3.9l-3.5,4.4l-2.9,4.8l-2.3,5.1l-1.8,5.4l-1.1,5.7l-.4,6l.4,5.9l1.1,5.7l1.8,5.4l2.3,5.1l2.9,4.9l3.5,4.3l3.9,4l4.4,3.4l4.8,2.9l5.1,2.4l5.4,1.7l5.8,1.1l5.9,.4l5.9,-.4l5.7,-1.1l5.5,-1.7l5.1,-2.4l4.8,-2.9l4.4,-3.4l3.9,-4l3.5,-4.3l2.9,-4.9l2.3,-5.1l1.8,-5.4l1.1,-5.7l.3,-5.9l-.3,-6L475.3,220l-1.8,-5.4l-2.3,-5.1l-2.9,-4.8l-3.5,-4.4l-3.9,-3.9l-4.4,-3.5L451.7,190l-5.1,-2.4l-5.5,-1.7l-5.7,-1.1l-5.9,-.3Z" class="g2_5" />
<path d="M401.7,411.6v38.2" class="g0_5" />
<path fill-rule="evenodd" d="M346.3,440.7l-6,.3l-5.6,1.1l-5.5,1.7l-5.2,2.4l-4.7,2.9l-4.3,3.5l-4,3.9l-3.5,4.4l-2.9,4.8l-2.3,5.1l-1.8,5.4l-1.1,5.7l-.3,6l.3,5.9l1.1,5.7l1.8,5.4l2.3,5.1l2.9,4.9l3.5,4.3l4,4l4.3,3.4l4.7,2.9l5.2,2.4l5.5,1.8l5.6,1l6,.4l5.9,-.4l5.7,-1l5.4,-1.8l5.2,-2.4l4.7,-2.9l4.4,-3.4l4,-4l3.4,-4.3l3,-4.9l2.3,-5.1l1.7,-5.4l1.1,-5.7l.4,-5.9l-.4,-6L392,476.2l-1.7,-5.4L388,465.7l-3,-4.8l-3.4,-4.4l-4,-3.9l-4.4,-3.5l-4.7,-2.9l-5.2,-2.4l-5.4,-1.7L352.2,441l-5.9,-.3Z" class="g1_5" />
<path d="M346.3,440.7l-6,.3l-5.6,1.1l-5.5,1.7l-5.2,2.4l-4.7,2.9l-4.3,3.5l-4,3.9l-3.5,4.4l-2.9,4.8l-2.3,5.1l-1.8,5.4l-1.1,5.7l-.3,6l.3,5.9l1.1,5.7l1.8,5.4l2.3,5.1l2.9,4.9l3.5,4.3l4,4l4.3,3.4l4.7,2.9l5.2,2.4l5.5,1.8l5.6,1l6,.4l5.9,-.4l5.7,-1l5.4,-1.8l5.2,-2.4l4.7,-2.9l4.4,-3.4l4,-4l3.4,-4.3l3,-4.9l2.3,-5.1l1.7,-5.4l1.1,-5.7l.4,-5.9l-.4,-6L392,476.2l-1.7,-5.4L388,465.7l-3,-4.8l-3.4,-4.4l-4,-3.9l-4.4,-3.5l-4.7,-2.9l-5.2,-2.4l-5.4,-1.7L352.2,441l-5.9,-.3Z" class="g2_5" />
<path d="M457.3,411.6L485,449.8" class="g0_5" />
<path fill-rule="evenodd" d="M512.7,440.7l-5.9,.3l-5.7,1.1l-5.4,1.7l-5.2,2.4l-4.7,2.9l-4.4,3.5l-4,3.9l-3.4,4.4l-3,4.8l-2.3,5.1l-1.7,5.4l-1.1,5.7l-.4,6l.4,5.9l1.1,5.7l1.7,5.4L471,510l3,4.9l3.4,4.3l4,4l4.4,3.4l4.7,2.9l5.2,2.4l5.4,1.8l5.7,1l5.9,.4l6,-.4l5.6,-1l5.5,-1.8l5.2,-2.4l4.7,-2.9l4.3,-3.4l4,-4l3.5,-4.3l2.9,-4.9l2.3,-5.1l1.8,-5.4l1,-5.7l.4,-5.9l-.4,-6l-1,-5.7l-1.8,-5.4l-2.3,-5.1l-2.9,-4.8L548,456.5l-4,-3.9l-4.3,-3.5L535,446.2l-5.2,-2.4l-5.5,-1.7L518.7,441l-6,-.3Z" class="g1_5" />
<path d="M512.7,440.7l-5.9,.3l-5.7,1.1l-5.4,1.7l-5.2,2.4l-4.7,2.9l-4.4,3.5l-4,3.9l-3.4,4.4l-3,4.8l-2.3,5.1l-1.7,5.4l-1.1,5.7l-.4,6l.4,5.9l1.1,5.7l1.7,5.4L471,510l3,4.9l3.4,4.3l4,4l4.4,3.4l4.7,2.9l5.2,2.4l5.4,1.8l5.7,1l5.9,.4l6,-.4l5.6,-1l5.5,-1.8l5.2,-2.4l4.7,-2.9l4.3,-3.4l4,-4l3.5,-4.3l2.9,-4.9l2.3,-5.1l1.8,-5.4l1,-5.7l.4,-5.9l-.4,-6l-1,-5.7l-1.8,-5.4l-2.3,-5.1l-2.9,-4.8L548,456.5l-4,-3.9l-4.3,-3.5L535,446.2l-5.2,-2.4l-5.5,-1.7L518.7,441l-6,-.3Z" class="g2_5" />
<path fill-rule="evenodd" d="M429.5,326.1l-5.9,.3l-5.8,1.2l-5.4,1.6l-5.1,2.4l-4.8,2.9l-4.4,3.5l-3.9,3.9l-3.5,4.4l-2.9,4.8l-2.3,5.1l-1.8,5.5l-1.1,5.7l-.4,5.9l.4,6l1.1,5.6l1.8,5.5l2.3,5.1l2.9,4.9l3.5,4.3l3.9,4l4.4,3.4l4.8,2.9l5.1,2.4l5.4,1.7l5.8,1.1l5.9,.4l5.9,-.4l5.7,-1.1l5.5,-1.7l5.1,-2.4l4.8,-2.9l4.4,-3.4l3.9,-4l3.5,-4.3l2.9,-4.9l2.3,-5.1l1.8,-5.5l1.1,-5.6l.3,-6l-.3,-5.9l-1.1,-5.7l-1.8,-5.5l-2.3,-5.1l-2.9,-4.8l-3.5,-4.4L460.9,338l-4.4,-3.5l-4.8,-2.9l-5.1,-2.4l-5.5,-1.6l-5.7,-1.2l-5.9,-.3Z" class="g1_5" />
<path d="M429.5,326.1l-5.9,.3l-5.8,1.2l-5.4,1.6l-5.1,2.4l-4.8,2.9l-4.4,3.5l-3.9,3.9l-3.5,4.4l-2.9,4.8l-2.3,5.1l-1.8,5.5l-1.1,5.7l-.4,5.9l.4,6l1.1,5.6l1.8,5.5l2.3,5.1l2.9,4.9l3.5,4.3l3.9,4l4.4,3.4l4.8,2.9l5.1,2.4l5.4,1.7l5.8,1.1l5.9,.4l5.9,-.4l5.7,-1.1l5.5,-1.7l5.1,-2.4l4.8,-2.9l4.4,-3.4l3.9,-4l3.5,-4.3l2.9,-4.9l2.3,-5.1l1.8,-5.5l1.1,-5.6l.3,-6l-.3,-5.9l-1.1,-5.7l-1.8,-5.5l-2.3,-5.1l-2.9,-4.8l-3.5,-4.4L460.9,338l-4.4,-3.5l-4.8,-2.9l-5.1,-2.4l-5.5,-1.6l-5.7,-1.2l-5.9,-.3Z" class="g2_5" />
<image preserveAspectRatio="none" x="398" y="222" width="65" height="28" xlink:href="img/1.png" />
<image preserveAspectRatio="none" x="269" y="297" width="52" height="56" xlink:href="img/2.png" />
<image preserveAspectRatio="none" x="532" y="318" width="67" height="28" xlink:href="img/3.png" />
<image preserveAspectRatio="none" x="402" y="362" width="56" height="28" xlink:href="img/4.png" />
<image preserveAspectRatio="none" x="314" y="455" width="66" height="53" xlink:href="img/5.png" />
<image preserveAspectRatio="none" x="483" y="455" width="61" height="56" xlink:href="img/6.png" />
</svg>
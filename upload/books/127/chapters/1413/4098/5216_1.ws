<?xml version="1.0" encoding="UTF-8" standalone="no" ?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd"><html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <link href="../Styles/Chapter-1.css" rel="stylesheet" type="text/css"/>

  <title/>
</head>

<body>
  <div class="layout">
    <div class="chapter_pos">
      <div>
        Chapter 1<br/>
        RELATIONS AND FUNCTIONS
      </div>
    </div>
  </div>

  <p style="text-align: center;"><img alt="Cover" class="cover_img_small" src="../Images/Cover.png"/><br/></p>
</body>
</html><?xml version="1.0" encoding="UTF-8" standalone="no" ?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd"><html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>Untitled-2</title>
  <link href="../Styles/Chapter-1.css" rel="stylesheet" type="text/css"/>
</head>

<body id="Untitled-2" xml:lang="en-US" xmlns:xml="http://www.w3.org/XML/1998/namespace">
  <div class="Basic-Graphics-Frame">
    <div>
      <div class="Basic-Graphics-Frame">
        <p class="heading-1 para-style-override-1"/>

        <div>
          <h4 style="text-align: left;">Chapter <span style="">1</span></h4>
        </div>

        <div>
          <p class="No-Paragraph-Style para-style-override-3"><br/></p>
        </div>

        <h2 class="heading-1 para-style-override-1">RELATIONS AND FUNCTIONS</h2>
      </div>

      <div class="Basic-Graphics-Frame">
        <p class="No-Paragraph-Style para-style-override-2"><br/></p>
      </div>

      <div class="Basic-Graphics-Frame"/>
    </div>

    <p class="No-Paragraph-Style para-style-override-4"/>

    <div style="text-align: center;">
      <span class="char-style-override-2">There is no</span> <span class="char-style-override-2">permanent place in the world for ugly mathematics .....</span>
    </div>

    <div style="text-align: center;">
      <span class="char-style-override-2">&nbsp;It may be very hard to define mathematical beauty but that is just as true of</span>
    </div>

    <div style="text-align: center;">
      <span class="char-style-override-2"><span style="font-size: 0.917em;">beauty of any kind, we may not know quite what we mean by a</span></span>
    </div>

    <div style="text-align: center;">
      <span class="char-style-override-2"><span style="font-size: 0.917em;">beautiful poem, but that does not prevent us from recognising</span></span>
    </div>

    <div style="text-align: center;">
      <span class="char-style-override-2">one when we read it. —</span> <span class="char-style-override-2">G</span><span class="char-style-override-2">. H. Hardy</span>
    </div>

    <p class="heading"><span class="char-style-override-7"><br/></span></p>

    <p class="heading"><span class="char-style-override-7"><br/></span></p>

    <p class="heading"><span class="char-style-override-7">1.1 Introduction</span></p>

    <p class="Body-Text para-style-override-5">Recall that the notion of relations and functions, domain, co-domain and range have been introduced in Class XI along with different types of specific real valued functions and their graphs. The concept of the term ‘relation’ in mathematics has been drawn from the meaning of relation in English language, according to which two objects or quantities are related if there is a recognisable connection or link between the two objects or quantities. Let A be the set of students of Class XII of a school and B be the set of students of Class XI of the same school. Then some of the examples of relations from A to B are</p>

    <p class="Body-Text para-style-override-5"><br/></p>

    <div>
      <div class="Basic-Graphics-Frame frame-2" style="text-align: center;"><img alt="Figure.tif" class="frame-1" src="../Images/image150.jpeg" style="width:30%"/></div>

      <div class="Basic-Graphics-Frame caption">
        <p class="No-Paragraph-Style para-style-override-6"><span class="char-style-override-8">Lejeune Dirichlet</span></p>

        <p class="No-Paragraph-Style para-style-override-7">(1805-1859)</p>

        <p class="No-Paragraph-Style para-style-override-7"><br/></p>
      </div>
    </div>

    <p class="Body-Text para-style-override-5"/>

    <p class="Remark-tab para-style-override-8"><span>(i) {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>)</span> <span class="char-style-override-11">∈</span> <span>A × B:</span> <span class="char-style-override-10">a</span> <span>is brother of</span> <span class="char-style-override-10">b</span><span>},</span></p>

    <p class="Body-Text para-style-override-9"><span>(ii) {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>)</span> <span class="char-style-override-11">∈</span> <span>A × B:</span> <span class="char-style-override-10">a</span> <span>is sister of</span> <span class="char-style-override-10">b</span><span>},</span></p>

    <p class="Body-Text para-style-override-10"><span>(iii) {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>)</span> <span class="char-style-override-11">∈</span> <span>A</span> <span>×</span> <span>B: age of</span> <span class="char-style-override-10">a</span> <span>is greater than age of</span> <span class="char-style-override-10">b</span><span>},</span></p>

    <p class="Body-Text para-style-override-10"><span>(iv) {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>)</span> <span class="char-style-override-11">∈</span> <span>A</span> <span>×</span> <span>B: total marks obtained by</span> <span class="char-style-override-10">a</span> <span>in the</span> <span>final examination is less than the total marks obtained by</span> <span class="char-style-override-10">b</span> <span>in the final examination},</span></p>

    <p class="Body-Text para-style-override-9"><span>(v) {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>)</span> <span class="char-style-override-11">∈</span> <span>A × B:</span> <span class="char-style-override-10">a</span> <span>lives in the same locality as</span> <span class="char-style-override-10">b</span><span>}. However, abstracting from this, we define mathematically a relation R from A to B as an arbitrary subset<br/>
    of A × B.</span></p>

    <p class="Body-Text para-style-override-8"><span>If (</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R, we say that</span> <span class="char-style-override-10">a</span> <span>is related to</span> <span class="char-style-override-10">b</span> <span>under the relation R and we write as</span> <span class="char-style-override-10"><br/>
    a</span> <span>R</span> <span class="char-style-override-10">b</span><span>. In general, (</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R, we do not bother whether there is a recognisable connection or link between</span> <span class="char-style-override-10">a</span> <span>and</span> <span class="char-style-override-10">b</span><span>. As seen in Class XI, functions are special kind of relations.</span></p>

    <p class="Body-Text"><span>In this chapter, we will study different types of relations and functions, composition of functions, invertible functions and bin</span>ary operations.</p>

    <p class="Body-Text"><br/></p>

    <p class="Body-Text"><br/></p>

    <p class="heading"><span class="char-style-override-7">1.2 Ty</span><span class="char-style-override-7">pes of Relations</span></p>

    <p class="Body-Text para-style-override-5"><span>In this section, we would like to study different types of relations. We know that a relation in a set A is a subset of A × A. Thus, the empty set</span> <span class="char-style-override-11">φ</span> <span>and A × A</span> <span>are two extreme relations. For illustration, consider a relation R</span> <span>in the set A = {1, 2, 3, 4} given by<br/>
    R = {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>):</span> <span class="char-style-override-10">a</span> <span>–</span> <span class="char-style-override-10">b</span> <span>= 10}. This is the empty set, as no pair (</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) satisfies the condition</span> <span class="char-style-override-10"><br/>
    a</span> <span>–</span> <span class="char-style-override-10">b</span> <span>= 10. Similarly, R</span><span class="char-style-override-11">′</span> <span>= {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) : |</span> <span class="char-style-override-10">a</span> <span>–</span> <span class="char-style-override-10">b</span> <span>|</span> <span class="char-style-override-11">≥</span> <span>0} is the whole set A × A, as all pairs<br/>
    (</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) in A × A satisfy |</span> <span class="char-style-override-10">a</span> <span>–</span> <span class="char-style-override-10">b</span> <span>|</span> <span class="char-style-override-11">≥</span> <span>0. These two extreme examples lead us to the following definitions.</span></p>

    <p class="Definition"><span class="char-style-override-12">Definition 1</span> <span>A relation R in a set A is called</span> <span class="char-style-override-10">empty relation</span><span>, if no element of A is related to any element of A, i.e., R =</span> <span class="char-style-override-11">φ ⊂</span> <span>A × A.</span></p>

    <p class="Definition"><span class="char-style-override-12">Definition 2</span> <span>A relation R in a set A is called</span> <span class="char-style-override-10">universal relation</span><span>, if each element of A is related to every element of A, i.e., R = A × A.</span></p>

    <p class="Body-Text para-style-override-11"><span>Both the empty relation and the universal relation are some times called</span> <span class="char-style-override-10">trivial relations</span><span>.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Example 1</span> <span>Let A be the set of all students of a boys school. Show that the relation R in A given by R = {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) :</span> <span class="char-style-override-10">a</span> <span>is sister of</span> <span class="char-style-override-10">b</span><span>} is the empty relation and R</span><span class="char-style-override-11">′</span> <span>= {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) : the difference between heights of</span> <span class="char-style-override-10">a</span> <span>and</span> <span class="char-style-override-10">b</span> <span>is less than 3 meters} is the universal relation.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> <span>Since the school is boys school, no student of the school can be sister of any student of the school. Hence, R =</span> <span class="char-style-override-11">φ</span><span>, showing that R is the empty relation. It is also obvious that the difference between heights of any two students of the school has to be less than 3 meters. This shows that R</span><span class="char-style-override-11">′</span> <span>= A × A is the universal relation.</span></p>

    <p class="Remarks para-style-override-12"><span class="char-style-override-15">Remark</span> <span>In Class XI, we have seen two ways of representing a relation, namely</span> <span>raster method and set builder method. However, a relation R</span> <span>in the set {1, 2, 3, 4} defined by R = {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) :</span> <span class="char-style-override-10">b</span> <span>=</span> <span class="char-style-override-10">a</span> <span>+ 1} is also expressed as</span> <span class="char-style-override-10">a</span> <span>R</span> <span class="char-style-override-10">b</span> <span>if and only if</span> <span class="char-style-override-10"><br/>
    b</span> <span>=</span> <span class="char-style-override-10">a</span> <span>+ 1 by many authors. We may also use this notation, as and when convenient.</span></p>

    <p class="Body-Text"><span>If (</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R, we say that</span> <span class="char-style-override-10">a</span> <span>is related to</span> <span class="char-style-override-10">b</span> <span>and we denote it as</span> <span class="char-style-override-10">a</span> <span>R</span> <span class="char-style-override-10">b</span><span>.</span></p>

    <p class="Body-Text"><span>One of the most important relation, which plays a significant role in Mathematics, is an</span> <span class="char-style-override-10">equivalence relation</span><span>. To study equivalence relation, we first consider three types of relations, namely reflexive, symmetric and transitive.</span></p>

    <p class="Definition"><span class="char-style-override-12">Definition 3</span> <span>A relation R in a set A is called</span></p>

    <p class="option-1"><span>(i)</span> <span class="char-style-override-10">reflexive</span><span>, if (</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">a</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R, for every</span> <span class="char-style-override-10">a</span> <span class="char-style-override-11">∈</span> <span>A,</span></p>

    <p class="option-1"><span>(ii)</span> <span class="char-style-override-10">symmetric</span><span>, if (</span><span class="char-style-override-10">a</span><span class="char-style-override-16">1</span><span>,</span> <span class="char-style-override-10">a</span><span class="char-style-override-16">2</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R implies that (</span><span class="char-style-override-10">a</span><span class="char-style-override-16">2</span><span>,</span> <span class="char-style-override-10">a</span><span class="char-style-override-16">1</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R, for all</span> <span class="char-style-override-10">a</span><span class="char-style-override-16">1</span><span>,</span> <span class="char-style-override-10">a</span><span class="char-style-override-16">2</span> <span class="char-style-override-11">∈</span> <span>A.</span></p>

    <p class="option-1"><span>(iii)</span> <span class="char-style-override-10">transitive</span><span>, if (</span><span class="char-style-override-10">a</span><span class="char-style-override-16">1</span><span>,</span> <span class="char-style-override-10">a</span><span class="char-style-override-16">2</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R and (</span><span class="char-style-override-10">a</span><span class="char-style-override-16">2</span><span>,</span> <span class="char-style-override-10">a</span><span class="char-style-override-16">3</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R implies that (</span><span class="char-style-override-10">a</span><span class="char-style-override-16">1</span><span>,</span> <span class="char-style-override-10">a</span><span class="char-style-override-16">3</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R, for all</span> <span class="char-style-override-10">a</span><span class="char-style-override-16">1</span><span>,</span> <span class="char-style-override-10">a</span><span class="char-style-override-16">2</span><span class="char-style-override-11">,</span> <span class="char-style-override-10">a</span><span class="char-style-override-16">3</span> <span class="char-style-override-11">∈</span> <span>A.</span></p>

    <p class="Definition para-style-override-11"><span class="char-style-override-12">Definition</span> <span class="char-style-override-12">4</span> <span>A relation R in a set A is said to be an</span> <span class="char-style-override-10">equivalence relation</span> <span>if R is reflexive, symmetric and transitive.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Example 2</span> <span>Let T be the set of all triangles in a plane with R a relation in T given by<br/>
    R = {(T</span><span class="char-style-override-16">1</span><span>, T</span><span class="char-style-override-16">2</span><span>) : T</span><span class="char-style-override-16">1</span> <span>is congruent to T</span><span class="char-style-override-16">2</span><span>}. Show that R is an equivalence relation.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> <span>R is reflexive, since every triangle is congruent to itself. Further,<br/>
    (T</span><span class="char-style-override-16">1</span><span>, T</span><span class="char-style-override-16">2</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R</span> <span class="char-style-override-11">⇒</span> <span>T</span><span class="char-style-override-16">1</span> <span>is congruent to T</span><span class="char-style-override-16">2</span> <span class="char-style-override-11">⇒</span> <span>T</span><span class="char-style-override-16">2</span> <span>is congruent to T</span><span class="char-style-override-16">1</span> <span class="char-style-override-11">⇒</span> <span>(T</span><span class="char-style-override-16">2</span><span>, T</span><span class="char-style-override-16">1</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R. Hence, R is symmetric. Moreover, (T</span><span class="char-style-override-16">1</span><span>, T</span><span class="char-style-override-16">2</span><span>), (T</span><span class="char-style-override-16">2</span><span>, T</span><span class="char-style-override-16">3</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R</span> <span class="char-style-override-11">⇒</span> <span>T</span><span class="char-style-override-16">1</span> <span>is congruent to T</span><span class="char-style-override-16">2</span> <span>and T</span><span class="char-style-override-16">2</span> <span>is congruent to T</span><span class="char-style-override-16">3</span> <span class="char-style-override-11">⇒</span> <span>T</span><span class="char-style-override-16">1</span> <span>is congruent to T</span><span class="char-style-override-16">3</span> <span class="char-style-override-11">⇒</span> <span>(T</span><span class="char-style-override-16">1</span><span>, T</span><span class="char-style-override-16">3</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R. Therefore, R is an equivalence relation.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Example 3</span> <span>Let L be the set of all lines in a plane and R be the relation in L defined as R = {(L</span><span class="char-style-override-16">1</span><span>, L</span><span class="char-style-override-16">2</span><span>) : L</span><span class="char-style-override-16">1</span> <span>is perpendicular to L</span><span class="char-style-override-16">2</span><span>}. Show that R is symmetric but neither reflexive nor transitive.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> <span>R is not reflexive, as a line L</span><span class="char-style-override-16">1</span> <span>can not be perpendicular to itself, i.e., (L</span><span class="char-style-override-16">1</span><span>, L</span><span class="char-style-override-16">1</span><span>)</span> <span class="char-style-override-11">∉</span> <span>R. R is symmetric as (L</span><span class="char-style-override-16">1</span><span>, L</span><span class="char-style-override-16">2</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R</span></p>

    <p class="Body-Text para-style-override-5"><span class="char-style-override-11">⇒</span> <span>L</span><span class="char-style-override-16">1</span> <span>is perpendicular to L</span><span class="char-style-override-16">2</span></p>

    <p class="Body-Text para-style-override-5"><span class="char-style-override-11">⇒</span> <span>L</span><span class="char-style-override-16">2</span> <span>is perpendicular to L</span><span class="char-style-override-16">1</span></p>

    <p class="Body-Text para-style-override-5"><span class="char-style-override-11">⇒</span> <span>(L</span><span class="char-style-override-16">2</span><span>, L</span><span class="char-style-override-16">1</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R.</span></p>

    <div>
      <div class="Basic-Graphics-Frame frame-2" style="text-align: center;"><img alt="1093.png" class="frame-4" src="../Images/image1.png"/></div>

      <div class="Basic-Graphics-Frame">
        <p class="caption para-style-override-13"><span class="">Fig 1.1</span></p>
      </div>
    </div>

    <p class="Remarks para-style-override-14"><span>R is not transitive</span><span>. Indeed, if L</span><span class="char-style-override-16">1</span> <span>is perpendicular to L</span><span class="char-style-override-16">2</span> <span>and L</span><span class="char-style-override-16">2</span> <span>is perpendicular to L</span><span class="char-style-override-16">3</span><span>, then L</span><span class="char-style-override-16">1</span> <span>can never be perpendicular to L</span><span class="char-style-override-16">3</span><span>. In fact, L</span><span class="char-style-override-16">1</span> <span>is parallel to L</span><span class="char-style-override-16">3</span><span>, i.e., (L</span><span class="char-style-override-16">1</span><span>, L</span><span class="char-style-override-16">2</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R, (L</span><span class="char-style-override-16">2</span><span>, L</span><span class="char-style-override-16">3</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R but (L</span><span class="char-style-override-16">1</span><span>, L</span><span class="char-style-override-16">3</span><span>)</span> <span class="char-style-override-11">∉</span> <span>R.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Example 4</span> <span>Show that the relation R in the set {1, 2, 3} given by R = {(1, 1), (2, 2),<br/>
    (3, 3), (1, 2), (2, 3)} is reflexive but neither symmetric nor transitive.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> <span>R is reflexive, since (1, 1), (2, 2) and (3, 3) lie in R. Also, R is not symmetric, as (1, 2)</span> <span class="char-style-override-11">∈</span> <span>R but (2, 1)</span> <span class="char-style-override-11">∉</span> <span>R. Similarly, R is not transitive, as (1, 2)</span> <span class="char-style-override-11">∈</span> <span>R and (2, 3)</span> <span class="char-style-override-11">∈</span> <span>R but (1, 3)</span> <span class="char-style-override-11">∉</span> <span>R.</span></p>

    <p class="Remarks para-style-override-12"><span class="char-style-override-12">Example 5</span> <span>Show that the relation R in the set</span> <span class="char-style-override-14">Z</span> <span>of integers given by</span></p>

    <p class="eq-1 para-style-override-15"><span>R = {(</span><span class="char-style-override-10">a, b</span><span>) : 2 divides</span> <span class="char-style-override-10">a</span> <span>–</span> <span class="char-style-override-10">b</span><span>}</span></p>

    <p class="Body-Text para-style-override-16">is an equivalence relation.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> <span>R is reflexive, as 2 divides (</span><span class="char-style-override-10">a</span> <span>–</span> <span class="char-style-override-10">a</span><span>) for all</span> <span class="char-style-override-10">a</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">Z</span><span>.</span> <span>Further,</span> <span>if (</span><span class="char-style-override-10">a, b</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R, then 2 divides</span> <span class="char-style-override-10">a</span> <span>–</span> <span class="char-style-override-10">b</span><span>. Therefore, 2 divides</span> <span class="char-style-override-10">b</span> <span>–</span> <span class="char-style-override-10">a</span><span>. Hence, (</span><span class="char-style-override-10">b</span><span>,</span> <span class="char-style-override-10">a</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R, which shows that R is</span> <span>symmetric. Similarly, if (</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R and (</span><span class="char-style-override-10">b</span><span>,</span> <span class="char-style-override-10">c</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R, then</span> <span class="char-style-override-10">a</span> <span>–</span> <span class="char-style-override-10">b</span> <span>and</span> <span class="char-style-override-10">b</span> <span>–</span> <span class="char-style-override-10">c</span> <span>are divisible by 2. Now,</span> <span class="char-style-override-10">a</span> <span>–</span> <span class="char-style-override-10">c</span> <span>= (</span><span class="char-style-override-10">a</span> <span>–</span> <span class="char-style-override-10">b</span><span>) + (</span><span class="char-style-override-10">b</span> <span>–</span> <span class="char-style-override-10">c</span><span>) is even (Why?). So, (</span><span class="char-style-override-10">a</span> <span>–</span> <span class="char-style-override-10">c</span><span>) is divisible by 2. This shows that R is transitive. Thus, R is an equivalence relation in</span> <span class="char-style-override-14">Z</span><span>.</span></p>

    <p class="Body-Text"><span>In E</span><span>xample 5, note that all even integers are related to zero, as (0, ± 2), (0, ± 4) etc., lie in R and no odd integer is related to 0, as (0, ± 1), (0, ± 3) etc., do not lie in R. Similarly, all odd integers are related to one and no even integer is related to one. Therefore, the set E of all even integers and the set O of all odd integers are subsets of</span> <span class="char-style-override-14"><b>Z</b></span> <span>satisfying following conditions:</span></p>

    <p class="option-body-text para-style-override-9">(i) All elements of E are related to each other and all elements of O are related to each other.</p>

    <p class="option-body-text para-style-override-9">(ii) No element of E is related to any element of O and vice-versa.</p>

    <p class="option-body-text para-style-override-9"><span>(iii) E and O are disjoint and</span> <span class="char-style-override-14"><b>Z</b></span> <span>= E</span> <span class="char-style-override-11">∪</span> <span>O.</span></p>

    <p class="Body-Text"><span>The subset E is called the</span> <span class="char-style-override-10">equivalence class containing zero</span> <span>and is denoted by [0]. Similarly, O is the equivalence class containing 1 and is denoted by [1]. Note that [0]</span> <span class="char-style-override-11">≠</span> <span>[1], [0] = [2</span><span class="char-style-override-10">r</span><span>] and [1] = [2</span><span class="char-style-override-10">r</span> <span>+ 1],</span> <span class="char-style-override-10">r</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14"><b>Z</b></span><span>. Infact, what we have seen above is true for an arbitrary equivalence relation R in a set X. Given an arbitrary equivalence relation R in an arbitrary set X, R divides X into mutually disjoint subsets A</span><sub>i</sub> <span>called partitions or subdivisions of X satisfying:</span></p>

    <p class="option-body-text para-style-override-17"><span>(i) all elements of A</span><sub>i</sub> <span>are related to each other, for all</span> <span class="char-style-override-10">i</span><span>.</span></p>

    <p class="option-body-text para-style-override-17"><span>(ii) no element of A</span><sub>i</sub> <span>is related to any element of A</span><sub>j</sub><span>,</span> <span class="char-style-override-10">i</span> <span class="char-style-override-11">≠</span> <span class="char-style-override-10">j</span><span>.</span></p>

    <p class="option-body-text para-style-override-17"><span>(iii)</span> <span class="char-style-override-11">∪</span> <span>A</span><sub>j</sub> <span>= X and A</span><sub>i</sub> <span class="char-style-override-11">∩</span> <span>A</span><sub>j</sub> <span>=</span> <span class="char-style-override-11">φ</span><span>,</span> <span class="char-style-override-10">i</span> <span class="char-style-override-11">≠</span> <span class="char-style-override-10">j</span><span>.</span></p>

    <p class="Body-Text"><span>The subsets A</span><sub>i</sub> <span>are called</span> <span class="char-style-override-10">equivalence classes</span><span>. The interesting part of the situation is that we can go reverse also. For example, consider a subdivision of the set</span> <span class="char-style-override-14">Z</span> <span>given by three mutually disjoint subsets A</span><span class="char-style-override-16">1</span><span>, A</span><span class="char-style-override-16">2</span> <span>and A</span><span class="char-style-override-16">3</span> <span>whose union is</span> <span class="char-style-override-14">Z</span> <span>with</span></p>

    <p class="Body-Text"><span>A</span><span class="char-style-override-16">1</span> <span>= {</span><span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14"><b>Z</b></span> <span>:</span> <span class="char-style-override-10">x</span> <span>is a multiple of 3} = {..., – 6, – 3, 0, 3, 6, ...}</span></p>

    <p class="Body-Text"><span>A</span><span class="char-style-override-16">2</span> <span>= {</span><span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14"><b>Z</b></span> <span>:</span> <span class="char-style-override-10">x</span> <span>– 1 is a multiple of 3} = {..., – 5, – 2, 1, 4, 7, ...}</span></p>

    <p class="Body-Text"><span>A</span><span class="char-style-override-16">3</span> <span>= {</span><span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14"><b>Z</b></span> <span>:</span> <span class="char-style-override-10">x</span> <span>– 2 is a multiple of 3} = {..., – 4, – 1, 2, 5, 8, ...}</span></p>

    <p class="Body-Text para-style-override-11"><span>Define a relation R in</span> <span class="char-style-override-14">Z</span> <span>given by R = {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) : 3 divides</span> <span class="char-style-override-10">a</span> <span>–</span> <span class="char-style-override-10">b</span><span>}. Following the arguments similar to those used in Example 5, we can show that R is an equivalence relation. Also, A</span><span class="char-style-override-16">1</span> <span>coincides with the set of all integers in</span> <span class="char-style-override-14">Z</span> <span>which are related to zero, A</span><span class="char-style-override-16">2</span> <span>coincides with the set of all integers which are related to 1 and A</span><span class="char-style-override-16">3</span> <span>coincides with the set of all integers in</span> <span class="char-style-override-14">Z</span> <span>which are related to 2. Thus, A</span><span class="char-style-override-16">1</span> <span>= [0], A</span><span class="char-style-override-16">2</span> <span>= [1] and A</span><span class="char-style-override-16">3</span> <span>= [2].<br/>
    In fact, A</span><span class="char-style-override-16">1</span> <span>= [3</span><span class="char-style-override-10">r</span><span>], A</span><span class="char-style-override-16">2</span> <span>= [3</span><span class="char-style-override-10">r</span> <span>+ 1] and A</span><span class="char-style-override-16">3</span> <span>= [3</span><span class="char-style-override-10">r</span> <span>+ 2], for all</span> <span class="char-style-override-10">r</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">Z</span><span>.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Example 6</span> <span>Let R be the relation defined in the set A = {1, 2, 3, 4, 5, 6, 7} by<br/>
    R = {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) : both</span> <span class="char-style-override-10">a</span> <span>and</span> <span class="char-style-override-10">b</span> <span>are either odd or even}. Show that R is an equivalence relation. Further, show that all the elements of the subset {1, 3, 5, 7} are related to each other and all the elements of the subset {2, 4, 6} are related to each other, but no element of the subset {1, 3, 5, 7} is related to any element of the subse</span>t {2, 4, 6}.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Given any element <span class="char-style-override-10">a</span> in A, both <span class="char-style-override-10">a</span> and <span class="char-style-override-10">a</span> must be either odd or even, so<br/>
    that <span>(</span><span class="char-style-override-10">a</span>, <span class="char-style-override-10">a</span>) <span class="char-style-override-11">∈</span> R. Further, (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>) <span class="char-style-override-11">∈</span> R <span class="char-style-override-11">⇒</span> both <span class="char-style-override-10">a</span> and <span class="char-style-override-10">b</span> must be either odd or even <span class="char-style-override-11"><br/>
    ⇒</span> (<span class="char-style-override-10">b</span>, <span class="char-style-override-10">a</span>) <span class="char-style-override-11">∈</span> R. Similarly, (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>) <span class="char-style-override-11">∈</span> R and (<span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span>) <span class="char-style-override-11">∈</span> R <span class="char-style-override-11">⇒</span> all elements <span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span>, must be either even or odd simultaneously <span class="char-style-override-11">⇒</span> (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">c</span>) <span class="char-style-override-11">∈</span> R. Hence, R is an equivalence relation. Further, all the elements of {1, 3, 5, 7} are related to each other, as all the elements<br/>
    of this subset are odd. Similarly, all the elements of the subset {2, 4, 6} are related to each other, as all of them are even. Also, no element of the subset {1, 3, 5, 7} can be related to any element of {2, 4, 6}, as elements of {1, 3, 5, 7} are odd, while elements of {2, 4, 6} are even.</p>

    <p class="Exercise"><span class="char-style-override-7"><br/></span></p>

    <p class="Exercise"><span class="char-style-override-7"><br/></span></p>

    <div class="lining_box">
      <p class="Exercise" style="text-align: center;"><span class="char-style-override-7">EXERCISE 1.1</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">1.</span> Determine whether each of the following relations are reflexive, symmetric and transitive:</p>

      <p class="option--i-----ii- para-style-override-8">(i) Relation R in the set A = {1, 2, 3, ..., 13, 14} defined as</p>

      <p class="option--i-----ii- para-style-override-18" style="text-align: left;">R = {(<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>) : 3<span class="char-style-override-10">x</span> – <span class="char-style-override-10">y</span> = 0}</p>

      <p class="option--i-----ii- para-style-override-8">(ii) Relation R in the set <span class="char-style-override-14">N</span> of natural numbers defined as</p>

      <p class="option--i-----ii- para-style-override-18">R = {(<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>) : <span class="char-style-override-10">y</span> = <span class="char-style-override-10">x</span> + 5 and <span class="char-style-override-10">x</span> &lt; 4}</p>

      <p class="option--i-----ii- para-style-override-8">(iii) Relation R in the set A = {1, 2, 3, 4, 5, 6} as</p>

      <p class="option--i-----ii- para-style-override-18">R = {(<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>) : <span class="char-style-override-10">y</span> is divisible by <span class="char-style-override-10">x</span>}</p>

      <p class="option--i-----ii- para-style-override-8">(iv) Relation R in the set <span class="char-style-override-14"><b>Z</b></span> of all integers defined as</p>

      <p class="option--i-----ii- para-style-override-13">R = {(<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>) : <span class="char-style-override-10">x</span> – <span class="char-style-override-10">y</span> is an integer}</p>

      <p class="option--i-----ii- para-style-override-19">(v) <span>Relation R in the set A of human beings in a town at a particular time given by</span></p>

      <p class="option--i-----ii-">(a) R = {(<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>) : <span class="char-style-override-10">x</span> and <span class="char-style-override-10">y</span> work at the same place}</p>

      <p class="option--i-----ii-">(b) R = {(<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>) : <span class="char-style-override-10">x</span> and <span class="char-style-override-10">y</span> live in the same locality}</p>

      <p class="option--i-----ii-">(c) R = {(<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>) : <span class="char-style-override-10">x</span> is exactly 7 cm taller than <span class="char-style-override-10">y</span>}</p>

      <p class="option--i-----ii-">(d) R = {(<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>) : <span class="char-style-override-10">x</span> is wife of <span class="char-style-override-10">y</span>}</p>

      <p class="option--i-----ii-">(e) R = {(<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>) : <span class="char-style-override-10">x</span> is father of <span class="char-style-override-10">y</span>}</p>

      <p class="option-3-aline para-style-override-8"><span class="char-style-override-12">2.</span> Sh<span>ow that the relation R in the set</span> <span class="char-style-override-14"><b>R</b></span> <span>of real numbers, defined as</span></p>

      <p class="option-3-aline para-style-override-8"><span>R = {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) :</span> <span class="char-style-override-10">a</span> <span class="char-style-override-11">≤</span> <span class="char-style-override-10">b</span><span class="char-style-override-19">2</span><span>} is neither reflexive nor symmetric nor transitive.</span></p>

      <p class="option-3-aline para-style-override-8"><span class="char-style-override-12">3.</span> <span>Check whether the relation R defined in the set {1, 2, 3, 4, 5, 6} as</span></p>

      <p class="option-3-aline para-style-override-8"><span>R = {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) :</span> <span class="char-style-override-10">b = a</span> <span>+ 1} is reflexive, symmetric or transitive.</span></p>

      <p class="option-3-aline para-style-override-8"><span class="char-style-override-12">4.</span> <span>Show that the relation R in</span> <span class="char-style-override-14"><b>R</b></span> <span>defined as R = {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) :</span> <span class="char-style-override-10">a</span> <span class="char-style-override-11">≤</span> <span class="char-style-override-10">b</span><span>}, is reflexive and transitive but not symmetric.</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">5.</span> <span>Check whether the relation R in</span> <span class="char-style-override-14"><b>R</b></span> <span>defined by R = {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) :</span> <span class="char-style-override-10">a</span> <span class="char-style-override-11">≤</span> <span class="char-style-override-10">b</span><span class="char-style-override-19">3</span><span>} is reflexive, symmetric or transitiv</span>e.</p>

      <p class="option-3-aline"><span class="char-style-override-12">6.</span> Show that the relation R in the set {1, 2, 3} given by R = {(1, 2), (2, 1)} is symmetric but neither reflexive nor transitive.</p>

      <p class="option-3-aline"><span class="char-style-override-12">7.</span> <span>Show that the relation R in the set A of all the books in a library of a college, given by R = {(</span><span class="char-style-override-10">x</span><span>,</span> <span class="char-style-override-10">y</span><span>) :</span> <span class="char-style-override-10">x</span> <span>and</span> <span class="char-style-override-10">y</span> <span>have same number of pages} is an equivalence relation.</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">8.</span> <span>Show that the relation R in the set A = {1, 2, 3, 4, 5} given by</span></p>

      <p class="option-3-aline"><span>R = {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) : |</span><span class="char-style-override-10">a</span> <span>–</span> <span class="char-style-override-10">b</span><span>| is even}, is an equivalence relation. Show that all the elements of {1, 3, 5} are related to each other and all the elements of {2, 4} are</span> <span>related to each other. But no element of {1, 3, 5} is related to any element of {2, 4}.</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">9.</span> <span>Show that each of the relation R in the set A = {</span><span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14"><b>Z</b></span> <span>: 0</span> <span class="char-style-override-11">≤</span> <span class="char-style-override-10">x</span> <span class="char-style-override-11">≤</span> <span>12}, given by</span></p>

      <p class="option--i-----ii-"><span>(i) R = {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) : |</span><span class="char-style-override-10">a</span> <span>–</span> <span class="char-style-override-10">b</span><span>| is a multiple of 4}</span></p>

      <p class="option--i-----ii-"><span>(ii) R = {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) :</span> <span class="char-style-override-10">a</span> <span>=</span> <span class="char-style-override-10">b</span><span>}</span></p>

      <p class="option-3-aline">is an equivalence relation. Find the set of all elements related to 1 in each case.</p>

      <p class="option-3-aline"><span class="char-style-override-12">10.</span> <span>Give an example of a relation. Which is</span></p>

      <p class="option--i-----ii-">(i) Symmetric but neither reflexive nor transitive.</p>

      <p class="option--i-----ii-">(ii) Transitive but neither reflexive nor symmetric.</p>

      <p class="option--i-----ii-">(iii) Reflexive and symmetric but not transitive.</p>

      <p class="option--i-----ii-">(iv) Reflexive and transitive but not symmetric.</p>

      <p class="option--i-----ii-">(v) Symmetric and transitive but not reflexive.</p>

      <p class="option-3-aline"><span class="char-style-override-12">11.</span> <span>Show that the relation R in the set A of points in a plane given by<br/>
      R = {(P, Q) : distance of the point P from the origin is same as the distance of the point Q from the origin}, is an equivalence relation. Further, show that the set of all points related to a point P</span> <span class="char-style-override-11">≠</span> <span>(0, 0) is the circle passing through P with origin as centre.</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">12.</span> <span>Show that the relation R defined in the set A of all triangles as R = {(T</span><span class="char-style-override-16">1</span><span>, T</span><span class="char-style-override-16">2</span><span>) : T</span><span class="char-style-override-16">1</span> <span>is similar to T</span><span class="char-style-override-16">2</span><span>}, is equivalence relation. Consider three right angle triangles T</span><span class="char-style-override-16">1</span> <span>with sides 3, 4, 5, T</span><span class="char-style-override-16">2</span> <span>with sides 5, 12, 13 and T</span><span class="char-style-override-16">3</span> <span>with sides 6, 8, 10. Which triangles among T</span><span class="char-style-override-16">1</span><span>, T</span><span class="char-style-override-16">2</span> <span>and T</span><span class="char-style-override-16">3</span> <span>are related?</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">13.</span> <span>Show that the relation R defined in the set A of all polygons as R = {(P</span><span class="char-style-override-16">1</span><span>, P</span><span class="char-style-override-16">2</span><span>) :<br/>
      P</span><span class="char-style-override-16">1</span> <span>and P</span><span class="char-style-override-16">2</span> <span>have same number of sides}, is an equivalence relation. What is the set of all elements in A related to the right angle triangle T with sides 3, 4 and 5?</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">14.</span> <span>Let L be the set of all lines in XY plane and R be the relation in L defined as<br/>
      R = {(L</span><span class="char-style-override-16">1</span><span>, L</span><span class="char-style-override-16">2</span><span>) : L</span><span class="char-style-override-16">1</span> <span>is parallel to L</span><span class="char-style-override-16">2</span><span>}. Show that R is an equivalence relation. Find the set of all lines related to the line</span> <span class="char-style-override-10">y</span> <span>= 2</span><span class="char-style-override-10">x</span> <span>+ 4.</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">15.</span> Let <span>R be the relation in the set {1, 2, 3, 4} given by R = {(1, 2), (2, 2), (1, 1), (4,4),<br/>
      (1, 3), (3, 3), (3, 2)}. Choose the correct answer.</span></p>

      <p class="Opt-4-A-D">(A) R is reflexive and symmetric but not transitive.</p>

      <p class="Opt-4-A-D">(B) R is reflexive and transitive but not symmetric.</p>

      <p class="Opt-4-A-D">(C) R is symmetric and transitive but not reflexive.</p>

      <p class="Opt-4-A-D">(D) R is an equivalence relation.</p>

      <p class="option-3-aline"><span class="char-style-override-12">16.</span> <span>Let R be the relation in the set</span> <span class="char-style-override-14"><b>N</b></span> <span>given by R = {(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) :</span> <span class="char-style-override-10">a</span> <span>=</span> <span class="char-style-override-10">b</span> <span>– 2,</span> <span class="char-style-override-10">b</span> <span>&gt; 6}. Choose the correct answer.</span></p>

      <p class="Opt--4-a---d"><span>(A) (2, 4)</span> <span class="char-style-override-11">∈</span> <span>R (B) (3, 8)</span> <span class="char-style-override-11">∈</span> <span>R (C) (6, 8)</span> <span class="char-style-override-11">∈</span> <span>R (D) (8, 7)</span> <span class="char-style-override-11">∈</span> <span>R</span></p>
    </div>

    <p class="heading"><span class="char-style-override-7"><br/></span></p>

    <p class="heading"><span class="char-style-override-7"><br/></span></p>

    <p class="heading"><span class="char-style-override-7">1.3 Types of Functions</span></p>

    <p class="Body-Text para-style-override-5">The notion of a function along with some special functions like identity function, constant function, polynomial function, rational function, modulus function, signum function etc. along with their graphs have been given in Class XI.</p>

    <p class="Body-Text">Addition, subtraction, multiplication and division of two functions have also been studied. As the concept of function is of paramount importance in mathematics and among other disciplines as well, we would like to extend our study about function from where we finished earlier. In this section, we would like to study different types of functions.</p>

    <p class="Body-Text"><span>Consider the functions</span> <span class="char-style-override-10">f</span><span class="char-style-override-16">1</span><span>,</span> <span class="char-style-override-10">f</span><span class="char-style-override-16">2</span><span>,</span> <span class="char-style-override-10">f</span><span class="char-style-override-16">3</span> <span>and</span> <span class="char-style-override-10">f</span><span class="char-style-override-16">4</span> <span>given by the following diagrams.</span></p>

    <p class="Body-Text"><span>In Fig 1.2, we observe that the images of distinct elements of X</span><span class="char-style-override-16">1</span> <span>under the function</span> <span class="char-style-override-10">f</span><span class="char-style-override-16">1</span> <span>are</span> <span>distinct, but the image of two distinct elements 1 and 2 of X</span><span class="char-style-override-16">1</span> <span>under</span> <span class="char-style-override-10">f</span><span class="char-style-override-16">2</span> <span>is same, namely</span> <span class="char-style-override-10">b</span><span>. Further, there are some elements like</span> <span class="char-style-override-10">e</span> <span>and</span> <span class="char-style-override-10">f</span> <span>in X</span><span class="char-style-override-16">2</span> <span>which are not images of any element of X</span><span class="char-style-override-16">1</span> <span>under</span> <span class="char-style-override-10">f</span><span class="char-style-override-16">1</span><span>, while all elements of X</span><span class="char-style-override-16">3</span> <span>are images of some elements of X</span><span class="char-style-override-16">1</span> <span>under</span> <span class="char-style-override-10">f</span><span class="char-style-override-16">3</span><span>. The above observations lead to the following definitions:</span></p>

    <p class="Definition"><span class="char-style-override-12">Definition 5</span> <span>A function</span> <span class="char-style-override-10">f</span> <span>: X</span> <span class="char-style-override-11">→</span> <span>Y is defined to be</span> <span class="char-style-override-10">one-one</span> <span>(or</span> <span class="char-style-override-10">injective</span><span>), if the</span> <span>images of distinct elements of X under</span> <span class="char-style-override-10">f</span> <span>are distinct, i.e., for every</span> <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span><span>,</span> <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span> <span class="char-style-override-11">∈</span> <span>X,</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span><span class="char-style-override-16">1</span><span>) =</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span><span class="char-style-override-16">2</span><span>) implies</span> <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> <span>=</span> <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span><span>. Otherwise,</span> <span class="char-style-override-10">f</span> <span>is called</span> <span class="char-style-override-10">many-one</span><span>.</span></p>

    <p class="Body-Text"><span>The function</span> <span class="char-style-override-10">f</span><span class="char-style-override-16">1</span> <span>and</span> <span class="char-style-override-10">f</span><span class="char-style-override-16">4</span> <span>in Fig 1.2 (i) and (iv) are one-one and the function</span> <span class="char-style-override-10">f</span><span class="char-style-override-16">2</span> <span>and</span> <span class="char-style-override-10">f</span><span class="char-style-override-16">3</span> <span>in Fig 1.2 (ii) and (iii) are many-one.</span></p>

    <p class="Definition"><span class="char-style-override-12">Definition 6</span> <span>A function</span> <span class="char-style-override-10">f</span> <span>: X</span> <span class="char-style-override-11">→</span> <span>Y is said to be</span> <span class="char-style-override-10">onto</span> <span>(or</span> <span class="char-style-override-10">surjective</span><span>), if every element of Y is the image of some element of X under</span> <span class="char-style-override-10">f</span><span>, i.e., for every</span> <span class="char-style-override-10">y</span> <span class="char-style-override-11">∈</span> <span>Y, there exists an element</span> <span class="char-style-override-10">x</span> <span>in X such that</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span><span>) =</span> <span class="char-style-override-10">y</span><span>.</span></p>

    <p class="Body-Text"><span>The function</span> <span class="char-style-override-10">f</span><span class="char-style-override-16">3</span> <span>and</span> <span class="char-style-override-10">f</span><span class="char-style-override-16">4</span> <span>in Fig 1.2 (iii), (iv) are onto and the function</span> <span class="char-style-override-10">f</span><span class="char-style-override-16">1</span> <span>in Fig 1.2 (i) is not onto as elements</span> <span class="char-style-override-10">e</span><span>,</span> <span class="char-style-override-10">f</span> <span>in X</span><span class="char-style-override-16">2</span> <span>are not the image of any element in X</span><span class="char-style-override-16">1</span> <span>u</span>nder <span class="char-style-override-10">f</span><span class="char-style-override-16">1</span>.</p>

    <p class="Remarks"><span class="char-style-override-15">Remark</span> <span class="char-style-override-10">f</span> : X <span class="char-style-override-11">→</span> <span>Y is onto if and only if Range of</span> <span class="char-style-override-10">f</span> <span>= Y.</span></p>

    <div>
      <div class="Basic-Graphics-Frame frame-2"><img alt="1220.png" class="frame-5" src="../Images/image5.png" style="width:100%"/></div>

      <div class="Basic-Graphics-Frame frame-2"><img alt="1211.png" class="frame-6" src="../Images/image4.png" style="width:100%"/></div>

      <div class="Basic-Graphics-Frame">
        <p class="caption para-style-override-15"><span class="">Fig 1.2 (i) to (iv)</span></p>
      </div>
    </div>

    <p class="Remarks"/>

    <p class="Definition"><span class="char-style-override-12"><br/></span></p>

    <p class="Definition"><span class="char-style-override-12">Definition 7</span> <span>A function</span> <span class="char-style-override-10">f</span> <span>: X</span> <span class="char-style-override-11">→</span> <span>Y is said to be</span> <span class="char-style-override-10">one-one</span> <span>and</span> <span class="char-style-override-10">onto</span> <span>(or</span> <span class="char-style-override-10">bijective</span><span>), if</span> <span class="char-style-override-10">f</span> <span>is both one-one and onto.</span></p>

    <p class="Body-Text para-style-override-11"><span>The function</span> <span class="char-style-override-10">f</span><span class="char-style-override-16">4</span> <span>in Fig 1.2 (iv) is one-one and onto.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Example 7</span> <span>Let A be the set of all 50 students of Class X in a school. Let</span> <span class="char-style-override-10">f</span> <span>: A</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">N</span> <span>be function defined by</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span><span>) = roll number of the student</span> <span class="char-style-override-10">x</span><span>. Show that</span> <span class="char-style-override-10">f</span> <span>is one-one<br/>
    but not onto.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> <span>No two different students of the class can have same roll number. Therefore,</span> <span class="char-style-override-10">f</span> <span>must be one-one. We can assume without any loss of generality that roll numbers of students are from 1 to 50. This implies that 51 in</span> <span class="char-style-override-14"><b>N</b></span> <span>is not roll number of any student of the class, so that 51 can not be image of any element of X under</span> <span class="char-style-override-10">f</span><span>. Hence</span><span>,</span> <span class="char-style-override-10">f</span> <span>is not onto.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Example 8</span> <span>Show that the function</span> <span class="char-style-override-10">f</span> <span>:</span> <span class="char-style-override-14"><b>N</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>N</b></span><span>, given by</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span><span>) = 2</span><span class="char-style-override-10">x</span><span>, is one-one but not onto.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> <span>The function</span> <span class="char-style-override-10">f</span> <span>is one-one, for</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span><span class="char-style-override-16">1</span><span>) =</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span><span class="char-style-override-16">2</span><span>)</span> <span class="char-style-override-11">⇒</span> <span>2</span><span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> <span>= 2</span><span class="char-style-override-10">x</span><span class="char-style-override-16">2</span> <span class="char-style-override-11">⇒</span> <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> <span>=</span> <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span><span>. Further,</span> <span class="char-style-override-10">f</span> <span>is not onto, as for 1</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">N</span><span>, there does</span> <span>not exist any</span> <span class="char-style-override-10">x</span> <span>in</span> <span class="char-style-override-14">N</span> <span>such that</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span><span>) = 2</span><span class="char-style-override-10">x</span> <span>= 1.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Example 9</span> <span>Prove that the function</span> <span class="char-style-override-10">f</span> <span>:</span> <span class="char-style-override-14"><b>R</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span><span>, given by</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span><span>) = 2</span><span class="char-style-override-10">x</span><span>, is one-one and onto.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> <span class="char-style-override-10">f</span> is one-one, as <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span><span class="char-style-override-16">1</span>) = <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span><span class="char-style-override-16">2</span>) <span class="char-style-override-11">⇒</span> 2<span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> = 2<span class="char-style-override-10">x</span><span class="char-style-override-16">2</span> <span class="char-style-override-11">⇒</span> <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> = <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span>. Also, given any real number <span class="char-style-override-10">y</span> in <span>R</span>, there exists <span class="Basic-Graphics-Frame"><img alt="1612.png" class="frame-3" src="../Images/image10.png"/></span> in R such that <span class="char-style-override-10">f</span>(<span class="char-style-override-10"><span class="Basic-Graphics-Frame"><img alt="1617.png" class="frame-3" src="../Images/image11.png"/></span></span>) = 2 . (<img alt="1612.png" class="frame-3" src="../Images/image10.png"/>) = <span class="char-style-override-10">y</span>. Hence, <span class="char-style-override-10">f</span> is onto.</p>

    <div class="Basic-Graphics-Frame frame-2" style="text-align: center;"><img alt="1200.png" class="frame-8" src="../Images/image3.png"/></div>

    <p class="Remarks"/>

    <p class="caption para-style-override-13"><span class="">Fig 1.3</span></p>

    <p class="Remarks"><span class="char-style-override-12">Example 1</span><span class="char-style-override-12">0</span> Show that the function <span class="char-style-override-10">f</span> : <b><span class="char-style-override-14">N</span></b> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>N</b></span>, <span>given by</span> <span class="char-style-override-10">f</span><span>(1) =</span> <span class="char-style-override-10">f</span><span>(2) = 1 and</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span> – 1, for every <span class="char-style-override-10">x</span> &gt; 2, is onto but not one-one.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> <span class="char-style-override-10">f</span> is not one-one, as <span class="char-style-override-10">f</span>(1) = <span class="char-style-override-10">f</span>(2) = 1. But <span class="char-style-override-10">f</span> is onto, as given any <span class="char-style-override-10">y</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">N</span>, <span class="char-style-override-10">y</span> <span class="char-style-override-11">≠</span> 1, we can choose <span class="char-style-override-10">x</span> as <span class="char-style-override-10">y</span> + 1 such that <span class="char-style-override-10">f</span>(<span class="char-style-override-10">y</span> + 1) = <span class="char-style-override-10">y</span> + 1 – 1 = <span class="char-style-override-10">y</span>. Also for 1 <span class="char-style-override-11">∈</span> <span class="char-style-override-14">N</span>, we<br/>
    have <span class="char-style-override-10">f</span>(1) = 1.</p>

    <div>
      <div class="Basic-Graphics-Frame frame-2" style="text-align: center;"><img alt="1190.png" class="frame-9" src="../Images/image2.png"/></div>

      <div class="Basic-Graphics-Frame">
        <p class="caption para-style-override-13"><span class="">Fig 1.4</span></p>
      </div>
    </div>

    <p class="Remarks"/>

    <p class="Remarks"><span class="char-style-override-12">Example 11</span> Show that the function <span class="char-style-override-10">f</span> : <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span>, defined as <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span><span class="char-style-override-19">2</span>, is neither one-one nor onto.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Since <span class="char-style-override-10">f</span>(– 1) = 1 = <span class="char-style-override-10">f</span>(1), <span class="char-style-override-10">f</span> is not one-one. Also, the element – 2 in the co-domain <span class="char-style-override-14">R</span> is not image of any element <span class="char-style-override-10">x</span> in the domain <span class="char-style-override-14">R</span> (Why?). Therefore <span class="char-style-override-10">f</span> is not onto.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 12</span> Show that <span class="char-style-override-10">f</span> : <span class="char-style-override-14">N</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">N</span>, given by</p>

    <p class="No-Paragraph-Style para-style-override-6"><span class="char-style-override-20"><span class="Basic-Graphics-Frame"><img alt="1622.png" class="frame-7" src="../Images/image12.png"/></span></span></p>

    <p class="Remark-tab">is both one-one and onto.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Suppose <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span><span class="char-style-override-16">1</span>) = <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span><span class="char-style-override-16">2</span>). Note that if <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> is odd and <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span> is even, then we will have <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> + 1 = <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span> – 1, i.e., <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span> – <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> = 2 which is impossible. Similarly, the possibility of <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> being even and <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span> being odd can also be ruled out, using the similar argument. Therefore,<br/>
    both <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> and <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span> must be either odd or even. Suppose both <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> and <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span> are odd. Then <span class="char-style-override-10"><br/>
    f</span>(<span class="char-style-override-10">x</span><span class="char-style-override-16">1</span>) = <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span><span class="char-style-override-16">2</span>) <span class="char-style-override-11">⇒</span> <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> + 1 = <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span> + 1 <span class="char-style-override-11">⇒</span> <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> = <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span>. Similarly, if both <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> and <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span> are even, then also <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span><span class="char-style-override-16">1</span>) = <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span><span class="char-style-override-16">2</span>) <span class="char-style-override-11">⇒</span> <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> – 1 = <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span> – 1 <span class="char-style-override-11">⇒</span> <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> = <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span>. Thus, <span class="char-style-override-10">f</span> is one-one. Also, any odd number<br/>
    2<span class="char-style-override-10">r</span> + 1 in the co-domain <span class="char-style-override-14">N</span> is the image of 2<span class="char-style-override-10">r</span> <span class="char-style-override-10">+</span> 2 in the domain <span class="char-style-override-14">N</span> and any even number 2<span class="char-style-override-10">r</span> in the co-domain <span class="char-style-override-14">N</span> is the image of 2<span class="char-style-override-10">r</span> – 1 in the domain <span class="char-style-override-14">N</span>. Thus, <span class="char-style-override-10">f</span> is onto.</p>

    <p class="Remark-tab"><span class="char-style-override-12">Example 13</span> Show that an onto function <span class="char-style-override-10">f</span> : {1, 2, 3} <span class="char-style-override-11">→</span> {1, 2, 3} is always one-one.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Suppose <span class="char-style-override-10">f</span> is not one-one. Then there exists two elements, say 1 and 2 in the domain whose image in the co-domain is same. Also, the image of 3 under <span class="char-style-override-10">f</span> can be only one element. Therefore, the range set can have at the most two elements of the <span>co-domain {1, 2, 3}, showing that</span> <span class="char-style-override-10">f</span> is not onto, a contradiction. Hence<span>,</span> <span class="char-style-override-10">f</span> must be one-one.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 14</span> Show that a one-one function <span class="char-style-override-10">f</span> : {1, 2, 3} <span class="char-style-override-11">→</span> {1, 2, 3} must be onto.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Since <span class="char-style-override-10">f</span> is one-one, three elements of {1, 2, 3} must be taken to 3 different elements of the co-domain {1, 2, 3} under <span class="char-style-override-10">f</span>. Hence, <span class="char-style-override-10">f</span> has to be onto.</p>

    <p class="Remarks"><span class="char-style-override-15">Remark</span> The results mentioned in Examples 13 and 14 are also true for an arbitrary finite set X, i.e., a one-one function <span class="char-style-override-10">f</span> : X <span class="char-style-override-11">→</span> X is necessarily onto and an onto map <span class="char-style-override-10"><br/>
    f</span> : X <span class="char-style-override-11">→</span> X is necessarily one-one, for every finite set X. In contrast to this, Examples 8 and 10 show that for an infinite set, this may not be true. In fact, this is a characteristic difference between a finite and an infinite set.</p>

    <p class="Exercise para-style-override-11"><span class="char-style-override-7"><br/></span></p>

    <p class="Exercise para-style-override-11"><span class="char-style-override-7"><br/></span></p>

    <div class="lining_box">
      <p class="Exercise para-style-override-11" style="text-align: center;"><span class="char-style-override-7">EXERCISE 1.2</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">1.</span> Show that the function <span class="char-style-override-10">f</span> : <b><span class="char-style-override-14">R</span><span class="char-style-override-21">∗</span></b> <span class="char-style-override-11">→</span> <b><span class="char-style-override-14">R</span><span class="char-style-override-21">∗</span></b> defined by <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) =&nbsp;<img alt="1627.png" class="frame-10" src="../Images/image13.png"/>&nbsp;is one-one and onto, where <b><span class="char-style-override-14">R</span><span class="char-style-override-21">∗</span></b> is the set of all non-zero real numbers. Is the result true, if the domain <b><span class="char-style-override-14">R</span><span class="char-style-override-21">∗</span></b> is replaced by <span class="char-style-override-14"><b>N</b></span> with co-domain being same as <b><span class="char-style-override-14">R</span><span class="char-style-override-21">∗</span></b>?</p>

      <p class="option-3-aline"><span class="char-style-override-12">2.</span> Check the injectivity and surjectivity of the following functions:</p>

      <p class="option--i-----ii- para-style-override-8">(i) <span class="char-style-override-10">f</span> : <b><span class="char-style-override-14">N</span></b> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>N</b></span> given by <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span><span class="char-style-override-19">2</span></p>

      <p class="option--i-----ii- para-style-override-8">(ii) <span class="char-style-override-10">f</span> : <span class="char-style-override-14"><b>Z</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>Z</b></span> given by <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span><span class="char-style-override-19">2</span></p>

      <p class="option--i-----ii- para-style-override-8">(iii) <span class="char-style-override-10">f</span> : <span class="char-style-override-14"><b>R</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span> given by <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span><span class="char-style-override-19">2</span></p>

      <p class="option--i-----ii- para-style-override-8">(iv) <span class="char-style-override-10">f</span> : <span class="char-style-override-14"><b>N</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>N</b></span> given by <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span><span class="char-style-override-19">3</span></p>

      <p class="option--i-----ii- para-style-override-8">(v) <span class="char-style-override-10">f</span> : <span class="char-style-override-14"><b>Z</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>Z</b></span> given by <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span><span class="char-style-override-19">3</span></p>

      <p class="option--i-----ii- para-style-override-17"><span class="char-style-override-12">3.</span> <span>Prove that the Greatest Integer Function</span> <span class="char-style-override-10">f</span> : <span class="char-style-override-14"><b>R</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span>, given by <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span>) = [<span class="char-style-override-10">x</span>], is neither one-one nor onto, where [<span class="char-style-override-10">x</span>] denotes the greatest integer less than or equal to <span class="char-style-override-10">x</span>.</p>

      <p class="option-3-aline"><span class="char-style-override-12">4.</span> Show t<span>hat the Modulus Function</span> <span class="char-style-override-10">f</span> <span>:</span> <span class="char-style-override-14"><b>R</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span><span>, given by</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span><span>) = |</span><span class="char-style-override-10">x</span><span>|, is neither one-one nor onto, where |</span> <span class="char-style-override-10">x</span> <span>| is</span> <span class="char-style-override-10">x</span><span>, if</span> <span class="char-style-override-10">x</span> <span>is positive or 0 and |</span><span class="char-style-override-10">x</span><span>| is –</span> <span class="char-style-override-10">x</span><span>, if</span> <span class="char-style-override-10">x</span> <span>is negative.</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">5.</span> <span>Show that the</span> Signum Function <span class="char-style-override-10">f</span> : <b><span class="char-style-override-14">R</span></b> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span>, given by</p>

      <p class="option-3-aline para-style-override-13"><span class="Basic-Graphics-Frame"><img alt="1632.png" class="frame-11" src="../Images/image14.png"/></span></p>

      <p class="option-3">is neith<span>er one-one nor onto.</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">6.</span> <span>L</span><span>et A = {1, 2, 3}, B = {4, 5, 6, 7} and let</span> <span class="char-style-override-10">f</span> <span>= {(1, 4), (2, 5), (3, 6)} be a function from A to B. Show that</span> <span class="char-style-override-10">f</span> <span>is one-one.</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">7.</span> <span>In each of the following cases, state whether the function is one-one, onto or bijective. Justify your answer.</span></p>

      <p class="option--i-----ii-"><span>(i)</span> <span class="char-style-override-10">f</span> <span>:</span> <span class="char-style-override-14"><b>R</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span> <span>defined by</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span><span>) = 3 – 4</span><span class="char-style-override-10">x</span></p>

      <p class="option--i-----ii-"><span>(ii)</span> <span class="char-style-override-10">f</span> <span>:</span> <span class="char-style-override-14"><b>R</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span> <span>defined by</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span><span>) = 1 +</span> <span class="char-style-override-10">x</span><span class="char-style-override-19">2</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">8.</span> <span>Let A and B be sets. Show that</span> <span class="char-style-override-10">f</span> <span>: A × B</span> <span class="char-style-override-11">→</span> <span>B × A such that</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) = (</span><span class="char-style-override-10">b</span><span>,</span> <span class="char-style-override-10">a</span><span>) is bijective fu</span>nction.</p>

      <p class="option-3-aline"><span class="char-style-override-12">9.</span> Let <span class="char-style-override-10">f</span> : <span class="char-style-override-14"><b>N</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>N</b></span> be defined by <span class="char-style-override-10">f</span>(<span class="char-style-override-10">n</span>) = <img alt="1637" src="../Images/image15.png"/> for all <span class="char-style-override-10">n</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">N</span>.</p>

      <p class="option-3-aline"><span>State whether the function</span> <span class="char-style-override-10">f</span> <span>is bijective. Justify your answer.</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">10.</span> <span>Let A</span> = <span class="char-style-override-14"><b>R</b></span> – {3} and B = <span class="char-style-override-14">R</span> – {1}. Consider the function <span class="char-style-override-10">f</span> : A <span class="char-style-override-11">→</span> B defined by <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <img alt="1642.png" class="frame-13" src="../Images/image16.png"/>. Is <span class="char-style-override-10">f</span> one-one and onto? Justify your answer.</p>

      <p class="option-3-aline"><span class="char-style-override-12">11.</span> Let <span class="char-style-override-10">f</span> <span>:</span> <span class="char-style-override-14"><b>R</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span> <span>be defined as</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span><span>) =</span> <span class="char-style-override-10">x</span><span class="char-style-override-19">4</span><span>. Choose the correct answer.</span></p>

      <p class="Opt-4-A-D para-style-override-20"><span>(A)</span> <span class="char-style-override-10">f</span> <span>is one-one onto (B)</span> <span class="char-style-override-10">f</span> <span>is many-one onto</span></p>

      <p class="Opt-4-A-D para-style-override-20"><span>(C)</span> <span class="char-style-override-10">f</span> <span>is one-one but not onto (D)</span> <span class="char-style-override-10">f</span> <span>is neither one-one nor onto.</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">12.</span> <span>Let</span> <span class="char-style-override-10">f</span> <span>:</span> <span class="char-style-override-14"><b>R</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span> <span>be defined as</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span><span>) = 3</span><span class="char-style-override-10">x</span><span>. Choose the correct answer.</span></p>

      <p class="Opt-4-A-D"><span>(A)</span> <span class="char-style-override-10">f</span> <span>is one-one onto (B)</span> <span class="char-style-override-10">f</span> <span>is many-one onto</span></p>

      <p class="Opt-4-A-D"><span>(C)</span> <span class="char-style-override-10">f</span> is one-one but not onto (D) <span class="char-style-override-10">f</span> is neither one-one nor onto.</p>
    </div>

    <p class="heading"><span class="char-style-override-7"><br/></span></p>

    <p class="heading"><span class="char-style-override-7"><br/></span></p>

    <p class="heading"><span class="char-style-override-7">1.4 Composition of Functions and Invertible Function</span></p>

    <p class="Body-Text para-style-override-5">In this s<span>ection, we will study composition of functions and the inverse of a bijective function. Consider the set A of all students, who appeared in Class X of</span> <span>a</span> <span>Board Examination in 2006. Each student appearing in the Board Examination is assigned a roll number by the Board which is written by the students in the answer script at the time of examination. In order to have confidentiality, the Board arranges to deface the roll numbers of students in the answer scripts and assigns a fake code number to each roll number. Let B</span> <span class="char-style-override-11">⊂</span> <span class="char-style-override-14">N</span> <span>be the set of all roll numbers and C</span> <span class="char-style-override-11">⊂</span> <span class="char-style-override-14">N</span> <span>be the set of all code numbers. This gives rise to two functions</span> <span class="char-style-override-10">f</span> <span>: A</span> <span class="char-style-override-11">→</span> <span>B and</span> <span class="char-style-override-10">g</span> <span>: B</span> <span class="char-style-override-11">→</span> <span>C given by</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">a</span><span>) = the roll number assigned to the student</span> <span class="char-style-override-10">a</span> <span>and</span> <span class="char-style-override-10">g</span><span>(</span><span class="char-style-override-10">b</span><span>) = the code number assigned to the roll number</span> <span class="char-style-override-10">b</span><span>. In this process each student is assigned a roll number through the function</span> <span class="char-style-override-10">f</span> <span>and each roll number is assigned a code number through the function</span> <span class="char-style-override-10">g</span><span>. Thus, by the combination of these two functions, each student is eventually attached a code number.</span></p>

    <p class="Body-Text">This leads to the following definition:</p>

    <p class="Definition"><span class="char-style-override-12">Definition 8</span> <span>Let</span> <span class="char-style-override-10">f</span> <span>: A</span> <span class="char-style-override-11">→</span> <span>B and</span> <span class="char-style-override-10">g</span> <span>: B</span> <span class="char-style-override-11">→</span> <span>C be two functions. Then the composition of</span> <span class="char-style-override-10">f</span> <span>and</span> <span class="char-style-override-10">g</span><span>, denoted by</span> <span class="char-style-override-10">go</span><span class="char-style-override-10">f</span>, is defined as the function <span class="char-style-override-10">gof</span> : A <span class="char-style-override-11">→</span> C given by</p>

    <p class="eq-1"><span class="char-style-override-10">go</span><span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">g</span>(<span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>)),<img alt="Screenshot from 2015-06-22 12:08:05" src="../Images/image152.png"/>&nbsp; &nbsp;<span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> A.</p>

    <p class="Body-Text"/>

    <p class="Body-Text"/>

    <p class="Body-Text"/>

    <p class="Body-Text para-style-override-15"><br/></p>

    <div class="Basic-Graphics-Frame frame-2" style="text-align: center;"><img alt="1294.png" class="frame-16" src="../Images/image6.png" style="width:100%"/></div>

    <div class="Basic-Graphics-Frame frame-2" style="text-align: center;">
      <span class="caption">Fig 1.5</span><br/>
    </div>

    <p class="Remarks"><span class="char-style-override-12">Example 15</span> Let <span class="char-style-override-10">f</span> : {2, 3, 4, 5} <span class="char-style-override-11">→</span> {3, 4, 5, 9} and <span class="char-style-override-10">g</span> : {3, 4, 5, 9} <span class="char-style-override-11">→</span> {7, 11, 15} be functions defined as <span class="char-style-override-10">f</span>(2) = 3, <span class="char-style-override-10">f</span>(3) = 4, <span class="char-style-override-10">f</span>(4) = <span class="char-style-override-10">f</span>(5) = 5 and <span class="char-style-override-10">g</span>(3) = <span class="char-style-override-10">g</span>(4) = 7 and <span class="char-style-override-10">g</span>(5) = <span class="char-style-override-10">g</span>(9) = 11. Find <span class="char-style-override-10">gof</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> We have <span class="char-style-override-10">gof</span>(2) = <span class="char-style-override-10">g</span>(<span class="char-style-override-10">f</span>(2)) = <span class="char-style-override-10">g</span>(3) = 7, <span class="char-style-override-10">gof</span>(3) = <span class="char-style-override-10">g</span>(<span class="char-style-override-10">f</span>(3)) = <span class="char-style-override-10">g</span>(4) = 7, <span class="char-style-override-10">gof</span>(4) = <span class="char-style-override-10">g</span>(<span class="char-style-override-10">f</span>(4)) = <span class="char-style-override-10">g</span>(5) = 11 and <span class="char-style-override-10">gof</span>(5) = <span class="char-style-override-10">g</span>(5) = 11.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 1</span><span class="char-style-override-12">6</span> Find <span class="char-style-override-10">gof</span> and <span class="char-style-override-10">fog</span>, if <span class="char-style-override-10">f</span> : <span class="char-style-override-14"><b>R</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span> and <span class="char-style-override-10">g</span> : <span class="char-style-override-14"><b>R</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span> are given by <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span>) = cos <span class="char-style-override-10">x</span> and <span class="char-style-override-10">g</span><span>(</span><span class="char-style-override-10">x</span>) = 3<span class="char-style-override-10">x</span><span class="char-style-override-19">2</span>. Show that <span class="char-style-override-10">go</span><span class="char-style-override-10">f</span> <span class="char-style-override-11">≠</span> <span class="char-style-override-10">fog</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> We have <span class="char-style-override-10">go</span><span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">g</span>(<span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>)) = <span class="char-style-override-10">g</span>(cos <span class="char-style-override-10">x</span>) = 3 (cos <span class="char-style-override-10">x</span>)<span class="char-style-override-19">2</span> = 3 cos<span class="char-style-override-19">2</span> <span class="char-style-override-10">x</span>. Similarly, <span class="char-style-override-10">fo</span><span class="char-style-override-10">g</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">f</span>(<span class="char-style-override-10">g</span>(<span class="char-style-override-10">x</span>)) = <span class="char-style-override-10">f</span>(3<span class="char-style-override-10">x</span><span class="char-style-override-19">2</span>) = cos (3<span class="char-style-override-10">x</span><span class="char-style-override-19">2</span>). Note that 3cos<span class="char-style-override-19">2</span> <span class="char-style-override-10">x</span> <span class="char-style-override-11">≠</span> cos 3<span class="char-style-override-10">x</span><span class="char-style-override-19">2</span>, for <span class="char-style-override-10">x</span> = 0. Hence, <span class="char-style-override-10">gof</span> <span class="char-style-override-11">≠</span> <span class="char-style-override-10">fog</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 17</span> Show that if&nbsp;<img alt="1652.png" class="frame-15" src="../Images/image18.png"/>&nbsp;is defined by<img alt="1657.png" class="frame-17" src="../Images/image19.png"/>&nbsp;and<img alt="1662.png" class="frame-18" src="../Images/image20.png"/>&nbsp; &nbsp;is defined by<img alt="1667.png" class="frame-17" src="../Images/image21.png"/>&nbsp; , then <span class="char-style-override-10">fog</span> = I<span class="char-style-override-16">A</span> and <span class="char-style-override-10">gof</span> = I<span class="char-style-override-16">B</span>, where, A = <span class="char-style-override-14">R</span> –<img alt="1672.png" class="frame-19" src="../Images/image22.png"/>&nbsp; , B = <span class="char-style-override-14">R</span> –<img alt="1677.png" class="frame-20" src="../Images/image23.png"/>; I<span class="char-style-override-16">A</span>(<span class="char-style-override-10">x</span>) =<span class="char-style-override-10">x</span>,<span class="Basic-Graphics-Frame"><img alt="1687.png" class="frame-14" src="../Images/image25.png"/></span><span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> A, I<span class="char-style-override-16">B</span> (<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span>,&nbsp;<img alt="1687.png" class="frame-14" src="../Images/image25.png"/><span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> B are called identity functions on sets A and B, respectively.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> We have</p>

    <p class="Remarks"><img alt="1692.png" class="frame-21" src="../Images/image26.png"/><br/></p>

    <p class="Remarks para-style-override-13"><span class="Basic-Graphics-Frame">=&nbsp;<img alt="1697.png" class="frame-22" src="../Images/image27.png"/></span>&nbsp;&nbsp;</p>

    <p class="Body-Text para-style-override-5">Similarly,&nbsp;<img alt="1702.png" class="frame-21" src="../Images/image28.png"/><span class="Basic-Graphics-Frame"><img alt="1708.png" class="frame-22" src="../Images/image29.png"/></span>&nbsp;&nbsp;</p>

    <p class="Body-Text">Thus, <span class="char-style-override-10">go</span><span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span>, <span class="Basic-Graphics-Frame"><img alt="1718.png" class="frame-14" src="../Images/image31.png"/></span><span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> B and <span class="char-style-override-10">fog</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span>, <span class="Basic-Graphics-Frame"><img alt="1723.png" class="frame-14" src="../Images/image32.png"/></span><span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> A, which implies that <span class="char-style-override-10">gof</span> = I<span class="char-style-override-16">B</span> and <span class="char-style-override-10">fog</span> = I<span class="char-style-override-16">A</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 18</span> Show that if <span class="char-style-override-10">f</span> : A <span class="char-style-override-11">→</span> B and <span class="char-style-override-10">g</span> : B <span class="char-style-override-11">→</span> C are one-one, then <span class="char-style-override-10">gof</span> : A <span class="char-style-override-11">→</span> C is also one-one.</p>

    <p class="Remarks para-style-override-12"><span class="char-style-override-12">Solution</span> Suppose <span class="char-style-override-10">go</span><span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span><span class="char-style-override-16">1</span>) = <span class="char-style-override-10">gof</span>(<span class="char-style-override-10">x</span><span class="char-style-override-16">2</span>)</p>

    <p class="eq-1 para-style-override-5"><span class="char-style-override-11">⇒</span> <span class="char-style-override-10">g</span>(<span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span><span class="char-style-override-16">1</span>)) = <span class="char-style-override-10">g</span>(<span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span><span class="char-style-override-16">2</span>))</p>

    <p class="eq-1 para-style-override-5"><span class="char-style-override-11">⇒</span> <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span><span class="char-style-override-16">1</span>) = <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span><span class="char-style-override-16">2</span>), as <span class="char-style-override-10">g</span> is one-one</p>

    <p class="eq-1 para-style-override-5"><span class="char-style-override-11">⇒</span> <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> = <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span>, as <span class="char-style-override-10">f</span> is one-one</p>

    <p class="eq-1 para-style-override-5">Hence, <span class="char-style-override-10">gof</span> is one-one.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 19</span> Show that if <span class="char-style-override-10">f</span> : A <span class="char-style-override-11">→</span> B and <span class="char-style-override-10">g</span> : B <span class="char-style-override-11">→</span> C are onto, then <span class="char-style-override-10">gof</span> : A <span class="char-style-override-11">→</span> C is also onto.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Given an arbitrary element <span class="char-style-override-10">z</span> <span class="char-style-override-11">∈</span> C, there exists a pre-image <span class="char-style-override-10">y</span> of <span class="char-style-override-10">z</span> under <span class="char-style-override-10">g</span> such that <span class="char-style-override-10">g</span>(<span class="char-style-override-10">y</span>) = <span class="char-style-override-10">z</span>, since <span class="char-style-override-10">g</span> is onto. Further, for <span class="char-style-override-10">y</span> <span class="char-style-override-11">∈</span> B, there exists an element <span class="char-style-override-10">x</span> in A with <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">y</span>, since <span class="char-style-override-10">f</span> is onto. Therefore, <span class="char-style-override-10">gof</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">g</span>(<span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>)) = <span class="char-style-override-10">g</span>(<span class="char-style-override-10">y</span>) = <span class="char-style-override-10">z</span>, showing that <span class="char-style-override-10">gof</span> is onto.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 20</span> Consider functions <span class="char-style-override-10">f</span> and <span class="char-style-override-10">g</span> such that composite <span class="char-style-override-10">gof</span> is defined and is one-one. Are <span class="char-style-override-10">f</span> and <span class="char-style-override-10">g</span> both necessarily one-one.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Consider <span class="char-style-override-10">f</span> : {1, 2, 3, 4} <span class="char-style-override-11">→</span> {1, 2, 3, 4, 5, 6} defined as <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span>, <span class="char-style-override-11"><span class="Basic-Graphics-Frame"><img alt="1728.png" class="frame-14" src="../Images/image33.png"/></span></span><span class="char-style-override-10">x</span> and <span class="char-style-override-10"><br/></span><span class="char-style-override-10">g</span> : {1, 2, 3, 4, 5, 6} <span class="char-style-override-11">→</span> {1, 2, 3, 4, 5, 6} as <span class="char-style-override-10">g</span><span>(</span><span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span>, for <span class="char-style-override-10">x</span> <span>= 1, 2, 3, 4 and</span> <span class="char-style-override-10">g</span><span>(5) =</span> <span class="char-style-override-10">g</span><span>(6) = 5.</span> <span>Then</span>, <span class="char-style-override-10">go</span><span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span><img alt="1713.png" class="frame-14" src="../Images/image30.png"/><span class="char-style-override-10">x</span>, which shows that <span class="char-style-override-10">gof</span> is one-one. But <span class="char-style-override-10">g</span> is clearly not one-one.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 21</span> Are <span class="char-style-override-10">f</span> and <span class="char-style-override-10">g</span> both necessarily onto, if <span class="char-style-override-10">gof</span> is onto?</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Consider <span class="char-style-override-10">f</span> : {1, 2, 3, 4} <span class="char-style-override-11">→</span> {1, 2, 3, 4} and <span class="char-style-override-10">g</span> : {1, 2, 3, 4} <span class="char-style-override-11">→</span> {1, 2, 3} defined as <span class="char-style-override-10">f</span>(1) = 1, <span class="char-style-override-10">f</span>(2) = 2, <span class="char-style-override-10">f</span>(3) = <span class="char-style-override-10">f</span>(4) = 3, <span class="char-style-override-10">g</span>(1) = 1, <span class="char-style-override-10">g</span>(2) = 2 and <span class="char-style-override-10">g</span>(3) = <span class="char-style-override-10">g</span>(4) = 3. It can be seen that <span class="char-style-override-10">gof</span> is onto but <span class="char-style-override-10">f</span> is not onto.</p>

    <p class="Remarks"><span style="color: rgb(0, 174, 239); font-weight: bold;">Remark</span> It can be verified in general that <span class="char-style-override-10">gof</span> is one-one implies that <span class="char-style-override-10">f</span> is one-one. Similarly, <span class="char-style-override-10">gof</span> is onto implies that <span class="char-style-override-10">g</span> is onto.</p>

    <p class="Body-Text">Now, we would like to have close look at the functions <span class="char-style-override-10">f</span> and <span class="char-style-override-10">g</span> described in the beginning of this section in reference to a Board <span class="char-style-override-23">e</span>xamination. Each student appearing in Class X <span class="char-style-override-23">e</span>xamination of the Board is assigned a roll number under the function <span class="char-style-override-10">f</span> and each roll number is assigned a code number under <span class="char-style-override-10">g</span>. After the answer scripts are examined, examiner enters the mark against each code number in a mark book and submits to the office of the Board. The Board officials decode by assigning roll number back to each code number through a process reverse to <span class="char-style-override-10">g</span> and thus mark gets attached to roll number rather than code number. Further, the process reverse to <span class="char-style-override-10">f</span> assigns a roll number to the student having that roll number. This helps in assigning mark to the student scoring that mark. We observe that while composing <span class="char-style-override-10">f</span> and <span class="char-style-override-10">g</span>, to get <span class="char-style-override-10">gof</span>, first <span class="char-style-override-10">f</span> and then <span class="char-style-override-10">g</span> was applied, while in the reverse process of the composite <span class="char-style-override-10">gof</span>, first the reverse process of <span class="char-style-override-10">g</span> is applied and then the reverse process of <span class="char-style-override-10">f</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 22</span> Let <span class="char-style-override-10">f</span> : {1, 2, 3} <span class="char-style-override-11">→</span> {<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span>} be one-one and onto function given by<br/>
    <span class="char-style-override-10">f</span><span>(1) =</span> <span class="char-style-override-10">a</span>, <span class="char-style-override-10">f</span><span>(2) =</span> <span class="char-style-override-10">b</span> and <span class="char-style-override-10">f</span><span>(3) =</span> <span class="char-style-override-10">c</span>. Show that there exists a function <span class="char-style-override-10">g</span> : {<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span>} <span class="char-style-override-11">→</span> {1, 2, 3} such that <span class="char-style-override-10">gof</span> = I<span class="char-style-override-16">X</span> and <span class="char-style-override-10">fog</span> = I<span class="char-style-override-16">Y</span>, where, X = {1, 2, 3} and Y = {<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span>}.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Consider <span class="char-style-override-10">g</span> : {<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span>} <span class="char-style-override-11">→</span> {1, 2, 3} as <span class="char-style-override-10">g</span>(<span class="char-style-override-10">a</span>) = 1, <span class="char-style-override-10">g</span>(<span class="char-style-override-10">b</span>) = 2 and <span class="char-style-override-10">g</span>(<span class="char-style-override-10">c</span>) = 3. It is easy to verify that the composite <span class="char-style-override-10">gof</span> = I<span class="char-style-override-16">X</span> is the identity function on X and the composite <span class="char-style-override-10">fog</span> = I<span class="char-style-override-16">Y</span> is the identity function on Y.</p>

    <p class="Remarks"><span class="char-style-override-15">Remark</span> The interesting fact is that the result mentioned in the above example is true for an arbitrary one-one and onto function <span class="char-style-override-10">f</span> : X <span class="char-style-override-11">→</span> Y. Not only this, even the converse is also true , i.e., if <span class="char-style-override-10">f</span> : X <span class="char-style-override-11">→</span> Y is a function such that there exists a function <span class="char-style-override-10">g</span> : Y <span class="char-style-override-11">→</span> X such that <span class="char-style-override-10">gof</span> = I<span class="char-style-override-16">X</span> and <span class="char-style-override-10">fog</span> = I<span class="char-style-override-16">Y</span>, then <span class="char-style-override-10">f</span> must be one-one and onto.</p>

    <p class="Body-Text">The above discussion, Example 22 and Remark lead to the following definition:</p>

    <p class="Definition"><span class="char-style-override-12">D</span><span class="char-style-override-12">efinition 9</span> A function <span class="char-style-override-10">f</span> : X <span class="char-style-override-11">→</span> Y is defined to be <span class="char-style-override-10">invertible</span>, if there exists a function <span class="char-style-override-10"><br/>
    g</span> : Y <span class="char-style-override-11">→</span> X <span>such that</span> <span class="char-style-override-10">gof</span> <span>= I</span><span class="char-style-override-16">X</span> <span>and</span> <span class="char-style-override-10">fog</span> <span>= I</span><span class="char-style-override-16">Y</span><span>. The function</span> <span class="char-style-override-10">g</span> <span>is called the</span> <span class="char-style-override-10">inverse of f</span> <span>and is denoted by</span> <span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span><span>.</span></p>

    <p class="Body-Text para-style-override-11"><span>Thus, if</span> <span class="char-style-override-10">f</span> <span>is invertible, then</span> <span class="char-style-override-10">f</span> <span>must be one-one and onto and conversely, if</span> <span class="char-style-override-10">f</span> <span>is one-one and onto, then</span> <span class="char-style-override-10">f</span> <span>must be invertible. This fact significantly helps for proving a function</span> <span class="char-style-override-10">f</span> <span>to be invertible by showing that</span> <span class="char-style-override-10">f</span> <span>is one-one and onto, specially when the actual inverse of</span> <span class="char-style-override-10">f</span> <span>is not to b</span>e determined.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 2</span><span class="char-style-override-12">3</span> Let <span class="char-style-override-10">f</span> : <span class="char-style-override-14">N</span> <span class="char-style-override-11">→</span> Y be a function defined as <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span>) = 4<span class="char-style-override-10">x</span> + 3, where,</p>

    <p class="Remarks"><span>Y = {</span><span class="char-style-override-10">y</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">N</span><span>:</span> <span class="char-style-override-10">y</span> = 4<span class="char-style-override-10">x</span> + 3 for some <span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">N</span>}. Show that <span class="char-style-override-10">f</span> is invertible. Find the inverse.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Consider an arbitrary element <span class="char-style-override-10">y</span> of Y. By the definition of Y, <span class="char-style-override-10">y</span> = 4<span class="char-style-override-10">x</span> + 3,<br/>
    for <span>some</span> <span class="char-style-override-10">x</span> in the domain <span class="char-style-override-14">N</span><span>. This shows that</span><img alt="1733.png" class="frame-23" src="../Images/image34.png"/>. Define <span class="char-style-override-10">g</span> : Y <span class="char-style-override-11">→</span> <span class="char-style-override-14">N</span> by&nbsp;<img alt="1738.png" class="frame-24" src="../Images/image35.png"/><span class="char-style-override-10">.</span> Now, <span class="char-style-override-10">go</span><span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">g</span>(<span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>)) = <span class="char-style-override-10">g</span>(4<span class="char-style-override-10">x</span> + 3) =&nbsp;<img alt="1743.png" class="frame-25" src="../Images/image36.png"/>&nbsp;and</p>

    <p class="Remarks"><span class="char-style-override-10">fog</span>(<span class="char-style-override-10">y</span>) = <span class="char-style-override-10">f</span>(<span class="char-style-override-10">g</span>(<span class="char-style-override-10">y</span>)) = <span class="char-style-override-10">f</span><img alt="1748.png" class="frame-26" src="../Images/image37.png"/>&nbsp;= <span class="char-style-override-10">y</span> – 3 + 3 = <span class="char-style-override-10">y</span>. This shows that <span class="char-style-override-10">gof</span> = I<span class="char-style-override-16">N</span> and <span class="char-style-override-10">fog</span> = I<span class="char-style-override-16">Y</span>, which implies that <span class="char-style-override-10">f</span> is invertible and <span class="char-style-override-10">g</span> is the inverse of <span class="char-style-override-10">f</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 24</span> <span>Let Y = {</span><span class="char-style-override-10">n</span><span class="char-style-override-19">2</span> <span>:</span> <span class="char-style-override-10">n</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">N</span><span>}</span> <span class="char-style-override-11">⊂</span> <span class="char-style-override-14">N</span><span>. Consider</span> <span class="char-style-override-10">f</span> <span>:</span> <span class="char-style-override-14">N</span> <span class="char-style-override-11">→</span> <span>Y as</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">n</span><span>) =</span> <span class="char-style-override-10">n</span><span class="char-style-override-19">2</span><span>. Show that</span> <span class="char-style-override-10">f</span> <span>is invertible. Find the inverse of</span> <span class="char-style-override-10">f</span><span>.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> <span>An arbitrary</span> element <span class="char-style-override-10">y</span> in Y is of the form <span class="char-style-override-10">n</span><span class="char-style-override-19">2</span>, for some <span class="char-style-override-10">n</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">N</span>. This<br/>
    implies that <span class="char-style-override-10">n</span> = <span class="Basic-Graphics-Frame"><img alt="1759.png" class="frame-27" src="../Images/image39.png"/></span>. This gives a function <span class="char-style-override-10">g</span> : <b>Y</b> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>N</b></span>, defined by <span class="char-style-override-10">g</span>(<span class="char-style-override-10">y</span>) =&nbsp;<img alt="1759.png" class="frame-27" src="../Images/image39.png"/>. Now,&nbsp;</p>

    <p class="Remarks"><span class="char-style-override-10">gof</span>(<span class="char-style-override-10">n</span>) = <span class="char-style-override-10">g</span>(<span class="char-style-override-10">n</span><span class="char-style-override-19">2</span>) =<img alt="1764.png" class="frame-28" src="../Images/image40.png"/>&nbsp; = <span class="char-style-override-10">n</span> and <span class="char-style-override-10">fog</span>(<span class="char-style-override-10">y</span>) =<img alt="1769.png" class="frame-29" src="../Images/image41.png"/>, which shows that&nbsp;</p>

    <p class="Remarks"><span class="char-style-override-10">gof</span> = I<span class="char-style-override-16">N</span> and <span class="char-style-override-10">fog</span> = I<span class="char-style-override-16">Y</span>. Hence, <span class="char-style-override-10">f</span> is invertible with <span class="char-style-override-10">f</span> <span class="char-style-override-19">–1</span> = <span class="char-style-override-10">g</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 25</span> Let <span class="char-style-override-10">f</span> : <span class="char-style-override-14"><b>N</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span> be a function defined as <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = 4<span class="char-style-override-10">x</span><span class="char-style-override-19">2</span> + 12<span class="char-style-override-10">x</span> + 15. Show that <span class="char-style-override-10">f</span> : <span class="char-style-override-14"><b>N</b></span><span class="char-style-override-11">→</span> S, where, S is the range of <span class="char-style-override-10">f</span>, is invertible. Find the inverse of <span class="char-style-override-10">f</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Let <span class="char-style-override-10">y</span> be an arbitrary element of range <span class="char-style-override-10">f</span>. Then <span class="char-style-override-10">y</span> = 4<span class="char-style-override-10">x</span><span class="char-style-override-19">2</span> + 12<span class="char-style-override-10">x</span> + 15, for some <span class="char-style-override-10">x</span> in <span class="char-style-override-14">N</span>, which implies that <span class="char-style-override-10">y =</span> (2<span class="char-style-override-10">x</span> + 3)<span class="char-style-override-19">2</span> + 6. This gives<img alt="1774.png" class="frame-30" src="../Images/image42.png"/>&nbsp; , as <span class="char-style-override-10">y</span> <span class="char-style-override-11">≥</span> 6.</p>

    <p class="Body-Text para-style-override-11">Let us define <span class="char-style-override-10">g</span> : S <span class="char-style-override-11">→</span> <span class="char-style-override-14">N</span> by <span class="char-style-override-10">g</span>(<span class="char-style-override-10">y</span>) =&nbsp;<img alt="1779.png" class="frame-31" src="../Images/image43.png"/>.</p>

    <p class="x1 para-style-override-16">Now <span class="char-style-override-10">go</span><span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">g</span>(<span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>)) = <span class="char-style-override-10">g</span>(4<span class="char-style-override-10">x</span><span class="char-style-override-19">2</span> + 12<span class="char-style-override-10">x</span> + 15) = <span class="char-style-override-10">g</span>((2<span class="char-style-override-10">x</span> + 3)<span class="char-style-override-19">2</span> + 6)</p>

    <p class="x1 para-style-override-16">=&nbsp;<img alt="1784.png" class="frame-32" src="../Images/image44.png"/></p>

    <p class="x1 para-style-override-16">and <span class="char-style-override-10">fog</span> (<span class="char-style-override-10">y</span>) =<img alt="1789.png" class="frame-33" src="../Images/image45.png"/>&nbsp;&nbsp;</p>

    <p class="x1 para-style-override-16">=&nbsp;<img alt="1794.png" class="frame-34" src="../Images/image46.png"/>&nbsp;= <span class="char-style-override-10">y</span> – 6 + 6 = <span class="char-style-override-10">y</span>.</p>

    <p class="Body-Text para-style-override-5">Hence, <span class="char-style-override-10">gof</span> = I<span class="char-style-override-24">N</span> and <span class="char-style-override-10">fog</span> =I<span class="char-style-override-16">S</span>. This implies that <span class="char-style-override-10">f</span> is invertible with <span class="char-style-override-10">f</span> <span class="char-style-override-19">–1</span> = <span class="char-style-override-10">g</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 2</span><span class="char-style-override-12">6</span> Consider <span class="char-style-override-10">f</span> : <span class="char-style-override-14"><b>N</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>N</b></span>, <span class="char-style-override-10">g</span> : <span class="char-style-override-14"><b>N</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>N</b></span> and <span class="char-style-override-10">h</span> : <span class="char-style-override-14"><b>N</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span> defined as <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span>) = 2<span class="char-style-override-10">x</span>, <span class="char-style-override-10">g</span><span>(</span><span class="char-style-override-10">y</span>) = 3<span class="char-style-override-10">y</span> + 4 and <span class="char-style-override-10">h</span><span>(</span><span class="char-style-override-10">z</span>) = sin <span class="char-style-override-10">z</span>,<img alt="1799.png" class="frame-14" src="../Images/image47.png"/>&nbsp; <span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span> and <span class="char-style-override-10">z</span> in N. Show that <span class="char-style-override-10">ho</span>(<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>) = (<span class="char-style-override-10">h</span>o<span class="char-style-override-10">g</span>) o<span class="char-style-override-10">f.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> We have</p>

    <p class="Remarks"><span class="char-style-override-10">h</span>o(<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>) (<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">h</span>(<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span> (<span class="char-style-override-10">x</span>)) = <span class="char-style-override-10">h</span>(<span class="char-style-override-10">g</span>(<span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>))) = <span class="char-style-override-10">h</span>(<span class="char-style-override-10">g</span>(2<span class="char-style-override-10">x</span>))</p>

    <p class="Body-Text">= <span class="char-style-override-10">h</span>(3(2<span class="char-style-override-10">x</span>) + 4) = <span class="char-style-override-10">h</span>(6<span class="char-style-override-10">x</span> + 4) = sin (6<span class="char-style-override-10">x</span> + 4)&nbsp;<img alt="1804.png" class="frame-35" src="../Images/image48.png"/></p>

    <p class="Body-Text para-style-override-5">Also, ((<span class="char-style-override-10">h</span>o<span class="char-style-override-10">g</span><span>)</span><span>o</span><span class="char-style-override-10">f</span> ) (<span class="char-style-override-10">x</span>) = (<span class="char-style-override-10">h</span>o<span class="char-style-override-10">g</span>) <span>(</span><span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>)) = (<span class="char-style-override-10">h</span>o<span class="char-style-override-10">g</span>) (2<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">h</span>(<span class="char-style-override-10">g</span>(2<span class="char-style-override-10">x</span>))</p>

    <p class="Body-Text">= <span class="char-style-override-10">h</span>(3(2<span class="char-style-override-10">x</span>) + 4) = <span class="char-style-override-10">h</span>(6<span class="char-style-override-10">x</span> + 4) = sin (6<span class="char-style-override-10">x</span> + 4), <span class="Basic-Graphics-Frame"><img alt="1815.png" class="frame-14" src="../Images/image50.png"/></span><span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">N</span>.</p>

    <p class="Body-Text para-style-override-5">This shows that <span class="char-style-override-10">h</span>o(<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>) = (<span class="char-style-override-10">h</span>o<span class="char-style-override-10">g</span><span>)o</span><span class="char-style-override-10">f</span>.</p>

    <p class="Body-Text para-style-override-5">This result is true in general situation as well.</p>

    <p class="Definition"><span class="char-style-override-12">Theorem 1</span> If <span class="char-style-override-10">f</span> : X <span class="char-style-override-11">→</span> Y, <span class="char-style-override-10">g</span> : Y <span class="char-style-override-11">→</span> Z and <span class="char-style-override-10">h</span> : Z <span class="char-style-override-11">→</span> S are functions, then</p>

    <p class="Definition para-style-override-14"><span class="char-style-override-10">h</span>o(<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>) = (<span class="char-style-override-10">h</span>o<span class="char-style-override-10">g</span>)o<span class="char-style-override-10">f</span>.</p>

    <p class="Body-Text para-style-override-5"><span class="char-style-override-12">Proof</span> We have</p>

    <p class="Body-Text para-style-override-5"><span class="char-style-override-10">h</span>o(<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>) (<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">h</span>(<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>)) = <span class="char-style-override-10">h</span>(<span class="char-style-override-10">g</span>(<span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>))), <span class="Basic-Graphics-Frame"><img alt="1820.png" class="frame-14" src="../Images/image51.png"/></span><span class="char-style-override-10">x</span> in X</p>

    <p class="Body-Text para-style-override-5">and (<span class="char-style-override-10">h</span>o<span class="char-style-override-10">g</span>) o<span class="char-style-override-10">f</span> (<span class="char-style-override-10">x</span>) <span>=</span> <span class="char-style-override-10">h</span>o<span class="char-style-override-10">g</span><span>(</span><span class="char-style-override-10">f</span> (<span class="char-style-override-10">x</span>)) = <span class="char-style-override-10">h</span>(<span class="char-style-override-10">g</span><span>(</span><span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span>))),<img alt="1810.png" class="frame-14" src="../Images/image49.png"/>&nbsp; <span class="char-style-override-10">x</span> in X.</p>

    <p class="Body-Text para-style-override-16"><span>Hence</span>, <span class="char-style-override-10">h</span>o(<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span><span>)</span> = (<span class="char-style-override-10">h</span>o<span class="char-style-override-10">g</span><span>)</span><span>o</span><span class="char-style-override-10">f</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 27</span> Consider <span class="char-style-override-10">f</span> : {1, 2, 3} <span class="char-style-override-11">→</span> {<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span>} and <span class="char-style-override-10">g</span> : {<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span>} <span class="char-style-override-11">→</span> {apple, ball, cat} defined as <span class="char-style-override-10">f</span>(1) = <span class="char-style-override-10">a</span>, <span class="char-style-override-10">f</span>(2) = <span class="char-style-override-10">b</span>, <span class="char-style-override-10">f</span>(3) = <span class="char-style-override-10">c</span>, <span class="char-style-override-10">g</span>(<span class="char-style-override-10">a</span>) = apple, <span class="char-style-override-10">g</span>(<span class="char-style-override-10">b</span>) = ball and <span class="char-style-override-10">g</span>(<span class="char-style-override-10">c</span>) = cat.<br/>
    Show that <span class="char-style-override-10">f</span>, <span class="char-style-override-10">g</span> and <span class="char-style-override-10">gof</span> are invertible. Find out <span class="char-style-override-10">f</span> <span class="char-style-override-19">–1</span>, <span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span> and (<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>)<span class="char-style-override-19">–1</span> and show that<br/>
    (<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>) <span class="char-style-override-19">–1</span> = <span class="char-style-override-10">f</span> <span class="char-style-override-19">–1</span><span>o</span><span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Note that by definition, <span class="char-style-override-10">f</span> and <span class="char-style-override-10">g</span> are bijective functions. Let <span class="char-style-override-10"><br/>
    f</span> <span class="char-style-override-19">–1</span>: {<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span>} <span class="char-style-override-11">→</span> (1, 2, 3} and <span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span> : {apple, ball, cat} <span class="char-style-override-11">→</span> {<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span>} be defined as <span class="char-style-override-10">f</span> <span class="char-style-override-19">–1</span>{<span class="char-style-override-10">a</span>} = 1, <span class="char-style-override-10">f</span> <span class="char-style-override-19">–1</span>{<span class="char-style-override-10">b</span>} = 2, <span class="char-style-override-10">f</span> <span class="char-style-override-19">–1</span>{<span class="char-style-override-10">c</span>} = 3, <span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span>{apple} = <span class="char-style-override-10">a</span>, <span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span>{ball} = <span class="char-style-override-10">b</span> and <span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span>{cat} = <span class="char-style-override-10">c</span>. <span>I</span><span>t</span> <span>is easy to verify that</span> <span class="char-style-override-10">f</span> <span class="char-style-override-19">–</span><span class="char-style-override-19">1</span><span>o</span><span class="char-style-override-10">f</span> <span class="char-style-override-10">=</span> <span>I</span><span class="char-style-override-16">{1, 2, 3}</span>, <span class="char-style-override-10">f</span><span>o</span> <span class="char-style-override-10">f</span> <span class="char-style-override-19">–1</span> <span class="char-style-override-10">=</span> <span>I</span><span class="char-style-override-16">{</span><span class="char-style-override-18">a</span><span class="char-style-override-16">,</span> <span class="char-style-override-18">b</span><span class="char-style-override-16">,</span> <span class="char-style-override-18">c</span><span class="char-style-override-16">}</span>, <span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span><span>o</span><span class="char-style-override-10">g =</span> <span>I</span><span class="char-style-override-16">{</span><span class="char-style-override-18">a</span><span class="char-style-override-16">,</span> <span class="char-style-override-18">b</span><span class="char-style-override-16">,</span> <span class="char-style-override-18">c</span><span class="char-style-override-16">}</span> <span>and</span> <span class="char-style-override-10">g</span><span>o</span> <span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span> <span class="char-style-override-10">=</span> <span>I</span><span class="char-style-override-16">D</span><span>,</span> where, D = {apple, ball, cat}. Now, <span class="char-style-override-10">gof</span> : {1, 2, 3} <span class="char-style-override-11">→</span> {apple, ball, cat} is given by <span class="char-style-override-10">go</span><span class="char-style-override-10">f</span>(1) = apple, <span class="char-style-override-10">gof</span>(2) = ball, <span class="char-style-override-10">gof</span>(3) = cat. We <span>can define</span></p>

    <p class="Remarks"><span>(</span><span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>)<span class="char-style-override-19">–1</span> : {apple, ball, cat} <span class="char-style-override-11">→</span> {1, 2, 3} by (<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>)<span class="char-style-override-19">–1</span> (apple) = 1, (<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>)<span class="char-style-override-19">–1</span> (ball) = 2 and</p>

    <p class="Remarks">(<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>)<span class="char-style-override-19">–1</span> (cat) = 3. It is easy to see that (<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>)<span class="char-style-override-19">–1</span> o (<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>) = I<span class="char-style-override-16">{1, 2, 3}</span> and</p>

    <p class="Remarks">(<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>) o (<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>)<span class="char-style-override-19">–1</span> = I<span class="char-style-override-16">D</span>. Thus, we have seen that <span class="char-style-override-10">f</span>, <span class="char-style-override-10">g</span> and <span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span> are invertible.</p>

    <p class="Body-Text para-style-override-5">Now, <span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span>o<span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span> (apple) = <span class="char-style-override-10">f</span> <span class="char-style-override-19">–1</span>(<span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span>(apple)) = <span class="char-style-override-10">f</span> <span class="char-style-override-19">–1</span>(<span class="char-style-override-10">a</span>) = 1 = (<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>)<span class="char-style-override-19">–1</span> (apple)</p>

    <p class="Body-Text"><span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span>o<span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span> (ball) = <span class="char-style-override-10">f</span> <span class="char-style-override-19">–1</span>(<span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span>(ball)) = <span class="char-style-override-10">f</span> <span class="char-style-override-19">–1</span>(<span class="char-style-override-10">b</span>) = 2 = (<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>)<span class="char-style-override-19">–1</span> (ball) and</p>

    <p class="Body-Text"><span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span>o<span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span> (cat) = <span class="char-style-override-10">f</span> <span class="char-style-override-19">–1</span>(<span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span>(cat)) = <span class="char-style-override-10">f</span> <span class="char-style-override-19">–1</span>(<span class="char-style-override-10">c</span>) = 3 = (<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>)<span class="char-style-override-19">–1</span> (cat).</p>

    <p class="Body-Text para-style-override-5">Hence (<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>)<span class="char-style-override-19">–1</span> = <span class="char-style-override-10">f</span> <span class="char-style-override-19">–1</span>o<span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span>.</p>

    <p class="Body-Text">The above result is true in general situation also.</p>

    <p class="Definition"><span class="char-style-override-12">Theorem 2</span> Let <span class="char-style-override-10">f</span> : X <span class="char-style-override-11">→</span> Y and <span class="char-style-override-10">g</span> : Y <span class="char-style-override-11">→</span> Z be two invertible functions. Then <span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span> is also invertible with (<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>)<span class="char-style-override-19">–1</span> = <span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span>o<span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span>.</p>

    <p class="Body-Text para-style-override-5"><span class="char-style-override-12">Proof</span> To show that <span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span> is invertible with (<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>)<span class="char-style-override-19">–1</span> = <span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span>o<span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span>, it is enough to show that<br/>
    (<span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span>o<span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span><span>)o</span>(<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>) = I<span class="char-style-override-16">X</span> and (<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>)o<span>(</span><span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span>o<span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span>) = I<span class="char-style-override-16">Z</span>.</p>

    <p class="eq-1 para-style-override-5">Now, (<span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span>o<span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span><span>)o</span>(<span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span>) = ((<span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span>o<span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span>) o<span class="char-style-override-10">g</span>) o<span class="char-style-override-10">f</span>, by Theorem 1</p>

    <p class="eq-1 para-style-override-5">= (<span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span>o(<span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span>o<span class="char-style-override-10">g</span>)) o<span class="char-style-override-10">f</span>, by Theorem 1</p>

    <p class="eq-1 para-style-override-5">= (<span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span> <span>o</span>I<span class="char-style-override-16">Y</span>) o<span class="char-style-override-10">f</span>, by definition of <span class="char-style-override-10">g</span><span class="char-style-override-19">–1</span></p>

    <p class="eq-1 para-style-override-5">= I<span class="char-style-override-16">X</span>.</p>

    <p class="Body-Text para-style-override-5">Similarly, it can be shown that (<span class="char-style-override-10">go</span><span class="char-style-override-10">f</span><span>)</span><span>o</span>(<span class="char-style-override-10">f</span> <span class="char-style-override-19">–1</span> <span>o</span><span class="char-style-override-10">g</span> <span class="char-style-override-19">–1</span>) = I<span class="char-style-override-16">Z</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 28</span> Let S = {1, 2, 3}. Determine whether the functions <span class="char-style-override-10">f</span> : S <span class="char-style-override-11">→</span> S defined as below have inverses. Find <span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span>, if it exists.</p>

    <p class="Remark-tab">(a) <span class="char-style-override-10">f</span> = {(1, 1), (2, 2), (3, 3)}</p>

    <p class="Opt-2-a-b-c- para-style-override-17">(b) <span class="char-style-override-10">f</span> = {(1, 2), (2, 1), (3, 1)}</p>

    <p class="Opt-2-a-b-c- para-style-override-17">(c) <span class="char-style-override-10">f</span> = {(1, 3), (3, 2), (2, 1)}</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span></p>

    <p class="Opt-2-a-b-c- para-style-override-17">(a) It is easy to see that <span class="char-style-override-10">f</span> is one-one and onto, so that <span class="char-style-override-10">f</span> is invertible with the inverse<br/>
    <span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span> of <span class="char-style-override-10">f</span> given by <span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span> = {(1, 1), (2, 2), (3, 3)} = <span class="char-style-override-10">f</span>.</p>

    <p class="Opt-2-a-b-c- para-style-override-17">(b) Since <span class="char-style-override-10">f</span>(2) = <span class="char-style-override-10">f</span>(3) = 1, <span class="char-style-override-10">f</span> is not one-one, so that <span class="char-style-override-10">f</span> is not invertible.</p>

    <p class="Opt-2-a-b-c- para-style-override-17">(c) It is easy to see that <span class="char-style-override-10">f</span> is one-one and onto, so that <span class="char-style-override-10">f</span> is invertible with</p>

    <p class="Opt-2-a-b-c- para-style-override-17"><span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span> = {(3, 1), (2, 3), (1, 2)}.</p>

    <p class="Exercise"><span class="char-style-override-7"><br/></span></p>

    <div class="lining_box">
      <p class="Exercise"><span class="char-style-override-7">EXERCISE 1.3</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">1.</span> Let <span class="char-style-override-10">f</span> : {1, 3, 4} <span class="char-style-override-11">→</span> <span>{1, 2, 5} and</span> <span class="char-style-override-10">g</span> <span>: {1, 2, 5}</span> <span class="char-style-override-11">→</span> <span>{1, 3} be given by</span></p>

      <p class="option-3-aline"><span class="char-style-override-10">f</span> <span>= {(1, 2), (3, 5), (4, 1)} and</span> <span class="char-style-override-10">g</span> <span>= {(1, 3), (2, 3), (5, 1)}. Write down</span> <span class="char-style-override-10">gof</span><span>.</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">2.</span> <span>Let</span> <span class="char-style-override-10">f</span><span>,</span> <span class="char-style-override-10">g</span> <span>and</span> <span class="char-style-override-10">h</span> <span>be functions from</span> <span class="char-style-override-14"><b>R</b></span> <span>to</span> <span class="char-style-override-14"><b>R</b></span><span>. Show that</span></p>

      <p class="eq-1"><span>(</span><span class="char-style-override-10">f</span> <span>+</span> <span class="char-style-override-10">g</span><span>)o</span><span class="char-style-override-10">h</span> <span>=</span> <span class="char-style-override-10">f</span><span>o</span><span class="char-style-override-10">h</span> <span>+</span> <span class="char-style-override-10">g</span><span>o</span><span class="char-style-override-10">h</span></p>

      <p class="eq-1"><span>(</span><span class="char-style-override-10">f</span> <span>.</span> <span class="char-style-override-10">g</span><span>)</span>o<span class="char-style-override-10">h</span> = (<span class="char-style-override-10">f</span>o<span class="char-style-override-10">h</span>) . (<span class="char-style-override-10">g</span>o<span class="char-style-override-10">h</span>)</p>

      <p class="option-3-aline"><span class="char-style-override-12">3.</span> Find <span class="char-style-override-10">g</span>o<span class="char-style-override-10">f</span> and <span class="char-style-override-10">f</span>o<span class="char-style-override-10">g</span>, if</p>

      <p class="option--i-----ii-">(i) <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = | <span class="char-style-override-10">x</span> | and <span class="char-style-override-10">g</span>(<span class="char-style-override-10">x</span>) = | 5<span class="char-style-override-10">x</span> – 2 |</p>

      <p class="option--i-----ii-">(ii) <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = 8<span class="char-style-override-10">x</span><span class="char-style-override-19">3</span> and <span class="char-style-override-10">g</span>(<span class="char-style-override-10">x</span>) = <img alt="1825" src="../Images/image52.png"/>.</p>

      <p class="option-3-aline"><span class="char-style-override-12">4.</span> If <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) =<img alt="1830.png" class="frame-37" src="../Images/image53.png"/>, <span class="Basic-Graphics-Frame"><img alt="1835.png" class="frame-38" src="../Images/image54.png"/></span>, &nbsp;show that <span class="char-style-override-10">f</span>o<span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span>, for all<img alt="1840.png" class="frame-39" src="../Images/image55.png"/>&nbsp; . What is the inverse of <span class="char-style-override-10">f</span>?</p>

      <p class="option-3-aline"><span class="char-style-override-12">5.</span> State wit<span>h reason whether following functions have inverse</span></p>

      <p class="option--i-----ii-"><span>(i)</span> <span class="char-style-override-10">f</span> <span>: {1, 2, 3, 4}</span> <span class="char-style-override-11">→</span> <span>{10} with</span></p>

      <p class="option--i-----ii-"><span class="char-style-override-10">f</span> <span>= {(1, 10), (2, 10), (3, 10), (4, 10)}</span></p>

      <p class="option--i-----ii-"><span>(ii)</span> <span class="char-style-override-10">g</span> <span>: {5, 6, 7, 8}</span> <span class="char-style-override-11">→</span> <span>{1, 2, 3, 4} with</span></p>

      <p class="option--i-----ii-"><span class="char-style-override-10">g</span> <span>= {(5, 4), (6, 3), (7, 4), (8, 2)}</span></p>

      <p class="option--i-----ii-"><span>(iii)</span> <span class="char-style-override-10">h</span> <span>: {2, 3, 4, 5}</span> <span class="char-style-override-11">→</span> <span>{7, 9, 11, 13} with</span></p>

      <p class="option--i-----ii- para-style-override-11"><span class="char-style-override-10">h</span> <span>= {(2,</span> 7), (3, 9), (4, 11), (5, 13)}</p>

      <p class="option-3-aline"><span class="char-style-override-12">6.</span> Show that <span class="char-style-override-10">f</span> : [–1, 1] <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span>, given by <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10"><span class="Basic-Graphics-Frame"><img alt="1850.png" class="frame-38" src="../Images/image57.png"/></span></span> is one-one. Find the inverse of the function <span class="char-style-override-10">f</span> : [–1, 1] <span class="char-style-override-11">→</span> Range <span class="char-style-override-10">f</span>.</p>

      <p class="option-3-aline">(Hint: For <span class="char-style-override-10">y</span> <span class="char-style-override-11">∈</span> Range <span class="char-style-override-10">f</span>, <span class="char-style-override-10">y</span> = <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) =<img alt="1850.png" class="frame-38" src="../Images/image57.png"/> , for some <span class="char-style-override-10">x</span> in [–1, 1], i.e., <span class="char-style-override-10">x</span> = <img alt="1855.png" class="frame-41" src="../Images/image58.png"/>)</p>

      <p class="option-3-aline"><span class="char-style-override-12">7.</span> Consider <span class="char-style-override-10">f</span> : <span class="char-style-override-14"><b>R</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span> given by <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = 4<span class="char-style-override-10">x</span> + 3. Show that <span class="char-style-override-10">f</span> is invertible. Find the inverse of <span class="char-style-override-10">f</span>.</p>

      <p class="option-3-aline"><span class="char-style-override-12">8.</span> Consider <span class="char-style-override-10">f</span> : <span class="char-style-override-14"><b>R</b></span><span class="char-style-override-24">+</span> <span class="char-style-override-11">→</span> [4, <span class="char-style-override-11">∞</span>) given by <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span><span class="char-style-override-19">2</span> + 4. Show that <span class="char-style-override-10">f</span> is invertible with the inverse <span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span> of <span class="char-style-override-10">f</span> given by <span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span>(<span class="char-style-override-10">y</span>) = <img alt="1861.png" class="frame-42" src="../Images/image59.png"/>, where <span class="char-style-override-14">R</span><span class="char-style-override-16">+</span> is the set of all non-negative real numbers.</p>

      <p class="option-3-aline para-style-override-11"><span class="char-style-override-12">9.</span> Consider <span class="char-style-override-10">f</span> : <span class="char-style-override-14">R</span><span class="char-style-override-24">+</span> <span class="char-style-override-11">→</span> [– 5, <span class="char-style-override-11">∞</span>) given by <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = 9<span class="char-style-override-10">x</span><span class="char-style-override-19">2</span> + 6<span class="char-style-override-10">x</span> – 5. Show that <span class="char-style-override-10">f</span> is invertible with <span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span>(<span class="char-style-override-10">y</span>) =<img alt="1866.png" class="frame-43" src="../Images/image60.png"/> .</p>

      <p class="option-3-aline para-style-override-11"><span class="char-style-override-12">10.</span> Let <span class="char-style-override-10">f</span> : X <span class="char-style-override-11">→</span> Y be an invertible function. Show that <span class="char-style-override-10">f</span> has unique inverse.</p>

      <p class="option-3-aline">(Hint: <span>suppose</span> <span class="char-style-override-10">g</span><span class="char-style-override-16">1</span> and <span class="char-style-override-10">g</span><span class="char-style-override-16">2</span> are two inverses of <span class="char-style-override-10">f</span>. Then for all <span class="char-style-override-10">y</span> <span class="char-style-override-11">∈</span> Y,</p>

      <p class="option-3-aline"><span class="char-style-override-10">f</span>o<span class="char-style-override-10">g</span><span class="char-style-override-16">1</span>(<span class="char-style-override-10">y</span>) = 1<span class="char-style-override-16">Y</span>(<span class="char-style-override-10">y</span>) = <span class="char-style-override-10">f</span>o<span class="char-style-override-10">g</span><span class="char-style-override-16">2</span>(<span class="char-style-override-10">y</span>). Use one-one ness of <span class="char-style-override-10">f</span>).</p>

      <p class="option-3-aline"><span class="char-style-override-12">11.</span> Consider <span class="char-style-override-10">f</span> : {1, 2, 3} <span class="char-style-override-11">→</span> {<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span>} given by <span class="char-style-override-10">f</span>(1) = <span class="char-style-override-10">a</span>, <span class="char-style-override-10">f</span>(2) = <span class="char-style-override-10">b</span> and <span class="char-style-override-10">f</span>(3) = <span class="char-style-override-10">c</span>. Find <span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span> and show that (<span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span>)<span class="char-style-override-19">–1</span> = <span class="char-style-override-10">f</span>.</p>

      <p class="option-3-aline"><span class="char-style-override-12">12.</span> Let <span class="char-style-override-10">f</span><span>: X</span> <span class="char-style-override-11">→</span> Y be an invertible function. Show that the inverse of <span class="char-style-override-10">f</span> <span class="char-style-override-19">–1</span> <span>is</span> <span class="char-style-override-10">f</span>, i.e.,</p>

      <p class="option-3-aline">(<span class="char-style-override-10">f</span><span class="char-style-override-19">–1</span>)<span class="char-style-override-19">–1</span> = <span class="char-style-override-10">f</span>.</p>

      <p class="option-3-aline"><span class="char-style-override-12">13.</span> If <span class="char-style-override-10">f</span><span>:</span> <span class="char-style-override-14"><b>R</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span> be given by <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="Basic-Graphics-Frame"><img alt="1871.png" class="frame-44" src="../Images/image61.png"/>, then <span class="char-style-override-10">f</span>o<span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) is</span></p>

      <p class="Opt--4-a---d">(A)<img alt="1876.png" class="frame-36" src="../Images/image62.png"/> (B) <span class="char-style-override-10">x</span><span class="char-style-override-19">3</span> (C) <span class="char-style-override-10">x</span> (D) (3 – <span class="char-style-override-10">x</span><span class="char-style-override-19">3</span>).</p>

      <p class="option-3-aline"><span class="char-style-override-12">14.</span> Let <span class="char-style-override-10">f</span> : <span class="char-style-override-14">R</span> –<img alt="1881.png" class="frame-45" src="../Images/image63.png"/> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> be a function defined as <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <img alt="1886.png" class="frame-46" src="../Images/image64.png"/>. The inverse of <span class="char-style-override-10">f</span> is the map <span class="char-style-override-10">g</span> : Range <span class="char-style-override-10">f</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> – <img alt="1891.png" class="frame-45" src="../Images/image65.png"/> given by</p>

      <p class="Opt-2-a-b-c-">(A) <img alt="1896.png" class="frame-47" src="../Images/image66.png"/> (B)<img alt="1901.png" class="frame-47" src="../Images/image67.png"/></p>

      <p class="Opt-2-a-b-c-">(C)<img alt="1906.png" class="frame-47" src="../Images/image68.png"/> (D) <img alt="1912.png" class="frame-47" src="../Images/image69.png"/></p>
    </div>

    <p class="heading"><span class="char-style-override-7"><br/></span></p>

    <p class="heading"><span class="char-style-override-7"><br/></span></p>

    <p class="heading"><span class="char-style-override-7">1.5 Binary Operations</span></p>

    <p class="Body-Text para-style-override-5">Right from the school days, you must have come across four fundamental operations namely addition, subtraction, multiplication and division. The main feature of these operations is that given any two numbers <span class="char-style-override-10">a</span> and <span class="char-style-override-10">b</span>, we associate another number <span class="char-style-override-10">a</span> + <span class="char-style-override-10">b</span> or <span class="char-style-override-10">a</span> – <span class="char-style-override-10">b</span> or <span class="char-style-override-10">ab</span> or <span class="Basic-Graphics-Frame"><img alt="1922.png" class="frame-10" src="../Images/image71.png"/></span>, <span class="char-style-override-10">b</span> <span class="char-style-override-11">≠</span> 0. It is to be noted that only two numbers can be added or multiplied at a time. When we need to add three numbers, we first add two numbers and the result is then added to the third number. Thus, addition, multiplication, subtraction and division are examples of binary operation, as ‘binary’ means two. If we want to have a general definition which can cover all these four operations, then the set of numbers is to be replaced by an arbitrary set X and then general binary operation is nothing but association of any pair of elements <span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span> from X to another element of X. This gives rise to a general definition as follows:</p>

    <p class="Definition para-style-override-11"><span class="char-style-override-12">Definition 10</span> A binary operation <span class="char-style-override-11">∗</span> on a set A is a function <span class="char-style-override-11">∗</span> : A × A <span class="char-style-override-11">→</span> A. We denote <span class="char-style-override-11">∗</span> (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>) by <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 29</span> Show that addition, subtraction and multiplication are binary operations on <span class="char-style-override-14">R</span>, but division is not a binary operation on <span class="char-style-override-14">R</span>. Further, show that division is a binary operation on the set <span class="char-style-override-14">R</span><span class="char-style-override-21">∗</span> of nonzero real numbers.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> + : <span class="char-style-override-14"><b>R</b></span> × <span class="char-style-override-14"><b>R</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span> is given by</p>

    <p class="Body-Text para-style-override-5">(<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>) <span class="char-style-override-11">→</span> <span class="char-style-override-10">a</span> + <span class="char-style-override-10">b</span></p>

    <p class="Body-Text para-style-override-5">– : <span class="char-style-override-14"><b>R</b></span> × <span class="char-style-override-14"><b>R</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span> is given by</p>

    <p class="Body-Text para-style-override-5">(<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>) <span class="char-style-override-11">→</span> <span class="char-style-override-10">a</span> – <span class="char-style-override-10">b</span></p>

    <p class="Body-Text para-style-override-5">× : <span class="char-style-override-14"><b>R</b></span> × <span class="char-style-override-14"><b>R</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span> is given by</p>

    <p class="Body-Text para-style-override-5">(<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>) <span class="char-style-override-11">→</span> <span class="char-style-override-10">ab</span></p>

    <p class="Body-Text para-style-override-11">Since ‘+’, ‘–’ and ‘×’ are functions, they are binary operations on <span class="char-style-override-14">R</span>.</p>

    <p class="Body-Text para-style-override-16">But <span class="char-style-override-11">÷</span> : <span class="char-style-override-14"><b>R</b></span> × <span class="char-style-override-14"><b>R</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span>, given by (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>) <span class="char-style-override-11">→</span> <span class="char-style-override-10"><span class="Basic-Graphics-Frame"><img alt="1927.png" class="frame-10" src="../Images/image72.png"/></span></span>, is not a function and hence not a binary operation, as for <span class="char-style-override-10">b</span> = 0, <span class="Basic-Graphics-Frame"><img alt="1932.png" class="frame-10" src="../Images/image73.png"/></span> is not defined.</p>

    <p class="Body-Text para-style-override-11">However, <span class="char-style-override-11">÷</span> : <span class="char-style-override-14"><b>R</b></span><span class="char-style-override-21">∗</span> × <span class="char-style-override-14"><b>R</b></span><span class="char-style-override-21">∗</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>R</b></span><span class="char-style-override-21">∗</span>, given by (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>) <span class="char-style-override-11">→</span> <img alt="1932.png" class="frame-10" src="../Images/image73.png"/> is a function and hence a binary operation on <span class="char-style-override-14"><b>R</b></span><span class="char-style-override-21">∗</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 30</span> Show that subtraction and division are not binary operations on <span class="char-style-override-14">N</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> – : <span class="char-style-override-14"><b>N</b></span> × <span class="char-style-override-14"><b>N</b></span> <span class="char-style-override-11">→</span> <span class="char-style-override-14"><b>N</b></span>, given by (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>) <span class="char-style-override-11">→</span> <span class="char-style-override-10">a</span> – <span class="char-style-override-10">b</span>, is not binary operation, as the image <span>of (3, 5) under ‘–’ is 3 – 5 = – 2</span> <span class="char-style-override-11">∉</span> <span class="char-style-override-14">N</span>. Similarly, <span class="char-style-override-11">÷</span> : <span class="char-style-override-14">N</span> × <span class="char-style-override-14">N</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">N</span>, given by (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>) <span class="char-style-override-11">→</span> <span class="char-style-override-10">a</span> <span class="char-style-override-11">÷</span> <span class="char-style-override-10">b</span> is not a binary operation, as the image of (3, 5) under <span class="char-style-override-11">÷</span> is 3 <span class="char-style-override-11">÷</span> 5 =<img alt="1937.png" class="frame-10" src="../Images/image74.png"/> <span class="char-style-override-11">∉</span> <span class="char-style-override-14">N</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 31</span> Show that <span class="char-style-override-11">∗</span> : <span class="char-style-override-14">R</span> × <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> given by (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>) <span class="char-style-override-11">→</span> <span class="char-style-override-10">a</span> + 4<span class="char-style-override-10">b</span><span class="char-style-override-19">2</span> is a binary operation.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Since <span class="char-style-override-11">∗</span> carries each pair (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>) to a unique element <span class="char-style-override-10">a</span> + 4<span class="char-style-override-10">b</span><span class="char-style-override-19">2</span> in <span class="char-style-override-14">R</span>, <span class="char-style-override-11">∗</span> is a binary operation on <span class="char-style-override-14">R</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">E</span><span class="char-style-override-12">xample 32</span> <span>Let P be the set of all subsets of a given set X. Show that</span> <span class="char-style-override-11">∪</span> <span>: P × P</span> <span class="char-style-override-11">→</span> <span>P given by (A, B)</span> <span class="char-style-override-11">→</span> <span>A</span> <span class="char-style-override-11">∪</span> <span>B and</span> <span class="char-style-override-11">∩</span> <span>: P × P</span> <span class="char-style-override-11">→</span> <span>P given by (A, B)</span> <span class="char-style-override-11">→</span> <span>A</span> <span class="char-style-override-11">∩</span> <span>B are binary operations on the set P.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> <span>Since union operation</span> <span class="char-style-override-11">∪</span> <span>carries each pair (A, B) in P × P to a unique element A</span> <span class="char-style-override-11">∪</span> <span>B in P,</span> <span class="char-style-override-11">∪</span> <span>is binary operation on P. Similarly, the intersection operation</span> <span class="char-style-override-11">∩</span> <span>carries each pair (A, B) in P × P to a unique element A</span> <span class="char-style-override-11">∩</span> <span>B in P,</span> <span class="char-style-override-11">∩</span> <span>is a binary operation on P.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Example 3</span><span class="char-style-override-12">3</span> <span>Show that the</span> <img alt="1942.png" class="frame-48" src="../Images/image75.png"/> <span>:</span> <span class="char-style-override-14">R</span> <span>×</span> <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> <span>given by (</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>)</span> <span class="char-style-override-11">→</span> <span>max {</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>} and the</span> <span class="char-style-override-11"><span class="Basic-Graphics-Frame"><img alt="1952.png" class="frame-48" src="../Images/image77.png"/></span></span> <span>:</span> <span class="char-style-override-14">R</span> <span>×</span> <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> <span>given by (</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>)</span> <span class="char-style-override-11">→</span> <span>min {</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>} are binary operations.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> <span>Since <span class="Basic-Graphics-Frame"><img alt="1957.png" class="frame-48" src="../Images/image78.png"/></span> carries each pair (</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>) in</span> <span class="char-style-override-14">R</span> <span>×</span> <span class="char-style-override-14">R</span> <span>to a unique element namely maximum of</span> <span class="char-style-override-10">a</span> <span>and</span> <span class="char-style-override-10">b</span> <span>lying in</span> <span class="char-style-override-14">R</span><span>,</span> <img alt="1957.png" class="frame-48" src="../Images/image78.png"/> <span>is a binary operation. Using the similar argument, one can say that</span> <img alt="1978.png" class="frame-48" src="../Images/image82.png"/> <span>is also a binary operation.</span></p>

    <p class="Remarks para-style-override-12"><span class="char-style-override-15">Remark</span> <span><span class="Basic-Graphics-Frame"><img alt="1973.png" class="frame-48" src="../Images/image81.png"/></span>(4, 7) = 7,</span> <img alt="1973.png" class="frame-48" src="../Images/image81.png"/><span>(4, – 7) = 4,</span> <span class="char-style-override-11"><span class="Basic-Graphics-Frame"><img alt="1983.png" class="frame-48" src="../Images/image83.png"/></span></span><span>(4, 7) = 4 and</span> <img alt="1983.png" class="frame-48" src="../Images/image83.png"/><span>(4, – 7) = – 7.</span></p>

    <p class="Body-Text"><span>When number of elements in a set A is small, we can express a binary operation</span> <span class="char-style-override-11">∗</span> <span>on the set A through a table called the</span> <span class="char-style-override-10">operation table</span> <span>for the operation</span> <span class="char-style-override-11">∗</span><span>. For example consider A = {1, 2, 3}. Then, the operation <span class="Basic-Graphics-Frame"><img alt="1993.png" class="frame-48" src="../Images/image85.png"/></span> on A defined in Example 33 can be</span> <span>expressed by the following operation</span> <span>t</span><span>able</span> <span>(Table 1.1)</span> <span>. Here</span><span>, <span class="Basic-Graphics-Frame"><img alt="1998.png" class="frame-48" src="../Images/image86.png"/></span></span> <span>(1, 3) = 3,</span> <span><span class="Basic-Graphics-Frame"><img alt="2003.png" class="frame-48" src="../Images/image87.png"/></span></span> <span>(2, 3) = 3,</span> <img alt="1993.png" class="frame-48" src="../Images/image85.png"/> <span>(1, 2) = 2.</span></p>

    <p class="Body-Text para-style-override-15" style="text-align: center;"><span class=""><b>Table 1.1</b></span></p>

    <p class="Body-Text para-style-override-15" style="text-align: center;"><span class=""><img alt="1418.png" class="frame-49" src="../Images/image7.png"/><br/></span></p>

    <p class="Body-Text"/>

    <p class="Body-Text para-style-override-11"><span>Here, we are having 3 rows and 3 columns in the operation table with (</span><span class="char-style-override-10">i</span><span>,</span> <span class="char-style-override-10">j</span><span>) the entry of the table being maximum of</span> <span class="char-style-override-10">i</span><span class="char-style-override-19">th</span> <span>and</span> <span class="char-style-override-10">j</span><span class="char-style-override-19">th</span> <span>elements of the set A. This can be generalised for general operation</span> <span class="char-style-override-11">∗</span> <span>: A × A</span> <span class="char-style-override-11">→</span> <span>A. If A = {</span><span class="char-style-override-10">a</span><span class="char-style-override-16">1</span><span>,</span> <span class="char-style-override-10">a</span><span class="char-style-override-16">2</span><span>, ...,</span> <span class="char-style-override-10">a</span><sub>n</sub><span>}. Then the operation table will be having</span> <span class="char-style-override-10">n</span> <span>rows and</span> <span class="char-style-override-10">n</span> <span>columns with (</span><span class="char-style-override-10">i</span><span>,</span> <span class="char-style-override-10">j</span><span>)</span><span class="char-style-override-19">th</span> <span>entry being</span> <span class="char-style-override-10">a</span><sub>i</sub> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">a</span><sub>j</sub><span>. Conversely, given any operation table having</span> <span class="char-style-override-10">n</span> <span>rows and</span> <span class="char-style-override-10">n</span> <span>columns with each entry</span> <span>being an element of A = {</span><span class="char-style-override-10">a</span><span class="char-style-override-16">1</span><span>,</span> <span class="char-style-override-10">a</span><span class="char-style-override-16">2</span><span>, ...,</span> <span class="char-style-override-10">a</span><sub>n</sub><span>}, we can define a binary operation</span> <span class="char-style-override-11">∗</span> <span>: A × A</span> <span class="char-style-override-11">→</span> <span>A given by</span> <span class="char-style-override-10">a</span><sub>i</sub> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">a</span><sub>j</sub> <span>= the entry in the</span> <span class="char-style-override-10">i</span><span class="char-style-override-19">th</span> <span>row and</span> <span class="char-style-override-10">j</span><span class="char-style-override-19">th</span> <span>column of the operation table.</span></p>

    <p class="Body-Text"><span>One may note that 3 and 4 can be added in any order and the result is same, i.e.,<br/>
    3 + 4 = 4 + 3, but subtraction of 3 and 4 in different order give different results, i.e.,<br/>
    3 – 4</span> <span class="char-style-override-11">≠</span> <span>4 – 3. Similarly, in case of multiplication of 3 and 4, order is immaterial, but division o</span><span>f</span> <span>3 and 4 in different order give different results. Thus, addition and multiplication of 3 and 4 are meaningful, but subtraction and division of 3 and 4 are meaningless. For subtraction and division we have to write ‘subtract 3 from 4’, ‘subtract 4 from 3’, ‘divide 3 by 4’ or ‘divide 4 by 3’.</span></p>

    <p class="Body-Text"><span>This leads to the following definition:</span></p>

    <p class="Definition para-style-override-11"><span class="char-style-override-12">Definition 11</span> A binary operation <span class="char-style-override-11">∗</span> on the set X is called <span class="char-style-override-10">commutative</span>, if <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = <span class="char-style-override-10">b</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">a</span>, for every <span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span> <span class="char-style-override-11">∈</span> X.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 34</span> Show that + : <span class="char-style-override-14">R</span> × <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> and × : <span class="char-style-override-14">R</span> × <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> are commutative binary operations, but – : <span class="char-style-override-14">R</span> × <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> and <span class="char-style-override-11">÷</span> : <span class="char-style-override-14">R</span><span class="char-style-override-21">∗</span> × <span class="char-style-override-14">R</span><span class="char-style-override-21">∗</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span><span class="char-style-override-21">∗</span> are not commutative.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Since <span class="char-style-override-10">a</span> + <span class="char-style-override-10">b</span> = <span class="char-style-override-10">b</span> + <span class="char-style-override-10">a</span> and <span class="char-style-override-10">a</span> × <span class="char-style-override-10">b</span> = <span class="char-style-override-10">b</span> × <span class="char-style-override-10">a</span>, <span class="Basic-Graphics-Frame"><img alt="2014.png" class="frame-14" src="../Images/image89.png"/></span><span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">R</span>, ‘+’ and ‘×’ are commutative binary operation. However, ‘–’ is not commutative, since 3 – 4 <span class="char-style-override-11">≠</span> 4 – 3.</p>

    <p class="Body-Text para-style-override-16">Similarly, 3 <span class="char-style-override-11">÷</span> 4 <span class="char-style-override-11">≠</span> 4 <span class="char-style-override-11">÷</span> 3 shows that ‘<span class="char-style-override-11">÷</span>’ is not commutative.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 35</span> Show that <span class="char-style-override-11">∗</span> : <span class="char-style-override-14">R</span> × <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> defined by <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = <span class="char-style-override-10">a</span> + 2<span class="char-style-override-10">b</span> is not commutative.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Since 3 <span class="char-style-override-11">∗</span> 4 = 3 + 8 = 11 and 4 <span class="char-style-override-11">∗</span> 3 = 4 + 6 = 10, showing that the operation <span class="char-style-override-11">∗</span> is not commutative.</p>

    <p class="Body-Text">If we want to associate three elements of a set X through a binary operation on X, we encounter a natural problem. The expression <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">c</span> may be interpreted as<br/>
    (<span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span>) <span class="char-style-override-11">∗</span> <span class="char-style-override-10">c</span> or <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> (<span class="char-style-override-10">b</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">c</span>) and these two expressions need not be same. For example,<br/>
    (8 – 5) – 2 <span class="char-style-override-11">≠</span> 8 – (5 – 2). Therefore, association of three numbers 8, 5 and 3 through the binary operation ‘subtraction’ is meaningless, unless bracket is used. But in case of addition, 8 + 5 + 2 has the same value whether we look at it as ( 8 + 5) + 2 or as 8 + (5 + 2). Thus, association of 3 or even more than 3 numbers through addition is meaningful without using bracket. This leads to the following:</p>

    <p class="Definition"><span class="char-style-override-12">Definition 1</span><span class="char-style-override-12">2</span> A binary operation <span class="char-style-override-11">∗</span> : A × A <span class="char-style-override-11">→</span> A is said to be <span class="char-style-override-10">associative</span> if</p>

    <p class="Definition para-style-override-21"><span>(</span><span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span>) <span class="char-style-override-11">∗</span> <span class="char-style-override-10">c</span> = <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> (<span class="char-style-override-10">b</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">c</span>), <span class="Basic-Graphics-Frame"><img alt="2019.png" class="frame-14" src="../Images/image90.png"/></span><span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span>, <span class="char-style-override-11">∈</span> A.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 36</span> Show that addition and multiplication are associative binary operation on <span class="char-style-override-14">R</span>. But subtraction is not associative on <span class="char-style-override-14">R</span>. Division is not associative on <span class="char-style-override-14">R</span><span class="char-style-override-21">∗</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Addition and multiplication are associative, since (<span class="char-style-override-10">a</span> + <span class="char-style-override-10">b</span>) + <span class="char-style-override-10">c</span> = <span class="char-style-override-10">a</span> + (<span class="char-style-override-10">b</span> + <span class="char-style-override-10">c</span>) and (<span class="char-style-override-10">a</span> × <span class="char-style-override-10">b</span>) × <span class="char-style-override-10">c</span> = <span class="char-style-override-10">a</span> × (<span class="char-style-override-10">b</span> × <span class="char-style-override-10">c</span>) <span class="Basic-Graphics-Frame"><img alt="2024.png" class="frame-14" src="../Images/image91.png"/></span> <span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span> <span class="char-style-override-11">∈</span> R. However, subtraction and division are not associative, as (8 – 5) – 3 <span class="char-style-override-11">≠</span> 8 – (5 – 3) and (8 <span class="char-style-override-11">÷</span> 5) <span class="char-style-override-11">÷</span> 3 <span class="char-style-override-11">≠</span> 8 <span class="char-style-override-11">÷</span> (5 <span class="char-style-override-11">÷</span> 3).</p>

    <p class="Remarks"><span class="char-style-override-12">Example 37</span> Show that <span class="char-style-override-11">∗</span> : <span class="char-style-override-14">R</span> × <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> given by <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> <span class="char-style-override-11">→</span> <span class="char-style-override-10">a</span> + 2<span class="char-style-override-10">b</span> is not associative.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> The operation <span class="char-style-override-11">∗</span> is not associative, since</p>

    <p class="Remarks para-style-override-12">(8 <span class="char-style-override-11">∗</span> 5) <span class="char-style-override-11">∗</span> 3 = (8 + 10) <span class="char-style-override-11">∗</span> 3 = (8 + 10) + 6 = 24,</p>

    <p class="Remarks para-style-override-12">while 8 <span class="char-style-override-11">∗</span> (5 <span class="char-style-override-11">∗</span> 3) = 8 <span class="char-style-override-11">∗</span> (5 + 6) = 8 <span class="char-style-override-11">∗</span> 11 = 8 + 22 = 30.</p>

    <p class="Remarks"><span style="color: rgb(0, 174, 239); font-weight: bold;">Remark</span> Associative property of a binary operation is very important in the sense that with this property of a binary operation, we can write <span class="char-style-override-10">a</span><span class="char-style-override-16">1</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">a</span><span class="char-style-override-16">2</span> <span class="char-style-override-11">∗</span> ... <span class="char-style-override-11">∗</span> <span class="char-style-override-10">a</span><sub>n</sub> which is not ambiguous. But in absence of this property, the expression <span class="char-style-override-10">a</span><span class="char-style-override-16">1</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">a</span><span class="char-style-override-16">2</span> <span class="char-style-override-11">∗</span> ... <span class="char-style-override-11">∗</span> <span class="char-style-override-10">a</span><sub>n</sub> is ambiguous unless brackets are used. Recall that in the earlier classes brackets were used whenever subtraction or division operations or more than one operation occurred.</p>

    <p class="Body-Text">F<span>or the binary operation ‘+’ on</span> <span class="char-style-override-14">R</span><span>, the interesting feature of the number zero is that</span> <span class="char-style-override-10"><br/>
    a</span> <span>+ 0 =</span> <span class="char-style-override-10">a</span> <span>= 0 +</span> <span class="char-style-override-10">a</span><span>, i.e., any number remains unaltered by adding zero. But in case of multiplication, the number 1 plays this role, as</span> <span class="char-style-override-10">a</span> <span>× 1 =</span> <span class="char-style-override-10">a</span> <span>= 1 ×</span> <span class="char-style-override-10">a</span><span>, <span class="Basic-Graphics-Frame"><img alt="2029.png" class="frame-14" src="../Images/image92.png"/></span></span> <span class="char-style-override-10">a</span> <span>in</span> <span class="char-style-override-14">R</span><span>. This leads to the following definition</span>:</p>

    <p class="Definition"><span class="char-style-override-12">Definition 1</span><span class="char-style-override-12">3</span> <span>Given a binary operation</span> <span class="char-style-override-11">∗</span> <span>: A × A</span> <span class="char-style-override-11">→</span> <span>A, an element</span> <span class="char-style-override-10">e</span> <span class="char-style-override-11">∈</span> <span>A, if it exists, is called</span> <span class="char-style-override-10">identity</span> <span>for the operation</span> <span class="char-style-override-11">∗</span><span>, if</span> <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">e</span> <span>=</span> <span class="char-style-override-10">a</span> <span>=</span> <span class="char-style-override-10">e</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">a</span><span>, <span class="Basic-Graphics-Frame"><img alt="2034.png" class="frame-14" src="../Images/image93.png"/></span></span> <span class="char-style-override-10">a</span> <span class="char-style-override-11">∈</span> <span>A.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Example 38</span> <span>Show that zero is the identity for addition on</span> <span class="char-style-override-14">R</span> <span>and 1 is the identity for multiplication on R.</span> But there is no identity element for the operations</p>

    <p class="Remarks para-style-override-13">– : <span class="char-style-override-14">R</span> × <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> and <span class="char-style-override-11">÷</span> : <span class="char-style-override-14">R</span><span class="char-style-override-21">∗</span> × <span class="char-style-override-14">R</span><span class="char-style-override-21">∗</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span><span class="char-style-override-21">∗</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> <span class="char-style-override-10">a</span> <span>+ 0 = 0 +</span> <span class="char-style-override-10">a</span> <span>=</span> <span class="char-style-override-10">a</span> <span>and</span> <span class="char-style-override-10">a</span> <span>× 1 =</span> <span class="char-style-override-10">a</span> <span>= 1 ×</span> <span class="char-style-override-10">a</span><span>, <span class="Basic-Graphics-Frame"><img alt="2039.png" class="frame-14" src="../Images/image94.png"/></span></span><span class="char-style-override-10">a</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">R</span> <span>implies that 0 and 1 are identity elements for the operations ‘+’ and ‘×’ respectively. Further, there is no element</span> <span class="char-style-override-10">e</span> <span>in</span> <span class="char-style-override-14">R</span> <span>with</span> <span class="char-style-override-10">a</span> <span>–</span> <span class="char-style-override-10">e</span> <span>=</span> <span class="char-style-override-10">e</span> <span>–</span> <span class="char-style-override-10">a</span><span>, <span class="Basic-Graphics-Frame"><img alt="2044.png" class="frame-14" src="../Images/image95.png"/></span></span><span class="char-style-override-10">a</span><span>. Similarly, we can not find any element</span> <span class="char-style-override-10">e</span> <span>in</span> <span class="char-style-override-14">R</span><span class="char-style-override-21">∗</span> <span>such that</span> <span class="char-style-override-10"><br/>
    a</span> <span class="char-style-override-11">÷</span> <span class="char-style-override-10">e</span> <span>=</span> <span class="char-style-override-10">e</span> <span class="char-style-override-11">÷</span> <span class="char-style-override-10">a</span><span>, <span class="Basic-Graphics-Frame"><img alt="2049.png" class="frame-10" src="../Images/image96.png"/></span></span><span class="char-style-override-10">a</span> <span>in</span> <span class="char-style-override-14">R</span><span class="char-style-override-21">∗</span><span>. Hence, ‘–’ and ‘</span><span class="char-style-override-11">÷</span><span>’ do not have identity element.</span></p>

    <p class="Remarks"><span class="char-style-override-15">Remark</span> <span>Zero is identity for the addition operation on</span> <span class="char-style-override-14">R</span> <span>but it is not identity for the addition operation on</span> <span class="char-style-override-14">N</span><span>, as 0</span> <span class="char-style-override-11">∉</span> <span class="char-style-override-14">N</span><span>. In fact the addition operation on</span> <span class="char-style-override-14">N</span> <span>does not have any identity.</span></p>

    <p class="Body-Text"><span>One further notices that for the addition operation + :</span> <span class="char-style-override-14">R</span> <span>×</span> <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span><span>, given any</span> <span class="char-style-override-10">a</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">R</span><span>, t</span>here exists – <span class="char-style-override-10">a</span> in <span class="char-style-override-14">R</span> such that <span class="char-style-override-10">a</span> + (– <span class="char-style-override-10">a</span>) = 0 (identity for ‘+’) = (– <span class="char-style-override-10">a</span>) + <span class="char-style-override-10">a</span>. Similarly, for the multiplication operation on <span class="char-style-override-14">R</span>, given any <span class="char-style-override-10">a</span> <span class="char-style-override-11">≠</span> 0 in <span class="char-style-override-14">R</span>, we can choose <span class="Basic-Graphics-Frame"><img alt="2054.png" class="frame-10" src="../Images/image97.png"/></span> in <span class="char-style-override-14">R</span> such that <span class="char-style-override-10">a</span> × <span class="Basic-Graphics-Frame"><img alt="2059.png" class="frame-10" src="../Images/image98.png"/></span> = 1(identity for ‘×’) = <span class="Basic-Graphics-Frame"><img alt="2065.png" class="frame-10" src="../Images/image99.png"/></span>× <span class="char-style-override-10">a</span>. This leads to the following definition:</p>

    <p class="Definition"><span class="char-style-override-12">Definition 14</span> Given a binary operation <span class="char-style-override-11">∗</span> : A × A <span class="char-style-override-11">→</span> A with the identity element <span class="char-style-override-10">e</span> in A, an element <span class="char-style-override-10">a</span> <span class="char-style-override-11">∈</span> A is said to be <span class="char-style-override-10">invertible</span> with respect to the operation <span class="char-style-override-11">∗</span>, if there exists an element <span class="char-style-override-10">b</span> in A such that <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = <span class="char-style-override-10">e</span> = <span class="char-style-override-10">b</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">a</span> and <span class="char-style-override-10">b</span> is called the <span class="char-style-override-10">inverse of a</span> and is denoted by <span class="char-style-override-10">a</span><span class="char-style-override-19">–1</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 39</span> Show that – <span class="char-style-override-10">a</span> is the inverse of <span class="char-style-override-10">a</span> for the addition operation ‘+’ on <span class="char-style-override-14">R</span> and <span class="Basic-Graphics-Frame"><img alt="2070.png" class="frame-10" src="../Images/image100.png"/></span> is the inverse of <span class="char-style-override-10">a</span> <span class="char-style-override-11">≠</span> 0 for the multiplication operation ‘×’ on <span class="char-style-override-14">R</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> As <span class="char-style-override-10">a</span> + (– <span class="char-style-override-10">a</span>) = <span class="char-style-override-10">a</span> – <span class="char-style-override-10">a</span> = 0 and (– <span class="char-style-override-10">a</span>) + <span class="char-style-override-10">a</span> = 0, – <span class="char-style-override-10">a</span> is the inverse of <span class="char-style-override-10">a</span> for addition. Similarly, for <span class="char-style-override-10">a</span> <span class="char-style-override-11">≠</span> 0, <span class="char-style-override-10">a</span> ×<span class="Basic-Graphics-Frame"><img alt="2075.png" class="frame-10" src="../Images/image101.png"/></span>= 1 = <span class="Basic-Graphics-Frame"><img alt="2080.png" class="frame-10" src="../Images/image102.png"/></span>× <span class="char-style-override-10">a</span> implies that <span class="Basic-Graphics-Frame"><img alt="2085.png" class="frame-10" src="../Images/image103.png"/></span> is the inverse of <span class="char-style-override-10">a</span> for multiplication.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 40</span> Show that – <span class="char-style-override-10">a</span> is not the inverse of <span class="char-style-override-10">a</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">N</span> for the addition operation + on <span class="char-style-override-14">N</span> and <span class="Basic-Graphics-Frame"><img alt="2090.png" class="frame-10" src="../Images/image104.png"/></span>is not the inverse of <span class="char-style-override-10">a</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">N</span> for multiplication operation × on <span class="char-style-override-14">N</span>, for <span class="char-style-override-10">a</span> <span class="char-style-override-11">≠</span> 1.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Since – <span class="char-style-override-10">a</span> <span class="char-style-override-11">∉</span> <span class="char-style-override-14">N</span>, – <span class="char-style-override-10">a</span> can not be inverse of <span class="char-style-override-10">a</span> for addition operation on <span class="char-style-override-14">N</span>, although – <span class="char-style-override-10">a</span> satisfies <span class="char-style-override-10">a</span> + (– <span class="char-style-override-10">a</span>) = 0 = (– <span class="char-style-override-10">a</span>) + <span class="char-style-override-10">a</span>.</p>

    <p class="Body-Text">Similarly, for <span class="char-style-override-10">a</span> <span class="char-style-override-11">≠</span> 1 in <span class="char-style-override-14">N</span>,<img alt="2090.png" class="frame-10" src="../Images/image104.png"/>&nbsp; &nbsp;<span class="char-style-override-11">∉</span> <span class="char-style-override-14">N</span>, which implies that other than 1 no element of <span class="char-style-override-14">N</span> has inverse for multiplication operation on <span class="char-style-override-14">N</span>.</p>

    <p class="Body-Text"><span>Examples 34, 36, 38 and 39 show that addition on</span> <span class="char-style-override-14">R</span> <span>is a commutative and associative binary operation</span> with 0 as the identity element and – <span class="char-style-override-10">a</span> as the inverse of <span class="char-style-override-10">a</span> in <span class="char-style-override-14">R</span> <span class="char-style-override-10"><img alt="2095.png" class="frame-14" src="../Images/image105.png"/>a</span>.</p>

    <p class="Body-Text"><br/></p>

    <div class="lining_box">
      <p class="Exercise" style="text-align: center;"><span class="char-style-override-7">EXERCISE 1.4</span></p>

      <p class="Exercise" style="text-align: center;"><span class="char-style-override-7"><br/></span></p>

      <p class="option-3-aline"><span class="char-style-override-12">1.</span> Determi<span>ne whether or not each of the definition of</span> <span class="char-style-override-11">∗</span> <span>given below gives a binary operation. In the event that</span> <span class="char-style-override-11">∗</span> <span>is not a binary operation, give justification for this.</span></p>

      <p class="option--i-----ii-"><span>(i) On</span> <span class="char-style-override-14"><b>Z</b></span><span class="char-style-override-19">+</span><span>, define</span> <span class="char-style-override-11">∗</span> <span>by</span> <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> <span>=</span> <span class="char-style-override-10">a</span> <span>–</span> <span class="char-style-override-10">b</span></p>

      <p class="option--i-----ii-"><span>(ii) On</span> <span class="char-style-override-14"><b>Z</b></span><span class="char-style-override-19">+</span><span>, define</span> <span class="char-style-override-11">∗</span> <span>by</span> <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> <span>=</span> <span class="char-style-override-10">ab</span></p>

      <p class="option--i-----ii-"><span>(iii) On</span> <span class="char-style-override-14"><b>R</b></span><span>, define</span> <span class="char-style-override-11">∗</span> <span>by</span> <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> <span>=</span> <span class="char-style-override-10">ab</span><span class="char-style-override-19">2</span></p>

      <p class="option--i-----ii-"><span>(iv) On</span> <span class="char-style-override-14"><b>Z</b></span><span class="char-style-override-19">+</span><span>, define</span> <span class="char-style-override-11">∗</span> <span>by</span> <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> <span>=</span> <span>|</span><span class="char-style-override-10">a</span> <span>–</span> <span class="char-style-override-10">b</span><span>|</span></p>

      <p class="option--i-----ii-"><span>(v) On</span> <span class="char-style-override-14"><b>Z</b></span><span class="char-style-override-19">+</span><span>, defin</span>e <span class="char-style-override-11">∗</span> by <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = <span class="char-style-override-10">a</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">2.</span> For each <span>operation</span> <span class="char-style-override-11">∗</span> <span>defined below, determine whether</span> <span class="char-style-override-11">∗</span> <span>is binary, commutative or associative.</span></p>

      <p class="option--i-----ii-"><span>(i) On</span> <span class="char-style-override-14">Z</span><span>, define</span> <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> <span>=</span> <span class="char-style-override-10">a</span> <span>–</span> <span class="char-style-override-10">b</span></p>

      <p class="option--i-----ii-"><span>(ii) On</span> <span class="char-style-override-14">Q</span><span>,</span> define <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = <span class="char-style-override-10">ab</span> + 1</p>

      <p class="option--i-----ii-">(iii) On <span class="char-style-override-14">Q</span>, define <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> =<img alt="Screenshot from 2015-06-22 12:36:09" src="../Images/image153.png"/></p>

      <p class="option--i-----ii-">(iv) <span>On</span> <span class="char-style-override-14">Z</span><span class="char-style-override-19">+</span><span>, define</span> <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> <span>= 2</span><span class="char-style-override-27">ab</span></p>

      <p class="option--i-----ii-"><span>(v) On</span> <span class="char-style-override-14">Z</span><span class="char-style-override-19">+</span>, define <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = <span class="char-style-override-10">a</span><span class="char-style-override-27">b</span></p>

      <p class="option--i-----ii-">(vi) On <span class="char-style-override-14">R</span> – {– 1}, define <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> =<img alt="2105.png" class="frame-39" src="../Images/image107.png"/></p>

      <p class="option-3-aline"><span class="char-style-override-12">3.</span> C<span>onsider th</span><span>e binary operation</span> <span class="char-style-override-11"><span class="Basic-Graphics-Frame"><img alt="2116.png" class="frame-48" src="../Images/image109.png"/></span></span><span>on the set {1, 2, 3, 4, 5} defined by</span> <span class="char-style-override-10"><br/>
      a</span> <span class="char-style-override-11"><span class="Basic-Graphics-Frame"><img alt="2121.png" class="frame-48" src="../Images/image110.png"/></span></span> <span class="char-style-override-10">b</span> <span>= min</span> {<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>}. Write the operation table of the operation <img alt="2116.png" class="frame-48" src="../Images/image109.png"/>.</p>

      <p class="option-3-aline"><span class="char-style-override-12">4.</span> Consider a binary operation <span class="char-style-override-11">∗</span> on the set {1, 2, 3, 4, 5} given by the following multiplication table (Table 1.2).</p>

      <p class="option--i-----ii- para-style-override-11">(i) Compute (2 <span class="char-style-override-11">∗</span> 3) <span class="char-style-override-11">∗</span> 4 and 2 <span class="char-style-override-11">∗</span> (3 <span class="char-style-override-11">∗</span> 4)</p>

      <p class="option--i-----ii- para-style-override-11">(<span>ii) Is</span> <span class="char-style-override-11">∗</span> commutative?</p>

      <p class="option--i-----ii- para-style-override-11">(iii) Compute (2 <span class="char-style-override-11">∗</span> 3) <span class="char-style-override-11">∗</span> (4 <span class="char-style-override-11">∗</span> 5).</p>

      <p class="option-3-aline">(Hint: use the following table)</p>

      <p class="Body-Text para-style-override-15" style="text-align: center;"><span class=""><b>Table 1.2</b></span></p>

      <p class="Body-Text para-style-override-15" style="text-align: center;"><span class=""><img alt="1487.png" class="frame-51" src="../Images/image8.png"/><br/></span></p>

      <p class="Body-Text para-style-override-22"/>

      <p class="option-3-aline"><span class="char-style-override-12">5.</span> Let <span class="char-style-override-11">∗′</span> be the binary operation on the set {1, 2, 3, 4, 5} defined by <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗′</span> <span class="char-style-override-10">b</span> = H.C.F. of <span class="char-style-override-10">a</span> and <span class="char-style-override-10">b</span>. Is the operation <span class="char-style-override-11">∗′</span> same as the operation <span class="char-style-override-11">∗</span> defined in Exercise 4 above? Justify your answer.</p>

      <p class="option-3-aline"><span class="char-style-override-12">6.</span> Let <span class="char-style-override-11">∗</span> be the binary operation on <span class="char-style-override-14">N</span> given by <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = L.C.M. of <span class="char-style-override-10">a</span> and <span class="char-style-override-10">b</span>. Find</p>

      <p class="option--i-----ii-">(i) 5 <span class="char-style-override-11">∗</span> 7, 20 <span class="char-style-override-11">∗</span> 16 (ii) Is <span class="char-style-override-11">∗</span> commutative?</p>

      <p class="option--i-----ii-">(iii) Is <span class="char-style-override-11">∗</span> associative? (iv) Find the identity of <span class="char-style-override-11">∗</span> in <span class="char-style-override-14">N</span></p>

      <p class="option--i-----ii-">(v) Which elements of <span class="char-style-override-14">N</span> are invertible for the operation <span class="char-style-override-11">∗</span>?</p>

      <p class="option-3-aline"><span class="char-style-override-12">7.</span> Is <span class="char-style-override-11">∗</span> defined on the set {1, 2, 3, 4, 5} by <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = L.C.M. of <span class="char-style-override-10">a</span> and <span class="char-style-override-10">b</span> a binary operation? Justify your answer.</p>

      <p class="option-3-aline"><span class="char-style-override-12">8.</span> Let <span class="char-style-override-11">∗</span> be the binary operation on <span class="char-style-override-14">N</span> defined by <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = H.C.F. of <span class="char-style-override-10">a</span> and <span class="char-style-override-10">b</span>.<br/>
      Is <span class="char-style-override-11">∗</span> commutative? Is <span class="char-style-override-11">∗</span> associative? Does there exist identity for this binary operation on <span class="char-style-override-14">N</span>?</p>

      <div class="Basic-Graphics-Frame frame-2">
        <br/>
      </div>

      <p class="option-3-aline"/>

      <p class="option-3-aline"><span class="char-style-override-12">9.</span> Let <span class="char-style-override-11">∗</span> be a binary operation on the set <span class="char-style-override-14">Q</span> of rational numbers as follows:</p>

      <p class="option--i-----ii- para-style-override-11">(i) <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = <span class="char-style-override-10">a</span> – <span class="char-style-override-10">b</span> (ii) <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = <span class="char-style-override-10">a</span><span class="char-style-override-19">2</span> + <span class="char-style-override-10">b</span><span class="char-style-override-19">2</span></p>

      <p class="option--i-----ii- para-style-override-11">(iii) <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = <span class="char-style-override-10">a</span> + <span class="char-style-override-10">ab</span> (iv) <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = (<span class="char-style-override-10">a</span> – <span class="char-style-override-10">b</span>)<span class="char-style-override-19">2</span></p>

      <p class="option--i-----ii- para-style-override-11">(v) <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = <img alt="2126" src="../Images/image111.png"/> (vi) <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = <span class="char-style-override-10">ab</span><span class="char-style-override-19">2</span></p>

      <p class="option-3-aline para-style-override-23">Find which of the binary operations are commutative and which are associative.</p>

      <p class="option-3-aline"><span class="char-style-override-12">10.</span> Find which of the operations given above has identity.</p>

      <p class="option-3-aline"><span class="char-style-override-12">11.</span> <span>Let A =</span> <span class="char-style-override-14">N</span> <span>×</span> <span class="char-style-override-14">N</span> <span>and</span> <span class="char-style-override-11">∗</span> <span>be the binary operation on A defined by</span></p>

      <p class="option-3-aline para-style-override-13"><span>(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>)</span> <span class="char-style-override-11">∗</span> <span>(</span><span class="char-style-override-10">c</span><span>,</span> <span class="char-style-override-10">d</span><span>) = (</span><span class="char-style-override-10">a</span> <span>+</span> <span class="char-style-override-10">c</span><span>,</span> <span class="char-style-override-10">b</span> <span>+</span> <span class="char-style-override-10">d</span><span>)</span></p>

      <p class="option-3-aline"><span>Show that</span> <span class="char-style-override-11">∗</span> <span>is commutative and associative. Find the identity element for</span> <span class="char-style-override-11">∗</span> <span>on A, if any.</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">12.</span> <span>State whether the following statements are true or false. Justify.</span></p>

      <p class="option--i-----iii--align para-style-override-19">(i) For an arbitrary binary operation <span class="char-style-override-11">∗</span> on a set <span class="char-style-override-14">N</span>, <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">a</span> = <span class="char-style-override-10">a</span> <span class="Basic-Graphics-Frame"><img alt="2136.png" class="frame-14" src="../Images/image113.png"/></span><span class="char-style-override-10">a</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">N</span>.</p>

      <p class="option--i-----iii--align para-style-override-19"><span>(ii) If</span> <span class="char-style-override-11">∗</span> is a commutative binary operation on <span class="char-style-override-14">N</span>, then <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> (<span class="char-style-override-10">b</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">c</span>) = (<span class="char-style-override-10">c</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span>) <span class="char-style-override-11">∗</span> <span class="char-style-override-10">a</span></p>

      <p class="option--i-----iii--align para-style-override-17"><span class="char-style-override-12">13.</span> <span>Consider a binary operation</span> <span class="char-style-override-11">∗</span> <span>on</span> <span class="char-style-override-14">N</span> <span>defined as</span> <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> <span>=</span> <span class="char-style-override-10">a</span><span class="char-style-override-19">3</span> <span>+</span> <span class="char-style-override-10">b</span><span class="char-style-override-19">3</span><span>. Choose the correct answer.</span></p>

      <p class="Opt-4-A-D">(A) Is <span class="char-style-override-11">∗</span> both associative and commutative?</p>

      <p class="Opt-2-a-b-c-">(B) Is <span class="char-style-override-11">∗</span> commutative but not associative?</p>

      <p class="Opt-2-a-b-c-">(C) Is <span class="char-style-override-11">∗</span> associative but not commutative?</p>

      <p class="Opt-2-a-b-c-">(D) Is <span class="char-style-override-11">∗</span> neither commutative nor associative?</p>
    </div>

    <p class="micellaneous" style="text-align: center;"><span class="char-style-override-7"><br/></span></p>

    <p class="micellaneous" style="text-align: center;"><span class="char-style-override-7"><br/></span></p>

    <p class="micellaneous" style="text-align: center;"><span class="char-style-override-7">Miscellaneous Examples</span></p>

    <p class="Remarks"><span class="char-style-override-12">Example 41</span> If R<span class="char-style-override-16">1</span> and R<span class="char-style-override-16">2</span> are equivalence relations in a set A, show that R<span class="char-style-override-16">1</span> <span class="char-style-override-11">∩</span> R<span class="char-style-override-16">2</span> is also an equivalence relation.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Sin<span>ce R</span><span class="char-style-override-16">1</span> <span>and R</span><span class="char-style-override-16">2</span> <span>are equivalence relations, (</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">a</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R</span><span class="char-style-override-16">1</span><span>, and (</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">a</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R</span><span class="char-style-override-16">2</span> <span><span class="Basic-Graphics-Frame"><img alt="2141.png" class="frame-14" src="../Images/image114.png"/></span></span><span class="char-style-override-10">a</span> <span class="char-style-override-11">∈</span> <span>A. This implies that (</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">a</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R</span><span class="char-style-override-16">1</span> <span class="char-style-override-11">∩</span> <span>R</span><span class="char-style-override-16">2</span><span>, <span class="Basic-Graphics-Frame"><img alt="2146.png" class="frame-14" src="../Images/image115.png"/></span></span><span class="char-style-override-10">a</span><span>, showing R</span><span class="char-style-override-16">1</span> <span class="char-style-override-11">∩</span> <span>R</span><span class="char-style-override-16">2</span> <span>is reflexive. Further, (</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R</span><span class="char-style-override-16">1</span> <span class="char-style-override-11">∩</span> <span>R</span><span class="char-style-override-16">2</span> <span class="char-style-override-11">⇒</span> <span>(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R</span><span class="char-style-override-16">1</span> <span>and (</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R</span><span class="char-style-override-16">2</span> <span class="char-style-override-11">⇒</span> <span>(</span><span class="char-style-override-10">b</span><span>,</span> <span class="char-style-override-10">a</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R</span><span class="char-style-override-16">1</span> <span>and (</span><span class="char-style-override-10">b</span><span>,</span> <span class="char-style-override-10">a</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R</span><span class="char-style-override-16">2</span> <span class="char-style-override-11">⇒</span> <span>(</span><span class="char-style-override-10">b</span><span>,</span> <span class="char-style-override-10">a</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R</span><span class="char-style-override-16">1</span> <span class="char-style-override-11">∩</span> <span>R</span><span class="char-style-override-16">2</span><span>, hence, R</span><span class="char-style-override-16">1</span> <span class="char-style-override-11">∩</span> <span>R</span><span class="char-style-override-16">2</span> <span>is symmetric. Similarly, (</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">b</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R</span><span class="char-style-override-16">1</span> <span class="char-style-override-11">∩</span> <span>R</span><span class="char-style-override-16">2</span> <span>and</span> <span>(</span><span class="char-style-override-10">b</span><span>,</span> <span class="char-style-override-10">c</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R</span><span class="char-style-override-16">1</span> <span class="char-style-override-11">∩</span> <span>R</span><span class="char-style-override-16">2</span> <span class="char-style-override-11">⇒</span> <span>(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">c</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R</span><span class="char-style-override-16">1</span> <span>and (</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">c</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R</span><span class="char-style-override-16">2</span> <span class="char-style-override-11">⇒</span> <span>(</span><span class="char-style-override-10">a</span><span>,</span> <span class="char-style-override-10">c</span><span>)</span> <span class="char-style-override-11">∈</span> <span>R</span><span class="char-style-override-16">1</span> <span class="char-style-override-11">∩</span> <span>R</span><span class="char-style-override-16">2</span><span>.</span> <span>This shows that R</span><span class="char-style-override-16">1</span> <span class="char-style-override-11">∩</span> <span>R</span><span class="char-style-override-16">2</span> <span>is transitive. Thus, R</span><span class="char-style-override-16">1</span> <span class="char-style-override-11">∩</span> <span>R</span><span class="char-style-override-16">2</span> <span>is an equiva</span>lence relation.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 42</span> <span>Let R be a relation on the set A of ordered pairs of positive integers defined by (</span><span class="char-style-override-10">x</span><span>,</span> <span class="char-style-override-10">y</span><span>) R (</span><span class="char-style-override-10">u</span><span>,</span> <span class="char-style-override-10">v</span><span>) if and only if</span> <span class="char-style-override-10">xv</span> <span>=</span> <span class="char-style-override-10">yu</span><span>. Show that R is an equivalence relation.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> <span>Clearly, (</span><span class="char-style-override-10">x</span><span>,</span> <span class="char-style-override-10">y</span><span>) R (</span><span class="char-style-override-10">x</span><span>,</span> <span class="char-style-override-10">y</span><span>),</span> <img alt="2131" src="../Images/image112.png"/>(<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>) <span class="char-style-override-11">∈</span> A, since <span class="char-style-override-10">xy</span> = <span class="char-style-override-10">yx</span>. This shows that R is reflexive. Further, (<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>) R (<span class="char-style-override-10">u</span>, <span class="char-style-override-10">v</span>) <span class="char-style-override-11">⇒</span> <span class="char-style-override-10">xv</span> = <span class="char-style-override-10">yu</span> <span class="char-style-override-11">⇒</span> <span class="char-style-override-10">uy</span> = <span class="char-style-override-10">vx</span> and hence (<span class="char-style-override-10">u</span>, <span class="char-style-override-10">v</span>) R (<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>). This shows that R is symmetric. Similarly, (<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>) R (<span class="char-style-override-10">u</span>, <span class="char-style-override-10">v</span>) and (<span class="char-style-override-10">u</span>, <span class="char-style-override-10">v</span>) R (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>) <span class="char-style-override-11">⇒</span> <span class="char-style-override-10">xv</span> = <span class="char-style-override-10">yu</span> and <span class="char-style-override-10">ub</span> = <span class="char-style-override-10">va</span> <span class="char-style-override-11">⇒</span><img alt="2151" src="../Images/image116.png"/>⇒<span class="char-style-override-10"><span class="Basic-Graphics-Frame"><img alt="2156.png" class="frame-53" src="../Images/image117.png"/></span></span> <span class="char-style-override-11">⇒</span> <span class="char-style-override-10">xb</span> = <span class="char-style-override-10">ya</span> and hence (<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>) R (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>). Thus, R is transitive. Thus, R is an equivalence relation.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 43</span> <span>Let X = {1, 2, 3, 4, 5, 6, 7, 8, 9}. Let R</span><span class="char-style-override-16">1</span> <span>be a relation in X given by</span> <span>R</span><span class="char-style-override-16">1</span> <span>= {(</span><span class="char-style-override-10">x</span><span>,</span> <span class="char-style-override-10">y</span><span>) :</span> <span class="char-style-override-10">x</span> <span>–</span> <span class="char-style-override-10">y</span> <span>is divisible by 3} and R</span><span class="char-style-override-16">2</span> <span>be another relation on X given by R</span><span class="char-style-override-16">2</span> <span>= {(</span><span class="char-style-override-10">x</span><span>,</span> <span class="char-style-override-10">y</span><span>): {</span><span class="char-style-override-10">x</span><span>,</span> <span class="char-style-override-10">y</span><span>}</span> <span class="char-style-override-11">⊂</span> <span>{1, 4, 7}} or {</span><span class="char-style-override-10">x</span><span>,</span> <span class="char-style-override-10">y</span><span>}</span> <span class="char-style-override-11">⊂</span> <span>{2, 5, 8} or {</span><span class="char-style-override-10">x</span><span>,</span> <span class="char-style-override-10">y</span><span>}</span> <span class="char-style-override-11">⊂</span> <span>{3, 6, 9}}. Show that R</span><span class="char-style-override-16">1</span> <span>= R</span><span class="char-style-override-16">2</span><span>.</span></p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Note that the characteristic of sets {1, 4, 7}, {2, 5, 8} and {3, 6, 9} is that difference between any two elements of these sets is a multiple of 3. Therefore,<br/>
    (<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>) <span class="char-style-override-11">∈</span> R<span class="char-style-override-16">1</span> <span class="char-style-override-11">⇒</span> <span class="char-style-override-10">x</span> – <span class="char-style-override-10">y</span> is a multiple of 3 <span class="char-style-override-11">⇒</span> {<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>} <span class="char-style-override-11">⊂</span> {1, 4, 7} or {<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>} <span class="char-style-override-11">⊂</span> {2, 5, 8} or {<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>} <span class="char-style-override-11">⊂</span> {3, 6, 9} <span class="char-style-override-11">⇒</span> (<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>) <span class="char-style-override-11">∈</span> R<span class="char-style-override-16">2</span>. Hence, R<span class="char-style-override-16">1</span> <span class="char-style-override-11">⊂</span> R<span class="char-style-override-16">2</span>. Similarly, {<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>} <span class="char-style-override-11">∈</span> R<span class="char-style-override-16">2</span> <span class="char-style-override-11">⇒</span> {<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>} <span class="char-style-override-11">⊂</span> {1, 4, 7} or {<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>} <span class="char-style-override-11">⊂</span> {2, 5, 8} or {<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>} <span class="char-style-override-11">⊂</span> {3, 6, 9} <span class="char-style-override-11">⇒</span> <span class="char-style-override-10">x</span> – <span class="char-style-override-10">y</span> is divisible by 3 <span class="char-style-override-11">⇒</span> {<span class="char-style-override-10">x</span>, <span class="char-style-override-10">y</span>} <span class="char-style-override-11">∈</span> R<span class="char-style-override-16">1</span>. This shows that R<span class="char-style-override-16">2</span> <span class="char-style-override-11">⊂</span> R<span class="char-style-override-16">1</span>. Hence, R<span class="char-style-override-16">1</span> = R<span class="char-style-override-16">2</span>.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 44</span> Let <span class="char-style-override-10">f</span> : X <span class="char-style-override-11">→</span> Y be a function. Define a relation R in X given by R = {(<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>): <span class="char-style-override-10">f</span>(<span class="char-style-override-10">a</span>) = <span class="char-style-override-10">f</span>(<span class="char-style-override-10">b</span>)}. Examine whether R is an equivalence relation or not.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> For every <span class="char-style-override-10">a</span> <span class="char-style-override-11">∈</span> X, (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">a</span>) <span class="char-style-override-11">∈</span> R, since <span class="char-style-override-10">f</span>(<span class="char-style-override-10">a</span>) = <span class="char-style-override-10">f</span>(<span class="char-style-override-10">a</span>), showing that R is reflexive. Similarly, (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>) <span class="char-style-override-11">∈</span> R <span class="char-style-override-11">⇒</span> <span class="char-style-override-10">f</span>(<span class="char-style-override-10">a</span>) = <span class="char-style-override-10">f</span>(<span class="char-style-override-10">b</span>) <span class="char-style-override-11">⇒</span> <span class="char-style-override-10">f</span>(<span class="char-style-override-10">b</span>) = <span class="char-style-override-10">f</span>(<span class="char-style-override-10">a</span>) <span class="char-style-override-11">⇒</span> (<span class="char-style-override-10">b</span>, <span class="char-style-override-10">a</span>) <span class="char-style-override-11">∈</span> R. Therefore, R is symmetric. Further, (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>) <span class="char-style-override-11">∈</span> R and (<span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span>) <span class="char-style-override-11">∈</span> R <span class="char-style-override-11">⇒</span> <span class="char-style-override-10">f</span>(<span class="char-style-override-10">a</span>) = <span class="char-style-override-10">f</span>(<span class="char-style-override-10">b</span>) and <span class="char-style-override-10">f</span>(<span class="char-style-override-10">b</span>) = <span class="char-style-override-10">f</span>(<span class="char-style-override-10">c</span>) <span class="char-style-override-11">⇒</span> <span class="char-style-override-10">f</span>(<span class="char-style-override-10">a</span>) = <span class="char-style-override-10">f</span>(<span class="char-style-override-10">c</span>) <span class="char-style-override-11">⇒</span> (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">c</span>) <span class="char-style-override-11">∈</span> R, which implies that R is transitive. Hence, R is an equivalence relation.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 45</span> Determine which of the following binary operations on the set R are associative and which are commutative.</p>

    <p class="Remark-tab">(a) <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = 1<img alt="2172.png" class="frame-14" src="../Images/image120.png"/> <span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span> <span class="char-style-override-11">∈</span> R (b) <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = <img alt="2167.png" class="frame-54" src="../Images/image119.png"/> <span class="Basic-Graphics-Frame"><img alt="2177.png" class="frame-14" src="../Images/image121.png"/></span> <span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span> <span class="char-style-override-11">∈</span> R</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span></p>

    <p class="Remarks para-style-override-17">(a) Clearly, by definition <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = <span class="char-style-override-10">b</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">a</span> = 1, <span class="Basic-Graphics-Frame"><img alt="2182.png" class="frame-14" src="../Images/image122.png"/></span><span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span> <span class="char-style-override-11">∈</span> R. Also<br/>
    (<span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span>) <span class="char-style-override-11">∗</span> <span class="char-style-override-10">c =</span> (1 <span class="char-style-override-11">∗</span> <span class="char-style-override-10">c</span>) =1 and <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> (<span class="char-style-override-10">b</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">c</span>) = <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> (1) = 1, <img alt="2182.png" class="frame-14" src="../Images/image122.png"/> <span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span> <span class="char-style-override-11">∈</span> R. Hence R is both associative and commutative.</p>

    <p class="Body-Text para-style-override-24">(b) <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = <img alt="2187.png" class="frame-55" src="../Images/image123.png"/>= <span class="char-style-override-10">b</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">a</span>, shows that <span class="char-style-override-11">∗</span> is commutative. Further,</p>

    <p class="Body-Text para-style-override-11">(<span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span>) <span class="char-style-override-11">∗</span> <span class="char-style-override-10">c</span> = <span class="Basic-Graphics-Frame"><img alt="2192.png" class="frame-13" src="../Images/image124.png"/></span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">c</span>.<br/></p>

    <p class="Body-Text para-style-override-11">= <img alt="2197.png" class="frame-56" src="../Images/image125.png"/>.</p>

    <p class="Body-Text para-style-override-16">But <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> (<span class="char-style-override-10">b</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">c</span>) =<img alt="2202.png" class="frame-57" src="../Images/image126.png"/></p>

    <p class="Body-Text para-style-override-16">= <img alt="2207.png" class="frame-58" src="../Images/image127.png"/>in general.</p>

    <p class="Body-Text para-style-override-16"><br/></p>

    <p class="Body-Text para-style-override-5">Hence, <span class="char-style-override-11">∗</span> is not associative.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 46</span> Find the number of all one-one functions from set A = {1, 2, 3} to itself.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> One-one function from {1, 2, 3} to itself is simply a permutation on three symbols 1, 2, 3. Therefore, total number of one-one maps from {1, 2, 3} to itself is same as total number of permutations on three symbols 1, 2, 3 which is 3! = 6.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 4</span><span class="char-style-override-12">7</span> Let <span>A = {1, 2, 3}. Then show that the number of relations containing (1, 2)</span> and (2, 3) which are reflexive and transitive but not symmetric is three.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> The smallest relation R<span class="char-style-override-16">1</span> containing (1, 2) and (2, 3) which is reflexive and transitive but not symmetric is {(1, 1), (2, 2), (3, 3), (1, 2), (2, 3), (1, 3)}. Now, if we add the pair (2, 1) to R<span class="char-style-override-16">1</span> to get R<span class="char-style-override-16">2</span>, then the relation R<span class="char-style-override-16">2</span> will be reflexive, transitive but not symmetric. Similarly, we can obtain R<span class="char-style-override-16">3</span> by adding (3, 2) to R<span class="char-style-override-16">1</span> to get the desired relation. However, we can not add two pairs (2, 1), (3, 2) or single pair (3, 1) to R<span class="char-style-override-16">1</span> at a time, as by doing so, we will be forced to add the remaining pair in order to maintain transitivity and in the process, the relation will become symmetric also which is not required. Thus, the total number of desired relations is three.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 48</span> Show that the number of equivalence relation in the set {1, 2, 3} containing (1, 2) and (2, 1) is two.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> The smallest equivalence relation R<span class="char-style-override-16">1</span> containing (1, 2) and (2, 1) is {(1, 1),(2, 2), (3, 3), (1, 2), (2, 1)}. Now we are left with only 4 pairs namely (2, 3), (3, 2),(1, 3) and (3, 1). If we add any one, say (2, 3) to R<span class="char-style-override-16">1</span>, then for symmetry we must add<br/>
    (3, 2) also and now for transitivity we are forced to add (1, 3) and (3, 1). Thus, the only equivalence relation bigger than R<span class="char-style-override-16">1</span> is the universal relation. This shows that the total number of equivalence relations containing (1, 2) and (2, 1) is two.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 49</span> Show that the number of binary operations on {1, 2} having 1 as identity and having 2 as the inverse of 2 is exactly one.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> A binary operation <span class="char-style-override-11">∗</span> on {1, 2} is a function from {1, 2} × {1, 2} to {1, 2}, i.e., a function from {(1, 1), (1, 2), (2, 1), (2, 2)} <span class="char-style-override-11">→</span> {1, 2}. Since 1 is the identity for the desired binary operation <span class="char-style-override-11">∗</span>, <span class="char-style-override-11">∗ (1, 1) = 1, ∗</span> (1, 2) = 2, <span class="char-style-override-11">∗</span> (2, 1) = 2 and the only choice left is for the pair (2, 2). Since 2 is the inverse of 2, i.e., <span class="char-style-override-11">∗</span> (2, 2) must be equal to 1. Thus, the number of desired binary operation is only one.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 50</span> <span>Consider the identity function</span> I<span class="char-style-override-24">N</span> : <span class="char-style-override-14">N</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">N</span> defined as I<span class="char-style-override-24">N</span> (<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span> <span class="Basic-Graphics-Frame"><img alt="2218.png" class="frame-57" src="../Images/image129.png"/></span><span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">N</span>. Show that although I<span class="char-style-override-24">N</span> is onto but I<span class="char-style-override-24">N</span> + I<span class="char-style-override-24">N</span> : <span class="char-style-override-14">N</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">N</span> defined as</p>

    <p class="Remarks para-style-override-13">(I<span class="char-style-override-24">N</span> + I<span class="char-style-override-24">N</span>) (<span class="char-style-override-10">x</span>) = I<span class="char-style-override-24">N</span> (<span class="char-style-override-10">x</span>) + I<span class="char-style-override-24">N</span> (<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span> + <span class="char-style-override-10">x</span> = 2<span class="char-style-override-10">x</span> is not onto.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Clearly I<span class="char-style-override-24">N</span> is onto. But I<span class="char-style-override-24">N</span> + I<span class="char-style-override-24">N</span> is not onto, as we can find an element 3<br/>
    in the co-domain <span class="char-style-override-14">N</span> such that there does not exist any <span class="char-style-override-10">x</span> in the domain <span class="char-style-override-14">N</span> with<br/>
    (I<span class="char-style-override-24">N</span> + I<span class="char-style-override-24">N</span>) (<span class="char-style-override-10">x</span>) = 2<span class="char-style-override-10">x</span> = 3.</p>

    <p class="Remarks"><span class="char-style-override-12">Example 51</span> Consider a function <span class="char-style-override-10">f</span> : <span class="Basic-Graphics-Frame"><img alt="2223.png" class="frame-57" src="../Images/image130.png"/></span>given by <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = sin <span class="char-style-override-10">x</span> and <span class="char-style-override-10"><br/>
    g</span> : <span class="Basic-Graphics-Frame"><img alt="2228.png" class="frame-59" src="../Images/image131.png"/> → R</span> given by <span class="char-style-override-10">g</span>(<span class="char-style-override-10">x</span>) = cos <span class="char-style-override-10">x</span>. Show that <span class="char-style-override-10">f</span> and <span class="char-style-override-10">g</span> are one-one, but <span class="char-style-override-10">f</span> + <span class="char-style-override-10">g</span> is not one-one.</p>

    <p class="Remarks"><span class="char-style-override-12">Solution</span> Since for any two distinct elements <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> and <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span> in <img alt="2228.png" class="frame-59" src="../Images/image131.png"/>, sin <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> <span class="char-style-override-11">≠</span> sin <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span> and cos <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> <span class="char-style-override-11">≠</span> cos <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span>, both <span class="char-style-override-10">f</span> and <span class="char-style-override-10">g</span> must be one-one. But (<span class="char-style-override-10">f</span> + <span class="char-style-override-10">g</span>) (0) = sin 0 + cos 0 = 1 and (<span class="char-style-override-10">f</span> + <span class="char-style-override-10">g</span>)<img alt="2233" src="../Images/image132.png"/>= <span class="Basic-Graphics-Frame"><img alt="2238.png" class="frame-61" src="../Images/image133.png"/></span>. Therefore, <span class="char-style-override-10">f</span> + <span class="char-style-override-10">g</span> is not one-one.</p>

    <p class="Remarks"><br/></p><br/>

    <div class="lining_box">
      <p class="micellaneous" style="text-align: center;"><span class="char-style-override-7">Miscellaneous Exercise on Chapter 1</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">1.</span> Let <span class="char-style-override-10">f</span> <span>:</span> <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> <span>be defined as</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span><span>) = 10</span><span class="char-style-override-10">x</span> <span>+ 7. Find the function</span> <span class="char-style-override-10">g</span> <span>:</span> <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> <span>such that</span> <span class="char-style-override-10">g</span> <span>o</span> <span class="char-style-override-10">f</span> <span>=</span> <span class="char-style-override-10">f</span> <span>o</span> <span class="char-style-override-10">g</span> <span>= 1</span><span class="char-style-override-24">R</span><span>.</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">2.</span> <span>Let</span> <span class="char-style-override-10">f</span> <span>: W</span> <span class="char-style-override-11">→</span> <span>W be defined as</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">n</span><span>) =</span> <span class="char-style-override-10">n</span> <span>– 1, if</span> <span class="char-style-override-10">n</span> <span>is odd and</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">n</span><span>) =</span> <span class="char-style-override-10">n</span> <span>+ 1, if</span> <span class="char-style-override-10">n</span> <span>is even. Show that</span> <span class="char-style-override-10">f</span> <span>is invertible. Find the inverse of</span> <span class="char-style-override-10">f</span><span>. Here, W is the set of all whole num</span>bers.</p>

      <p class="option-3-aline"><span class="char-style-override-12">3.</span> If <span class="char-style-override-10">f</span> : <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> is defined by <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span><span class="char-style-override-19">2</span> – 3<span class="char-style-override-10">x</span> + 2, find <span class="char-style-override-10">f</span> (<span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>)).</p>

      <p class="option-3-aline"><span class="char-style-override-12">4.</span> Show that the function <span class="char-style-override-10">f</span> : <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> {<span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">R</span> : – 1 &lt; <span class="char-style-override-10">x</span> &lt; 1} defined by <img alt="2243" src="../Images/image134.png"/>, <span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">R</span> is one one and onto function.</p>

      <p class="option-3-aline"><span class="char-style-override-12">5.</span> Sh<span>ow that the function</span> <span class="char-style-override-10">f</span> <span>:</span> <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> <span>given by</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span><span>) =</span> <span class="char-style-override-10">x</span><span class="char-style-override-19">3</span> <span>is injective.</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">6.</span> <span>Give examples of two functions</span> <span class="char-style-override-10">f</span> <span>:</span> <span class="char-style-override-14">N</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">Z</span> <span>and</span> <span class="char-style-override-10">g</span> <span>:</span> <span class="char-style-override-14">Z</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">Z</span> <span>such that</span> <span class="char-style-override-10">g</span> <span>o</span> <span class="char-style-override-10">f</span> <span>is injective but</span> <span class="char-style-override-10">g</span> <span>is not injective.</span></p>

      <p class="option-3-aline"><span>(Hint : Consider</span> <span class="char-style-override-10">f</span><span>(</span><span class="char-style-override-10">x</span><span>) =</span> <span class="char-style-override-10">x</span> <span>and</span> <span class="char-style-override-10">g</span><span>(</span><span class="char-style-override-10">x</span><span>) = |</span><span class="char-style-override-10">x</span><span>|).</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">7.</span> <span>Give examples of two functions</span> <span class="char-style-override-10">f</span> <span>:</span> <span class="char-style-override-14">N</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">N</span> <span>and</span> <span class="char-style-override-10">g</span> <span>:</span> <span class="char-style-override-14">N</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">N</span> <span>such that</span> <span class="char-style-override-10">g</span> <span>o</span> <span class="char-style-override-10">f</span> <span>is onto but</span> <span class="char-style-override-10">f</span> <span>is</span> not onto.</p>

      <p class="option-3-aline">(Hint : Consider <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span> + 1 and <img alt="2248" src="../Images/image135.png"/></p>

      <p class="option-3-aline"><span class="char-style-override-12">8.</span> Given a non empty set X, consider P(X) which is the set of all subsets of X. Define the relation R in P(X) as follows:</p>

      <p class="option-3-aline">For subsets A, B in P(X), ARB if and only if A <span class="char-style-override-11">⊂</span> B. Is R an equivalence relation on P(X)? Justify your answer.</p>

      <p class="option-3-aline"><span class="char-style-override-12">9.</span> Given a non-empty set X, consider the binary operation <span class="char-style-override-11">∗</span> : P(X) × P(X) <span class="char-style-override-11">→</span> P(X) given by A <span class="char-style-override-11">∗</span> B = A <span class="char-style-override-11">∩</span> B <span class="Basic-Graphics-Frame"><img alt="2258.png" class="frame-14" src="../Images/image137.png"/></span>A, B in P(X), where P(X) is the power set of X. <span>Show that</span> X is the identity element for this operation and X is the only invertible element in P(X) with respect to the operation <span class="char-style-override-11">∗</span>.</p>

      <p class="option-3-aline"><span class="char-style-override-12">10.</span> Find the number of all onto functions from the set {1, 2, 3, ... , <span class="char-style-override-10">n</span>} to itself.</p>

      <p class="option-3-aline"><span class="char-style-override-12">11.</span> Let S = {<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span>} and T = {1, 2, 3}. Find F<span class="char-style-override-19">–1</span> of the following functions F from S to T, if it exists.</p>

      <p class="option--i-----ii-">(i) F = {(<span class="char-style-override-10">a</span>, 3), (<span class="char-style-override-10">b</span>, 2), (<span class="char-style-override-10">c</span>, 1)} (ii) F = {(<span class="char-style-override-10">a</span>, 2), (<span class="char-style-override-10">b</span>, 1), (<span class="char-style-override-10">c</span>, 1)}</p>

      <p class="option-3-aline"><span class="char-style-override-12">12.</span> Consider the binary operations <span class="char-style-override-11">∗</span> : <span class="char-style-override-14">R</span> × <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> and o : <span class="char-style-override-14">R</span> × <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> defined as <span class="char-style-override-10"><br/>
      a</span> <span class="char-style-override-11">∗</span><span class="char-style-override-10">b</span> = |<span class="char-style-override-10">a</span> – <span class="char-style-override-10">b</span>| and <span class="char-style-override-10">a</span> o <span class="char-style-override-10">b</span> = <span class="char-style-override-10">a</span>, <span class="Basic-Graphics-Frame"><img alt="2263.png" class="frame-14" src="../Images/image138.png"/></span><span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">R</span>. Show that <span class="char-style-override-11">∗</span> is commutative but not <span>associative, o is associative but not commutative. Further, show that</span> <span class="Basic-Graphics-Frame"><img alt="2269.png" class="frame-14" src="../Images/image139.png"/></span><span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span> <span class="char-style-override-11">∈</span> <span class="char-style-override-14">R</span>, <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> (<span class="char-style-override-10">b</span> o <span class="char-style-override-10">c</span>) = (<span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span>) o (<span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">c</span>). [If it is so, we say that the operation <span class="char-style-override-11">∗</span> distributes over the operation o]. Does o distribute over <span class="char-style-override-11">∗</span>? Justify your answer.</p>

      <p class="option-3-aline"><span class="char-style-override-12">13.</span> Given a non-empty set X, let <span class="char-style-override-11">∗</span> : P(X) × P(X) <span class="char-style-override-11">→</span> P(X) be defined as<br/>
      A * B = (A – B) <span class="char-style-override-11">∪</span> (B – A), <span class="Basic-Graphics-Frame"><img alt="2274.png" class="frame-64" src="../Images/image140.png"/></span>A, B <span class="char-style-override-11">∈</span> P(X). Show that the empty set <span class="char-style-override-11">φ</span> is the identity for the operation <span class="char-style-override-11">∗</span> and all the elements A of P(X) are invertible with<br/>
      A<span class="char-style-override-19">–1</span> = A. (Hint : (A – <span class="char-style-override-11">φ</span>) <span class="char-style-override-11">∪</span> (<span class="char-style-override-11">φ</span> – A) = A and (A – A) <span class="char-style-override-11">∪</span> (A – A) = A <span class="char-style-override-11">∗</span> A = <span class="char-style-override-11">φ</span>).</p>

      <p class="option-3-aline para-style-override-11"><span class="char-style-override-12">14.</span> Define a binary operation <span class="char-style-override-11">∗</span> on the set {0, 1, 2, 3, 4, 5} as</p>

      <p class="option-3-aline para-style-override-21"><img alt="2274" src="../Images/image140.png"/><br/></p>

      <p class="option-3-aline">Show that zero is the identity for this operation and each element <span class="char-style-override-10">a</span> <span class="char-style-override-11">≠</span> <span class="char-style-override-10">0</span> of the set is invertible with 6 – <span class="char-style-override-10">a</span> being the inverse of <span class="char-style-override-10">a</span>.</p>

      <p class="option-3-aline"><span class="char-style-override-12">15.</span> Let A = {– 1, 0, 1, 2}, B = {– 4, – 2, 0, 2} and <span class="char-style-override-10">f</span>, <span class="char-style-override-10">g</span> : A <span class="char-style-override-11">→</span> B be functions defined by <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">x</span><span class="char-style-override-19">2</span> – <span class="char-style-override-10">x</span>, <span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> A and <img alt="2279" src="../Images/image141.png"/> <span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> A. Are <span class="char-style-override-10">f</span> and <span class="char-style-override-10">g</span> equal? Justify your answer. (Hint: One may note that two functions <span class="char-style-override-10">f</span> : A <span class="char-style-override-11">→</span> B and</p>

      <p class="option-3-aline"><span class="char-style-override-10">g</span> : A <span class="char-style-override-11">→</span> B such that <span class="char-style-override-10">f</span>(<span class="char-style-override-10">a</span>) = <span class="char-style-override-10">g</span>(<span class="char-style-override-10">a</span>) <span class="char-style-override-10"><img alt="2284.png" class="frame-14" src="../Images/image142.png"/>a</span> <span class="char-style-override-11">∈</span> A, are called equal functions).</p>

      <p class="option-3-aline"><span class="char-style-override-12">16.</span> Let A = {1, 2, 3}. Then number of relations containing (1, 2) and (1, 3) which are reflexive and symmetric but not transitive is</p>

      <p class="Opt-4-A-D">(A) 1 (B) 2 (C) 3 (D) 4</p>

      <p class="option-3-aline"><span class="char-style-override-12">17.</span> Let A = {1, 2, 3}. Then number of equivalence relations containing (1, 2) is</p>

      <p class="Opt-4-A-D">(A) 1 (B) 2 (C) 3 (D) 4</p>

      <p class="option-3-aline"><span class="char-style-override-12">18.</span> Let <span class="char-style-override-10">f</span> : <span class="char-style-override-14">R</span> <span class="char-style-override-11">→</span> <span class="char-style-override-14">R</span> be the Signum Function defined as</p>

      <p class="option-3-aline para-style-override-13"><span class="char-style-override-10"><img alt="2289" src="../Images/image143.png"/></span></p>

      <p class="option-3-aline">and <span class="char-style-override-10">g</span> : R <span class="char-style-override-11">→</span> R be the Greatest Integer Function given by <span class="char-style-override-10">g</span><span>(</span><span class="char-style-override-10">x</span>) = [<span class="char-style-override-10">x</span>], where [<span class="char-style-override-10">x</span>] <span>is greatest integer less than or equal to</span> <span class="char-style-override-10">x</span>. Then, does <span class="char-style-override-10">fog</span> and <span class="char-style-override-10">gof</span> coincide in (0, 1]<span>?</span></p>

      <p class="option-3-aline"><span class="char-style-override-12">19.</span> Number of binary operations on the set {<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>} are</p>

      <p class="Opt-4-A-D para-style-override-11">(A) 10 (B) 16 (C) 20 (D ) 8</p>
    </div>

    <div class="Basic-Graphics-Frame">
      <br/>
    </div><br/>

    <div class="box">
      <p class="micellaneous" style="text-align: center;"><span class="char-style-override-7">Summary</span></p>

      <p class="Body-Text para-style-override-5">In this chapter, we studied different types of relations and equivalence relation, composition of functions, invertible functions and binary operations. The main features of this chapter are as follows:</p>

      <p class="sumarry-tab-1 para-style-override-8"/>

      <ul>
        <li><span class="char-style-override-10">Empty relation</span> is the relation R in X given by R = <span class="char-style-override-11">φ</span> <span class="char-style-override-11">⊂</span> X × X.<br/></li>

        <li><span class="char-style-override-10">Universal relation</span> is the relation R in X given by R = X × X.<br/></li>

        <li><span class="char-style-override-10">Reflexive relation</span> R in X is a relation with (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">a</span>) <span class="char-style-override-11">∈</span> R <span class="Basic-Graphics-Frame"><img alt="2299.png" class="frame-14" src="../Images/image145.png"/></span><span class="char-style-override-10">a</span> <span class="char-style-override-11">∈</span> X.<br/></li>

        <li><span class="char-style-override-10">Symmetric relation</span> R in X is a relation satisfying (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>) <span class="char-style-override-11">∈</span> R implies (<span class="char-style-override-10">b</span>, <span class="char-style-override-10">a</span>) <span class="char-style-override-11">∈</span> R.<br/></li>

        <li><span class="char-style-override-10">Transitive relation</span> R in X is a relation satisfying (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>) <span class="char-style-override-11">∈</span> R and (<span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span>) <span class="char-style-override-11">∈</span> R implies that (<span class="char-style-override-10">a</span>, <span class="char-style-override-10">c</span>) <span class="char-style-override-11">∈</span> R.<br/></li>

        <li><span class="char-style-override-10">Equivalence relation</span> R in X is a relation which is reflexive, symmetric and transitive.<br/></li>

        <li><span class="char-style-override-10">Equivalence class</span> [<span class="char-style-override-10">a</span>] containing <span class="char-style-override-10">a</span> <span class="char-style-override-11">∈</span> X for an equivalence relation R in X is the subset of X containing all elements <span class="char-style-override-10">b</span> related to <span class="char-style-override-10">a</span>.<br/></li>

        <li>A function <span class="char-style-override-10">f</span> : X <span class="char-style-override-11">→</span> Y is <span class="char-style-override-10">one-one</span> (or <span class="char-style-override-10">injective</span>) if<br/></li>

        <li><span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span><span class="char-style-override-16">1</span>) = <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span><span class="char-style-override-16">2</span>) <span class="char-style-override-11">⇒</span> <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span> = <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span> <span class="Basic-Graphics-Frame"><img alt="2304.png" class="frame-14" src="../Images/image146.png"/></span> <span class="char-style-override-10">x</span><span class="char-style-override-16">1</span>, <span class="char-style-override-10">x</span><span class="char-style-override-16">2</span> <span class="char-style-override-11">∈</span> X.<br/></li>

        <li>A function <span class="char-style-override-10">f</span> : X <span class="char-style-override-11">→</span> Y is <span class="char-style-override-10">onto</span> (or <span class="char-style-override-10">surjective</span>) if given any <span class="char-style-override-10">y</span> <span class="char-style-override-11">∈</span> Y, <span class="char-style-override-11">∃</span> <span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> X such that <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">y</span>.<br/></li>

        <li>A function <span class="char-style-override-10">f</span> : X <span class="char-style-override-11">→</span> Y is <span class="char-style-override-10">one-one and onto</span> (or <span class="char-style-override-10">bijective</span>), if <span class="char-style-override-10">f</span> is both one-one<br/></li>

        <li>and onto.<br/></li>

        <li>The <span class="char-style-override-10">composition</span> of functions <span class="char-style-override-10">f</span> : A <span class="char-style-override-11">→</span> B and <span class="char-style-override-10">g</span> : B <span class="char-style-override-11">→</span> C is the function<br/></li>

        <li><span class="char-style-override-10">gof</span> : A <span class="char-style-override-11">→</span> C given by <span class="char-style-override-10">go</span><span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>) = <span class="char-style-override-10">g</span>(<span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>))<span class="Basic-Graphics-Frame"><img alt="2309.png" class="frame-14" src="../Images/image147.png"/></span> <span class="char-style-override-10">x</span> <span class="char-style-override-11">∈</span> A.<br/></li>

        <li>A function <span class="char-style-override-10">f</span> : X <span class="char-style-override-11">→</span> Y is <span class="char-style-override-10">invertible</span> if <span class="char-style-override-11">∃</span> <span class="char-style-override-10">g</span> : Y <span class="char-style-override-11">→</span> X such that <span class="char-style-override-10">gof</span> = I<span class="char-style-override-16">X</span> and<br/></li>

        <li><span class="char-style-override-10">fog</span> = I<span class="char-style-override-16">Y</span>.<br/></li>

        <li>A function <span class="char-style-override-10">f</span> : X <span class="char-style-override-11">→</span> Y is <span class="char-style-override-10">invertible</span> if and only if <span class="char-style-override-10">f</span> is one-one and onto.<br/></li>

        <li>Given a finite set X, a function <span class="char-style-override-10">f</span> : X <span class="char-style-override-11">→</span> X is one-one (respectively onto) if and only if <span class="char-style-override-10">f</span> is onto (respectively one-one). This is the characteristic property of a finite set. This is not true for infinite set<br/></li>

        <li>A <span class="char-style-override-13">binary operation</span> <span class="char-style-override-11">∗</span> on a set A is a function <span class="char-style-override-11">∗</span> from A × A to A.<br/></li>

        <li>An element <span class="char-style-override-10">e</span> <span class="char-style-override-11">∈</span> X is the <span class="char-style-override-13">identity</span> element for binary operation <span class="char-style-override-11">∗</span> : X × X <span class="char-style-override-11">→</span> X, if <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">e</span> = <span class="char-style-override-10">a</span> = <span class="char-style-override-10">e</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">a</span> <span class="Basic-Graphics-Frame"><img alt="2314.png" class="frame-14" src="../Images/image148.png"/></span><span class="char-style-override-10">a</span> <span class="char-style-override-11">∈</span> X.<br/></li>

        <li>An element <span class="char-style-override-10">a</span> <span class="char-style-override-11">∈</span> X is <span class="char-style-override-13">invertible</span> for binary operation <span class="char-style-override-11">∗</span> : X × X <span class="char-style-override-11">→</span> X, if<br/></li>

        <li>there exists <span class="char-style-override-10">b</span> <span class="char-style-override-11">∈</span> X such that <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = <span class="char-style-override-10">e</span> = <span class="char-style-override-10">b</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">a</span> where, <span class="char-style-override-10">e</span> is the identity for the binary operation <span class="char-style-override-11">∗</span>. The element <span class="char-style-override-10">b</span> is called <span class="char-style-override-13">inverse</span> of <span class="char-style-override-10">a</span> and is denoted by <span class="char-style-override-10">a</span><span class="char-style-override-19">–1</span>.<br/></li>

        <li>An operation <span class="char-style-override-11">∗</span> on X is <span class="char-style-override-13">commutative</span> if <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span> = <span class="char-style-override-10">b</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">a</span> <span class="Basic-Graphics-Frame"><img alt="2320.png" class="frame-14" src="../Images/image149.png"/></span><span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span> in X.<br/></li>

        <li>An operation <span class="char-style-override-11">∗</span> on X is <span class="char-style-override-13">associative</span> if (<span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">b</span>) <span class="char-style-override-11">∗</span> <span class="char-style-override-10">c</span> = <span class="char-style-override-10">a</span> <span class="char-style-override-11">∗</span> (<span class="char-style-override-10">b</span> <span class="char-style-override-11">∗</span> <span class="char-style-override-10">c</span>)<span class="char-style-override-10">a</span>, <span class="char-style-override-10">b</span>, <span class="char-style-override-10">c</span> in X.<br/></li>
      </ul>
    </div>

    <p class="micellaneous para-style-override-12"><span class="char-style-override-7"><br/></span></p>

    <div class="box1">
      <p class="micellaneous para-style-override-12" style="text-align: center;"><span class="char-style-override-7">Historical Note</span></p>

      <p class="Historical-Note">The concept of function has evolved over a long period of time starting from R. Descartes (1596-1650), who used the word ‘function’ in his manuscript “<span class="char-style-override-10">Geometrie</span>” in 1637 to mean some positive integral power <span class="char-style-override-10">x</span><span class="char-style-override-27">n</span> of a variable <span class="char-style-override-10">x</span> while studying geometrical curves like hyperbola, parabola and ellipse. James Gregory (1636-1675) in his work “ <span class="char-style-override-10">Vera Circuli et Hyperbolae Quadratura</span>” (1667) considered function as a quantity obtained from other quantities by successive use of algebraic operations or by any other operations. Later G. W. Leibnitz (1646-1716) in his manuscript “<span class="char-style-override-10">Methodus tangentium inversa, seu de functionibus</span>” written in 1673 used the word ‘function’ to mean a quantity varying from point to point on a curve such as the coordinates of a point on the curve, the slope of the curve, the tangent and the normal to the curve at a point. However, in his manuscript “<span class="char-style-override-10">Historia</span>” (1714), Leibnitz used the word ‘function’ to mean quantities that depend on a variable. He was the first to use the phrase ‘function of <span class="char-style-override-10">x</span>’. John Bernoulli (1667-1748) used the notation <span class="char-style-override-11">φ</span><span class="char-style-override-10">x</span> for the first time in 1718 to indicate a function of <span class="char-style-override-10">x</span>. But the general adoption of symbols like <span class="char-style-override-10">f</span>, F, <span class="char-style-override-11">φ</span>, <span class="char-style-override-11">ψ</span> ... to represent functions was made by Leonhard Euler (1707-1783) in 1734 in the first part of his manuscript “<span class="char-style-override-10">Analysis Infinitorium</span>”. Later on, Joeph Louis Lagrange (1736-1813) published his manuscripts “<span class="char-style-override-10">Theorie des functions analytiques</span>” in 1793, where he discussed about analytic function and used the notion <span class="char-style-override-10">f</span>(<span class="char-style-override-10">x</span>), F(<span class="char-style-override-10">x</span>), <span class="char-style-override-11">φ</span>(<span class="char-style-override-10">x</span>) etc. for different function of <span class="char-style-override-10">x</span>. Subsequently, Lejeunne Dirichlet<br/>
      (1805-1859) gave the definition of function which was being used till the set theoretic definition of function presently used, was given after set theory was developed by Georg Cantor (1845-1918). The set theoretic definition of function known to us presently is simply an abstraction of the definition given by Dirichlet in a rigorous manner.</p>
    </div>
  </div>
</body>
</html>
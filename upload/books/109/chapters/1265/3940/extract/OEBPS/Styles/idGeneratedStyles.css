html, body {
font-family:Arial, Helvetica, sans-serif;
}

body {
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}
.box{
background-color:#C6D4EB;
padding: 15px;
font-size:1.0em;
line-height:150%;
position:relative;
}
.box1{
background-color:#477531;
padding: 15px;

line-height:150%;
color:#fff;
font-size:1.5em;

}
.box11{
background-color:#FFE1C1;
padding: 15px;

line-height:150%;
color:black;
font-size:1.5em;

}
.box12{
background-color:#FFF8D0;
padding: 15px;

line-height:150%;
color:black;
font-size:1.5em;

}
.box2{
background-color:#FFF6DF;
padding: 15px;
font-size:1.0em;
line-height:150%;

}
.CharOverride-13
{
color:#000;
font-size:1.2em;
background:#FFDCC0;
padding:10px;
border-radius:10px;
z-index:1000;
position:absolute;
top:-30px;
}
.lining_boxpink
{
background-color:#C0E0F0;
border:4px solid #75BDDF;
border-radius:15px;
padding:15px;
width:60%;
position:relative;
z-index:100;
text-align:center;
margin:auto;
padding-top:30px;
}
.learnt
{
color:#000;
font-size:1.2em;
background:#FFC78B;
padding:10px;
border-radius:10px;
z-index:1000;
position:absolute;
top:-30px;
}
.lining_boxyellow
{
background-color:#EEE7C8;
border-radius:15px;
padding:15px;
position:relative;
z-index:100;
text-align:center;
margin:auto;
padding-top:30px;
}
.Exercise-Head{
color: #00B2B0;
font-size: 1.2em;
}
img
{
max-width:100%;
}
.caption
{
font-style: italic;
font-size: 0.9em;
color: #4D4D4D;
text-align:center;
}
/* Chapter Name */
h1
{
color:#fff;
font-size:1.5em;
background:#477531;
padding:10px;
}
/* Chapter number */
h2
{
color:#23A044;
font-size:1.3em;
}
/* Concept Heading */
h3
{
color:black;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
h4
{
color:black;
font-size:1.1em;
font-weight:bold;
margin-top:40px;
}
/* Sub Heading 2*/
h5
{
color:#CC0000;
font-size:1.1em;
font-weight:bold;
}
.clear
{
clear:both;
}
.Exercise{

color:#00BDF4;
}
.yellow{
background:#FEDC9E;
font-weight: bold;
}
.lining_box
{
background:#FFE6C5;
padding:15px;
border-bottom:4px solid #ADA79E;
border-top:4px solid #ADA79E;
}
.lining_box1
{
background:#AAD6EF;
padding:15px;
border-bottom:2px solid #FB3099;
border-top:2px solid #FB3099;
}
.lining_box3
{
border-bottom:4px solid #ADA79E;
border-top:4px solid #ADA79E;
border-left:20px solid #00B2B0;
padding:15px;
border-radius:5px;
}
.purple
{
color:#fff;
font-size:1.2em;
background:#6F2895;
padding:10px;

z-index:1000;
position:absolute;
top:-30px;
}
.lining_box4
{
border:4px solid #ADA79E;
padding:15px;
position:relative;
z-index:100;
border-radius:5px;
margin:auto;
padding-top:30px;
}
.lining_box2
{
border:2px solid black;
background:#ffffff;
padding:15px;
border-radius:5px;
}
.note
{
font-style: italic;
font-size: 0.83em;
color: #4D4D4D;
}
p
{
margin-top:10px;
}
table
{
width:80%;
border:1px solid #000;
border-collapse:collapse;
text-align: center;
margin:auto;
}
td
{
padding:10px;
border:1px solid #000;
border-collapse:collapse;
}
.cover_img_small
{
width:50%;
}
div.layout

{

text-align: center;

}

div.chapter_pos


{


text-align: center;


width: 96%;


position:absolute;


top:70%;


font-weight:bold;


font-size:28px;


color:#fff;


}


div.chapter_pos div


{


background:#009292;


padding:10px;


width:30%;


margin:auto;

opacity:0.9;


}


div.chapter_pos div span


{


font-size:0.7em;


color:#eaeaea;


font-weight:normal;


}

@media only screen and (max-width: 767px) {


div.chapter_pos


{


font-size:0.8em;

line-height:120%;

top:50%;

}


div.chapter_pos div span


{


font-size:0.7em;


}

}

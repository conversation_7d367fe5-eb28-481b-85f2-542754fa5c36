@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face{
	
	font-family:"Arial, Helvetica, sans-serif";
	font-size:70%;
	}
html, body {
font-family:"Walkman-Chanakya-905";
}

body {
font-size:120%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}

.image {
text-align:center;
}

.chapter {
text-align:left;
font-size:170%;
}
.chapterNO {
text-align:right;
font-size:140%;
}
.space{
	text_align:right;

}


.chapterText {
font-sze:200%;
margin:2% 0;
text-align:left;
}

.topicHeading {
font-sze:170%;
margin:2% 0;
font-weight:bold;
}



.center {
	text-align:center;
	font-weight:bold;
	color: #000;
}
.english
{
font-family:Arial, Helvetica, sans-serif;
font-size:0.8em;
}
.englishMeaning
{
font-family:Arial, Helvetica, sans-serif;
font-size:80%;
font-weight:bold; }
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:50%;
line-height:110%;
font-weight:bold;

font-size:180%;

color:#fff;

}

div.chapter_pos div

{

background:#266A2E;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
h2
{
color:#fff;
font-size:1.5em;
background:#00CED1;
padding:10px;
}

h4
{
color:#d1640f;
font-size:1.3em;
}

.activityBox{
background-color:#EBFAFF;
padding: 15px;
border-radius:15px;
font-size:0.9em;
}

.box{
background-color:#FFE6E6;
padding: 15px;
border-radius:15px;
font-size:0.9em;
}

.activity {
background-color:#FFFFC8;
padding: 15px;
border-radius:15px;
font-size:0.9em;
}

.activitybox2{
background-color:#EBFFEB;
padding: 15px;
border-radius:15px;
font-size:0.9em;
}

.lining_box
{
border:2px solid #00CED1; 
border-radius:15px;
padding:15px;
}
.Sub
{
color:#993300;
font-size:1.3em;
}

.Sub1
{
color:#FF3300;
font-size:1.1em;

}
.Sub2
{
Color:#FF9900;
font-size:1.1em;

}

.exContent{
background-color:#F8DFF8;
padding: 15px;
font-size:1em;
}



.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}



.footer

{

display:none;

}

table td

{

padding:10px;

}



.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:70%;

font-size:1em;
}
div.chapter_pos div
{
width:80%;
}
.cover_img_small
{
width:90%;
}
}

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Helvetica"><span style="font-size:18px"><strong>Permutations and Combinations</strong></span></span></span></p>

<p style="text-align:justify">&nbsp;</p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Helvetica"><span style="font-size:16px"><strong>Fundamental Principle of Counting</strong></span> </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Helvetica">The fundamental principle of counting is a way to figure out the total number of ways in which different events can occur. If a certain work <span class="math-tex">\({A}\)</span> can be done in <span class="math-tex">\({m}\)</span> ways and another work <span class="math-tex">\({B}\)</span> in <span class="math-tex">\({n}\)</span> ways, then<br />
(i) the number of ways of doing the work <span class="math-tex">\({A}\)</span> or <span class="math-tex">\({B}\)</span> is <span class="math-tex">\({m+n}\)</span>. [addition principle]<br />
(ii) the number of ways of doing both the works is <span class="math-tex">\({mn}\)</span>. [multiplication principle] </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Helvetica"><span style="font-size:16px"><strong>Factorial Notation</strong></span> </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Helvetica">Let <span class="math-tex">\({n}\)</span> be a positive integer. Then, the continued product of first <span class="math-tex">\({n}\)</span> natural numbers is called factorial <span class="math-tex">\({n.}\)</span> It is denoted by <span class="math-tex">\({n!}\)</span> or <span class="math-tex">\({ {n}.}\)</span><br />
Thus, <span class="math-tex">\({n!=n(n-1)(n-2)\ldots 3\cdot 2\cdot 1}\)</span> e.g. <span class="math-tex">\({5!=5\times 4\times 3\times 2\times 1=120}\)</span> and <span class="math-tex">\({4!=4\times 3\times 2\times 1=24}\)</span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Helvetica"><span style="font-size:16px"><strong>Properties of Factorial Notation</strong></span><br />
<br />
1. <span class="math-tex">\({0!=1!=1}\)</span><br />
2. Factorials of negative integers and fractions are not defined.<br />
3. <span class="math-tex">\({n!=n(n-1)!=n(n-1)(n-2)!}\)</span><br />
4. <span class="math-tex">\({\displaystyle \frac{n!}{r!}=n(n-1)(n-2)\ldots(r+1)!}\)</span> </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Helvetica"><span style="font-size:16px"><strong>Permutation</strong></span> </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Helvetica">Each of different arrangements which can be made by taking some or all of a number of things, is called a permutation and number of permutations ofn distinct objects taking <span class="math-tex">\({r}\)</span> at a time is denoted by <span class="math-tex">\({n_{P_{r}}.}\)</span> <span class="math-tex">\({n_{P_{r}}=\displaystyle \frac{n!}{(n-r)!}, \forall 0\leq r\leq n=n(n-1)(n-2) (n-r+1), \forall n\in N}\)</span> and <span class="math-tex">\({r\in W}\)</span> </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Helvetica"><span style="font-size:16px"><strong>Properties of <span class="math-tex">\({n_{P_{r}}}\)</span> </strong></span></span></span></p>

<ul>
	<li style="text-align:justify"><span style="color:#4e5f70"><span style="font-family:Helvetica"><span class="math-tex">\({n}\)</span><span class="math-tex">\({n_{P_{n}}=n!}\)</span><span class="math-tex">\({ {}n_{P_{0}}=1, nP_{1}=n}\)</span></span></span></li>
</ul>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex">\({ {}n_{P_{0}}=1, nP_{1}=n}\)</span> and <span class="math-tex">\({n_{P_{n-1}}=n!}\)</span> <span class="math-tex">\({ {}n_{P_{r}}=n^{n-1}P_{r-1}=r^{n-1}P_{r-1}+^{n-1}P_{r}}\)</span> <span class="math-tex">\({ {}n-1P_{r}=(n-r)^{n-1}P_{r-1}}\)</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-size:16px"><strong>Number of Permutations without Repetition </strong></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70">(i) Arranging <span class="math-tex">\({n}\)</span> distinct objects, taken <span class="math-tex">\({r}\)</span> at a time equivalent to filling <span class="math-tex">\({r}\)</span> places from <span class="math-tex">\({n}\)</span> things&nbsp;<br />
Number of arrangements <span class="math-tex">\({=}\)</span>Number of ways of filling <span class="math-tex">\({r}\)</span> places <span class="math-tex">\({ =n(n-1)(n-2)\ (n-r+1) }\)</span> <span class="math-tex">\({ =\frac{n(n-1)(n-2)\ldots(n-r+1)\{(n-r)!\}}{(n-r)!} }\)</span> <span class="math-tex">\({ =\frac{n!}{(n-r)!}=^{n}P_{r} }\)</span><br />
<br />
(ii) The number of arrangements of <span class="math-tex">\({n}\)</span> different objects taken all at a time <span class="math-tex">\({ =^{n}P_{n}=n! }\)</span><br />
(a) <span class="math-tex">\({n_{P_{0}}=\displaystyle \frac{n}{n}!=1, nP_{r}=n^{n-1}P_{r-1}}\)</span><br />
(b) <span class="math-tex">\({0!=1,1/(-r)!=0}\)</span> or <span class="math-tex">\({(-r)!=\infty r\in N]}\)</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-size:16px"><strong>Number of Permutations with Repetition</strong></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex">\({r}\)</span> (i) The number of permutations (arrangements) of <span class="math-tex">\({n}\)</span> different objects, taken <span class="math-tex">\({r}\)</span> at a time, when each object may occur once, twice, thrice upto <span class="math-tex">\({r}\)</span> times in any arrangement is equal to the number of ways of filling <span class="math-tex">\({r}\)</span> places, where each place can be filled by anyone of <span class="math-tex">\({n}\)</span> objects. Number of permutations<span class="math-tex">\({=}\)</span>Number ofways of filling <span class="math-tex">\({r}\)</span> places <span class="math-tex">\({=(n)^{r}}\)</span><br />
(ii) The number of arrangements that can be formed using <span class="math-tex">\({n}\)</span> objects out of which <span class="math-tex">\({p}\)</span> are identical (and of one kind), <span class="math-tex">\({q}\)</span> are identical (and of another kind), <span class="math-tex">\({r}\)</span> are identical (and of another kind) and the rest are distinct, is <span class="math-tex">\({\displaystyle \frac{n!}{p!q!r!}.}\)</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-size:16px"><strong>Circular Permutation</strong></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70">If objects are arranged along a closed curve, then permutation is known as circular permutation.</span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-size:16px"><strong>Important Results on Circular Permutation </strong></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70">(i) The number of circular permutations of <span class="math-tex">\({n}\)</span> different things taken all at a time is <span class="math-tex">\({(n-1)!}\)</span>. If clockwise and anti-clockwise orders are taken as different.<br />
(ii) If clockwise and anti-clockwise circular permutations are considered to be the same, then it is <span class="math-tex">\({\displaystyle \frac{(n-1)!}{2}.}\)</span><br />
(iii) Number of circular permutations of <span class="math-tex">\({n}\)</span> different things, taken <span class="math-tex">\({r}\)</span> at a time, when clockwise and anti-clockwise orders are taken as different, is <span class="math-tex">\({\displaystyle \frac{n_{P_{r}}}{r}.}\)</span><br />
(iv) Number of circular permutations of <span class="math-tex">\({n}\)</span> different things, taken <span class="math-tex">\({r}\)</span> at a time, when clockwise and anti-clockwise orders are not different, is <span class="math-tex">\({n_{P_{r}}/2r.}\)</span><br />
(v) Number of circular permutations <span class="math-tex">\({ {o} {f}n}\)</span> things, when <span class="math-tex">\({p}\)</span> are alike and the rest different, taken all at a time distinguishing clockwise and anti-clockwise arrangements is <span class="math-tex">\({\displaystyle \frac{(n-1)!}{p!}.}\)</span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-size:16px"><strong>Combination</strong></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70">Each of the different groups or selections which can be formed by taking some or all of the number of objects, irrespective oftheir arrangements, is called a combination. If <span class="math-tex">\({n_{C_{r}}}\)</span> denotes the number of combinations of <span class="math-tex">\({n}\)</span> different things taken <span class="math-tex">\({r}\)</span> at a time, then <span class="math-tex">\({n_{C_{r}}=\displaystyle \frac{n!}{r!(n-r)!}=\frac{n_{P_{r}}}{r!}}\)</span>, where <span class="math-tex">r&lt;n and <span class="math-tex">\({r\in W.}\)</span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex"><span style="font-size:16px"><strong>Properties of <span class="math-tex">\({n_{C_{\Gamma}}}\)</span> <span class="math-tex">\({ {G}n_{C_{r}=^{n}C_{n-r}} {G}n_{C_{r}+^{n}C_{r-1}=^{n+1}C_{r}} {G}n_{C_{r}}=0}\)</span></strong></span>, if <span class="math-tex">\({r\not\in\{ {O},\ 1,2,3,\ n\} {G}n_{P_{r}}=n_{C_{r}\cdot r!}}\)</span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex"><span style="font-size:16px"><strong>Number of Combinations with Repetition</strong></span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex">(i) Number of ways in which atleast one object is selected out ofn distinct objects <span class="math-tex">\({\Rightarrow}\)</span> <span class="math-tex">\({ n_{C_{1}}+n_{C_{2}}+n_{C_{3}}+\ +n_{C_{n}}=2^{n}-1 }\)</span><br />
(ii) The total number of ways in which it is possible to make groups by taking some or all out of <span class="math-tex">\({n=(n_{1}+n_{2}+}\)</span> things, when <span class="math-tex">\({n_{1}}\)</span> are alike (of one kind), <span class="math-tex">\({n_{2}}\)</span> are alike (of second kind) and so on, is <span class="math-tex">\({ \{(n_{1}+1)(n_{2}+1)\ \}-1. }\)</span><br />
(iii) The number of selections of <span class="math-tex">\({r}\)</span> objects out of <span class="math-tex">\({n}\)</span> identical objects is 1.<br />
(iv) Total number of selections of zero or more objects from <span class="math-tex">\({n}\)</span> identical objects is <span class="math-tex">\({n+1.}\)</span><br />
(v) The number of selections taking atleast one out of <span class="math-tex">\({a_{1}+a_{2}+a_{3}+ +a_{n}+k}\)</span> objects, where <span class="math-tex">\({a_{1}}\)</span> are alike (of one kind), <span class="math-tex">\({a_{2}}\)</span> are alike (of second kind) and so on <span class="math-tex">\({a_{n}}\)</span> are alike (ofnth kind) and <span class="math-tex">\({k}\)</span> is distinct, is <span class="math-tex">\({ [(a_{1}+1)(a_{2}+1)(a_{3}+1)\ (a_{n}+1)]2^{k}-1. }\)</span><br />
(vi) If <span class="math-tex">\({ N=p^{a_{1}}\cdot q^{a_{2}}\cdot r^{a_{3}}\ldots}\)</span>, where <span class="math-tex">\({p, q, r}\)</span>, are different prime numbers and <span class="math-tex">\({a_{1}, a_{2}, a_{3}}\)</span>, are natural numbers, then<br />
(a) Total number of divisors of <span class="math-tex">\({N}\)</span> (including 1 and <span class="math-tex">\({N}\)</span>) <span class="math-tex">\({ =(a_{1}+1)(a_{2}+1)(a_{3}+1) }\)</span><br />
(b) Total number of divisors of <span class="math-tex">\({N}\)</span> (excluding 1 and <span class="math-tex">\({N}\)</span>) <span class="math-tex">\({ =\{(a_{1}+1)(a_{2}+1)(a_{3}+1)\ -2 }\)</span><br />
(c) Total number of divisors of <span class="math-tex">\({N}\)</span> (excluding 1 or <span class="math-tex">\({N}\)</span>) <span class="math-tex">\({ =\{(a_{1}+1)(a_{2}+1)(a_{3}+1)\ -1 }\)</span><br />
(d) The sum of these divisors is <span class="math-tex">\({ =\ (p^{0}+p^{1}+p^{2}+\ +p^{a_{1}})(q^{0}+q^{1}+q^{2}+\ldots+q^{a_{2}}) }\)</span> <span class="math-tex">\({ (r^{0}+r^{1}+r^{2}+\ +r^{a_{3}}) }\)</span><br />
(e) The number of ways in which a composite number <span class="math-tex">\({N}\)</span> can be decomposed into two factors is<br />
<br />
<strong>Case I</strong> When <span class="math-tex">\({N}\)</span> is not a perfect square, then <span class="math-tex">\({ \frac{1}{2}\{(a_{1}+1)(a_{2}+1)\ \} }\)</span><br />
<strong>Case II</strong> When <span class="math-tex">\({N}\)</span> is a perfect square, then <span class="math-tex">\({ \frac{1}{2}[\{(a_{1}+1)(a_{2}+1)(a_{3}+1)\ +1] }\)</span><br />
(f) The number of ways in which a composite number <span class="math-tex">\({N}\)</span> can be decomposed in two-two factors which are coprime, is <span class="math-tex">\({2^{k-1}}\)</span>, where <span class="math-tex">\({k}\)</span> is the number of different factors of <span class="math-tex">\({N.}\)</span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex"><span style="font-size:16px"><strong>Applications of Permutations and Combinations </strong></span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span style="font-size:12px"><span style="font-family:Arial,Helvetica,Tahoma,Verdana,sans-serif">The functional and the geometrical applications of permutations and combinations are as given below:</span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex"><span style="font-size:14px"><strong>Functional Applications</strong></span><br />
1. The number of all permutations (arrangements) of <span class="math-tex">\({n}\)</span> different objects taken <span class="math-tex">\({r}\)</span> at a time,<br />
(i) when a particular object is always included in each arrangement, is <span class="math-tex">\({n-1C_{r-1}\times r!.}\)</span><br />
(ii) when a particular object is never taken in each arrangement, is <span class="math-tex">\({n-1C_{r}\times r!.}\)</span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex">2. If the sets <span class="math-tex">\({A}\)</span> has <span class="math-tex">\({m}\)</span> elements and <span class="math-tex">\({B}\)</span> has <span class="math-tex">\({n}\)</span> elements, then<br />
(i) the number of functions from <span class="math-tex">\({A}\)</span> to <span class="math-tex">\({B}\)</span> is <span class="math-tex">\({n^{m}.}\)</span><br />
(ii) the number of one-one functions from <span class="math-tex">\({A}\)</span> to <span class="math-tex">\({B}\)</span> is <span class="math-tex">\({n_{P_{m}}}\)</span>, where <span class="math-tex">\({m\leq n.}\)</span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex"><span style="font-size:14px"><strong>Geometrical Applications</strong></span> </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex">(i) Number of triangles formed from <span class="math-tex">\({n}\)</span> points, when no three points are collinear, is <span class="math-tex">\({n_{C_{3}}.}\)</span><br />
(ii) Out of <span class="math-tex">\({n}\)</span> non-concurrent and non-parallel straight lines, the points of intersection are <span class="math-tex">\({n_{C_{2}}.}\)</span><br />
(iii) Number of parallelograms in two systems of parallel lines (when Ist system contains <span class="math-tex">\({m}\)</span> parallel lines and IInd system contains <span class="math-tex">\({n}\)</span> parallel lines) <span class="math-tex">\({=n_{C_{2}}\times m_{C_{2}}.}\)</span><br />
(iv) The number of diagonals in a polygon of <span class="math-tex">\({n}\)</span> sides, is <span class="math-tex">\({ n_{C_{2}-n}. }\)</span><br />
(v) The number of total triangles formed byjoining the <span class="math-tex">\({n}\)</span> points on a plane of which <span class="math-tex">\({m}\)</span> are collinear, is <span class="math-tex">\({ n_{C_{3}-}m_{C_{3}}. }\)</span><br />
(vi) The number of total different straight lines formed by joining the <span class="math-tex">\({n}\)</span> points on a plane of which <span class="math-tex">\({m}\)</span> are collinear, is <span class="math-tex">\({n_{C_{2}}-m_{C_{2}}+1.}\)</span><br />
(vii) Thenumber of rectangles of any size in a square of <span class="math-tex">\({n\displaystyle \times n {i} {s}\sum_{r=1}^{n}r^{3}}\)</span> and number of squares of any size is <span class="math-tex">\({\displaystyle \sum_{r=1}^{n}r^{2}.}\)</span> </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex"><span style="font-size:14px"><strong>Prime Factors</strong></span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex">Any integer greater than 1 can be expressed as product of primes e.g. <span class="math-tex">\({7=7^{1},12=2^{2}\cdot 3^{1},360=2^{3}\cdot 3^{2}\cdot 5^{1}}\)</span> Let <span class="math-tex">\({ n=p_{1}^{\alpha_{1}}\cdot p_{2}^{\alpha_{2}}\cdot p_{3}^{\alpha_{3}}\cdot \cdot p_{r}^{\alpha_{ {r}}}}\)</span>, where <span class="math-tex">\({p_{j}, i=1,2, \ldots,r}\)</span> are distinct primes and <span class="math-tex">\({\alpha_{i}, i=1,2, \ldots,r}\)</span> are positive integers.&nbsp;<br />
(i) Number of divisors <span class="math-tex">\({ {o} {f}n}\)</span> is <span class="math-tex">\({ (\alpha_{1}+1)(\alpha_{2}+1)(\alpha_{3}+1)\ldots(\alpha_{r}+1) }\)</span><br />
(ii) Sum of divisors of <span class="math-tex">\({n}\)</span> is <span class="math-tex">\({ \frac{(p_{1}^{\alpha_{1}+1}-1)}{(p_{1}-1)}\frac{(p_{2}^{\alpha_{2}+1}-1)}{(p_{2}-1)}\ldots\frac{(p_{r}^{\alpha_{r}+1}-1)}{(p_{r}-1)} }\)</span><br />
(iii) If <span class="math-tex">\({p}\)</span> is a prime and <span class="math-tex">\({p^{r}}\)</span> divides, then <span class="math-tex">\({ r=[\displaystyle \frac{n}{p}]+[\frac{n}{p^{2}}]+\ldots}\)</span> </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex"><span style="font-size:14px"><strong>Division of Objects into Groups</strong></span> </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex">The division of objects into groups are taken place as when the objects are different and identical as given below: When Objects are Different </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex">1. The number of ways of dividing <span class="math-tex">\({n}\)</span> different objects into 3 groups of <span class="math-tex">\({p,q}\)</span> and <span class="math-tex">\({r(p+q+r=n)}\)</span> is<br />
(i) <span class="math-tex">\({\displaystyle \frac{n!}{p!q!r!};p, q}\)</span> and <span class="math-tex">\({r}\)</span> are unequal.<br />
(ii) <span class="math-tex">\({\displaystyle \frac{n!}{p!2!(q!)^{2}}}\)</span> ; <span class="math-tex">\({q=r(\displaystyle {i} {i} {i})\frac{n!}{3!(p!)^{3}}}\)</span> ; <span class="math-tex">\({p=q=r}\)</span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex">2. The number of ways of dividing <span class="math-tex">\({n}\)</span> different objects into <span class="math-tex">\({r}\)</span> groups is <span class="math-tex">\({ \frac{1}{r!}[r^{n}-\left(\begin{array}{l} r\\ 1 \end{array}\right)(r-1)^{n}+\left(\begin{array}{l} r\\ 2 \end{array}\right)(r-2)^{n}-\left(\begin{array}{l} r\\ 3 \end{array}\right)(r-3)^{n}+ }\)</span> </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex">3. The number of ways of dividing <span class="math-tex">\({n}\)</span> different objects into <span class="math-tex">\({r}\)</span> groups taking into account the order of the groups and also the order of objects in each group, is <span class="math-tex">\({ (n+r-1)P_{n}=r(r+1)(r+2)\ldots(r+n-1)\ . }\)</span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex"><span style="font-size:14px"><strong>When Objects are Identical </strong></span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex">1. The number of ways of dividing <span class="math-tex">\({n}\)</span> identical objects among <span class="math-tex">\({r}\)</span> persons, such that each gets 1,2,3, objects, is the coefficient of <span class="math-tex">\({x^{n-r}}\)</span> in the expansion of <span class="math-tex">\({ (1+x+x^{2}+\ +x^{k-1})^{r}. }\)</span> </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex">2. The number of ways of dividing <span class="math-tex">\({n}\)</span> identical objects among <span class="math-tex">\({r}\)</span> persons such that each one may get atmost <span class="math-tex">\({n}\)</span> objects, is <span class="math-tex">\({\left(\begin{array}{l} -1n+r\\ -1r \end{array}\right).}\)</span> In other words, the total number of ways of dividing <span class="math-tex">\({n}\)</span> identical objects into <span class="math-tex">\({r}\)</span> groups, if blank groups are allowed, is <span class="math-tex">\({n+r-1C_{r-1}.}\)</span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex">3. The total number of ways of dividing <span class="math-tex">\({n}\)</span> identical objects among <span class="math-tex">\({r}\)</span> persons and each one ofthem, receives atleast one item, is <span class="math-tex">\({n-1C_{r-1}.}\)</span> In other words, the number of ways in which <span class="math-tex">\({n}\)</span> identical things can be divided into <span class="math-tex">\({r}\)</span> groups such that blank groups are not allowed, is <span class="math-tex">\({n-1C_{r-1}.}\)</span><br />
<br />
4. The total number of selections of some or all out of <span class="math-tex">\({p+q+r}\)</span> items, where <span class="math-tex">\({p}\)</span> are alike of one kind, <span class="math-tex">\({q}\)</span> are alike of second kind and rest are alike of third kind, is <span class="math-tex">\({[(p+1)\cdot(q+1)\cdot(r+1)-1].}\)</span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex"><span style="font-size:14px"><strong>Restricted Selection/Arrangement </strong></span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex">1. The number of ways in which <span class="math-tex">\({r}\)</span> objects can be selected from <span class="math-tex">\({n}\)</span> different objects, if <span class="math-tex">\({k}\)</span> particular objects are<br />
(a) always included <span class="math-tex">\({=n-kC_{r-k}}\)</span><br />
(b) never included <span class="math-tex">\({=n-kC_{r}}\)</span> </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex">2. The number of arrangements of <span class="math-tex">\({n}\)</span> distinct objects taken <span class="math-tex">\({r}\)</span> at a time, so that <span class="math-tex">\({k}\)</span> particular objects are<br />
(a) always included <span class="math-tex">\({=n-kC_{r-k}\cdot r!}\)</span><br />
(b) never included <span class="math-tex">\({=n-kC_{r}\cdot r!}\)</span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex"><span style="font-size:16px"><strong>Dearrangement </strong></span></span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex">Any change in the given order of the things, is called a dearrangement. </span></span></p>

<p style="text-align:justify"><span style="color:#4e5f70"><span class="math-tex">1. If <span class="math-tex">\({n}\)</span> things form an arrangement in a row, then the number of ways in which they can be dearranged, so that none of them occupies its original place, is <span class="math-tex">\({ n!\ (1-\frac{1}{1!}+\frac{1}{2!}-\frac{1}{3!}+\ +(-1)^{n}\cdot\frac{1}{n!})\ . }\)</span><br />
2. Ifn things are arranged at <span class="math-tex">\({n}\)</span> places, then the number of ways to rearrange exactly <span class="math-tex">\({r}\)</span> things at right places, is <span class="math-tex">\({ \frac{n!}{r!}[1-\frac{1}{1!}+\frac{1}{2!}-\frac{1}{3!}+\frac{1}{4!}+\ +(-1)^{n-r}\frac{1}{(n-r)!}] }\)</span> </span></span></p>

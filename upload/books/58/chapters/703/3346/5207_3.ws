<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title>Cover</title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000001.jpg');"> </div>
    <div class="pos fs0" style="left: 201px; top: 205px; color: #231F20;">3</div>
    <div class="pos fs1" style="left: 299px; top: 191px; color: #231F20;">Give and Take</div>
    <div class="pos fs2" style="left: 161px; top: 292px; color: #231F20;">I am Kittu. This is my home. Isn't it huge? It has 100 rooms.</div>
    <div class="pos fs2" style="left: 161px; top: 330px; color: #231F20;">Help me in painting some of the rooms.</div>
    <div class="pos fit fs2" style="left: 207px; top: 1093px; width: 782px; color: #231F20;">
      <span class="just">I start from room 2. I add 10 to 2 to reach room 12 and paint</span>
    </div>
    <div class="pos fit fs2" style="left: 207px; top: 1134px; width: 782px; color: #231F20;">
      <span class="just">it. To add 10 to 2, we can go all the way to the right to 10.</span>
    </div>
    <div class="pos fs2" style="left: 207px; top: 1172px; color: #231F20;">Then up to 11, and one step right to 12.</div>
    <div class="pos fs2" style="left: 207px; top: 1210px; color: #231F20;">This is one way to go from 2 to 12.</div>
    <div class="pos fs2" style="left: 207px; top: 1249px; color: #231F20;">Is there a shortcut? Of course! Follow me.</div>
    <div class="pos fs2" style="left: 207px; top: 1287px; color: #231F20;">We can jump up one row.</div>
    <div class="pos fs2" style="left: 207px; top: 1325px; color: #231F20;">A jump from 2 to 12 is like taking</div>
    <div class="pos fs2" style="left: 763px; top: 1325px; color: #231F20;">steps.</div>
    <div class="pos fs3" style="left: 564px; top: 1429px; color: #231F20;">29</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000002.jpg');"> </div>
    <div class="pos fs2" style="left: 265px; top: 180px; color: #231F20;">Now try one jump up from 14.</div>
    <div class="pos fs2" style="left: 265px; top: 229px; color: #231F20;" id="w1x">14</div>
    <div class="pos fs2" style="left: 307px; top: 229px; color: #231F20;" id="w2x">+</div>
    <div class="pos fs2" style="left: 332px; top: 229px; color: #231F20;" id="w3x">10</div>
    <div class="pos fs2" style="left: 374px; top: 229px; color: #231F20;" id="w4x">=</div>
    <div class="pos fs2" style="left: 417px; top: 229px; color: #231F20;" id="w5x">24</div>
    <div class="pos fs2" style="left: 478px; top: 229px; color: #FFFFFF;" id="w6x">.</div>
    <div class="pos fs2" style="left: 266px; top: 272px; color: #231F20;">Colour this room.</div>
    <div class="pos fs2" style="left: 265px; top: 332px; color: #231F20;">How will I go from 22 to 41? Jump from</div>
    <div class="pos fs2" style="left: 265px; top: 381px; color: #231F20;">22 to 42.</div>
    <div class="pos fs2" style="left: 265px; top: 424px; color: #231F20;">Then one step left. We can write it like this.</div>
    <div class="pos fit fs2" style="left: 265px; top: 468px; width: 170px; color: #231F20;">
      <span class="just">22 + 20 = 42</span>
    </div>
    <div class="pos fs2" style="left: 265px; top: 512px; color: #231F20;" id="w7x">42</div>
    <div class="pos fs2" style="left: 307px; top: 512px; color: #231F20;" id="w8x">–</div>
    <div class="pos fs2" style="left: 339px; top: 512px; color: #231F20;" id="w9x">1</div>
    <div class="pos fs2" style="left: 374px; top: 512px; color: #231F20;" id="w10x">=</div>
    <div class="pos fs2" style="left: 400px; top: 512px; color: #231F20;" id="w11x">41</div>
    <div class="pos fs2" style="left: 265px; top: 555px; color: #231F20;">How many steps did I go in all?</div>
    <div class="pos fs2" style="left: 266px; top: 633px; color: #231F20;">You could also go this way:</div>
    <div class="pos fs2" style="left: 266px; top: 676px; color: #231F20;">From 22 take one step left to 21.</div>
    <div class="pos fs2" style="left: 266px; top: 720px; color: #231F20;">Then two jumps up to 41.</div>
    <div class="pos fs2" style="left: 266px; top: 764px; color: #231F20;" id="w12x">22</div>
    <div class="pos fs2" style="left: 308px; top: 764px; color: #231F20;" id="w13x">–</div>
    <div class="pos fs2" style="left: 340px; top: 764px; color: #231F20;" id="w14x">1</div>
    <div class="pos fs2" style="left: 376px; top: 764px; color: #231F20;" id="w15x">=</div>
    <div class="pos fs2" style="left: 401px; top: 764px; color: #231F20;" id="w16x">21</div>
    <div class="pos fit fs2" style="left: 266px; top: 807px; width: 170px; color: #231F20;">
      <span class="just">21 + 20 = 41</span>
    </div>
    <div class="pos fs4" style="left: 219px; top: 870px; color: #231F20;">Try these on Kittu's home:</div>
    <div class="pos fs2" style="left: 266px; top: 928px; color: #231F20;" id="w17x">a)</div>
    <div class="pos fs2" style="left: 326px; top: 928px; color: #231F20;" id="w18x">10</div>
    <div class="pos fs2" style="left: 370px; top: 928px; color: #231F20;" id="w19x">less</div>
    <div class="pos fs2" style="left: 430px; top: 928px; color: #231F20;" id="w20x">than</div>
    <div class="pos fs2" style="left: 501px; top: 928px; color: #231F20;" id="w21x">34</div>
    <div class="pos fs2" style="left: 544px; top: 928px; color: #231F20;" id="w22x">is</div>
    <div class="pos fs2" style="left: 266px; top: 983px; color: #231F20;" id="w23x">b)</div>
    <div class="pos fs2" style="left: 326px; top: 983px; color: #231F20;" id="w24x">53</div>
    <div class="pos fs2" style="left: 369px; top: 983px; color: #231F20;" id="w25x">–</div>
    <div class="pos fs2" style="left: 392px; top: 983px; color: #231F20;" id="w26x">20</div>
    <div class="pos fs2" style="left: 434px; top: 983px; color: #231F20;" id="w27x">=</div>
    <div class="pos fs2" style="left: 266px; top: 1038px; color: #231F20;" id="w28x">c)</div>
    <div class="pos fs2" style="left: 326px; top: 1038px; color: #231F20;" id="w29x">11</div>
    <div class="pos fs2" style="left: 370px; top: 1038px; color: #231F20;" id="w30x">more</div>
    <div class="pos fs2" style="left: 445px; top: 1038px; color: #231F20;" id="w31x">than</div>
    <div class="pos fs2" style="left: 517px; top: 1038px; color: #231F20;" id="w32x">31</div>
    <div class="pos fs2" style="left: 560px; top: 1038px; color: #231F20;" id="w33x">is</div>
    <div class="pos fs2" style="left: 266px; top: 1094px; color: #231F20;" id="w34x">d)</div>
    <div class="pos fs2" style="left: 326px; top: 1094px; color: #231F20;" id="w35x">11</div>
    <div class="pos fs2" style="left: 369px; top: 1094px; color: #231F20;" id="w36x">less</div>
    <div class="pos fs2" style="left: 429px; top: 1094px; color: #231F20;" id="w37x">than</div>
    <div class="pos fs2" style="left: 500px; top: 1094px; color: #231F20;" id="w38x">66</div>
    <div class="pos fs2" style="left: 553px; top: 1094px; color: #231F20;" id="w39x">is</div>
    <div class="pos fs2" style="left: 266px; top: 1149px; color: #231F20;" id="w40x">e)</div>
    <div class="pos fs2" style="left: 326px; top: 1149px; color: #231F20;" id="w41x">62</div>
    <div class="pos fs2" style="left: 368px; top: 1149px; color: #231F20;" id="w42x">+</div>
    <div class="pos fs2" style="left: 394px; top: 1149px; color: #231F20;" id="w43x">13</div>
    <div class="pos fs2" style="left: 436px; top: 1149px; color: #231F20;" id="w44x">=</div>
    <div class="pos fs2" style="left: 266px; top: 1204px; color: #231F20;" id="w45x">f)</div>
    <div class="pos fs2" style="left: 326px; top: 1204px; color: #231F20;" id="w46x">23</div>
    <div class="pos fs2" style="left: 370px; top: 1204px; color: #231F20;" id="w47x">less</div>
    <div class="pos fs2" style="left: 430px; top: 1204px; color: #231F20;" id="w48x">than</div>
    <div class="pos fs2" style="left: 501px; top: 1204px; color: #231F20;" id="w49x">89</div>
    <div class="pos fs2" style="left: 544px; top: 1204px; color: #231F20;" id="w50x">is</div>
    <div class="pos fs2" style="left: 266px; top: 1259px; color: #231F20;" id="w51x">g)</div>
    <div class="pos fs2" style="left: 326px; top: 1259px; color: #231F20;" id="w52x">10</div>
    <div class="pos fs2" style="left: 369px; top: 1259px; color: #231F20;" id="w53x">and</div>
    <div class="pos fs2" style="left: 429px; top: 1259px; color: #231F20;" id="w54x">40</div>
    <div class="pos fs2" style="left: 473px; top: 1259px; color: #231F20;" id="w55x">more</div>
    <div class="pos fs2" style="left: 549px; top: 1259px; color: #231F20;" id="w56x">is</div>
    <div class="pos fs2" style="left: 712px; top: 1204px; color: #231F20;">.</div>
    <div class="pos fs2" style="left: 716px; top: 1259px; color: #231F20;">.</div>
    <div class="pos fs5" style="left: 242px; top: 1305px; color: #231F20;">The 10×10 number grid is a useful aid for adding and subtracting two-digit numbers. Children</div>
    <div class="pos fs5" style="left: 242px; top: 1332px; color: #231F20;">should be encouraged to try these operations mentally using the grid as often as possible.</div>
    <div class="pos fs2" style="left: 712px; top: 928px; color: #231F20;">.</div>
    <div class="pos fs2" style="left: 727px; top: 1038px; color: #231F20;">.</div>
    <div class="pos fs2" style="left: 720px; top: 1094px; color: #231F20;">.</div>
    <div class="pos fs3" style="left: 621px; top: 1430px; color: #231F20;">30</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000003.jpg');"> </div>
    <div class="pos fs2" style="left: 208px; top: 165px; color: #231F20;" id="w1x">h)</div>
    <div class="pos fs2" style="left: 269px; top: 165px; color: #231F20;" id="w2x">9</div>
    <div class="pos fs2" style="left: 295px; top: 165px; color: #231F20;" id="w3x">added</div>
    <div class="pos fs2" style="left: 385px; top: 165px; color: #231F20;" id="w4x">to</div>
    <div class="pos fs2" style="left: 420px; top: 165px; color: #231F20;" id="w5x">28</div>
    <div class="pos fs2" style="left: 463px; top: 165px; color: #231F20;" id="w6x">gives</div>
    <div class="pos fs2" style="left: 208px; top: 220px; color: #231F20;" id="w7x">i)</div>
    <div class="pos fs2" style="left: 269px; top: 220px; color: #231F20;" id="w8x">The</div>
    <div class="pos fs2" style="left: 327px; top: 220px; color: #231F20;" id="w9x">sum</div>
    <div class="pos fs2" style="left: 394px; top: 220px; color: #231F20;" id="w10x">of</div>
    <div class="pos fs2" style="left: 428px; top: 220px; color: #231F20;" id="w11x">9</div>
    <div class="pos fs2" style="left: 454px; top: 220px; color: #231F20;" id="w12x">and</div>
    <div class="pos fs2" style="left: 514px; top: 220px; color: #231F20;" id="w13x">44</div>
    <div class="pos fs2" style="left: 557px; top: 220px; color: #231F20;" id="w14x">is</div>
    <div class="pos fs2" style="left: 208px; top: 275px; color: #231F20;" id="w15x">j)</div>
    <div class="pos fs2" style="left: 269px; top: 275px; color: #231F20;" id="w16x">Reducing</div>
    <div class="pos fs2" style="left: 401px; top: 275px; color: #231F20;" id="w17x">98</div>
    <div class="pos fs2" style="left: 445px; top: 275px; color: #231F20;" id="w18x">by</div>
    <div class="pos fs2" style="left: 486px; top: 275px; color: #231F20;" id="w19x">34</div>
    <div class="pos fs2" style="left: 530px; top: 275px; color: #231F20;" id="w20x">gives</div>
    <div class="pos fs2" style="left: 208px; top: 330px; color: #231F20;" id="w21x">k)</div>
    <div class="pos fs2" style="left: 269px; top: 330px; color: #231F20;" id="w22x">4</div>
    <div class="pos fs2" style="left: 295px; top: 330px; color: #231F20;" id="w23x">and</div>
    <div class="pos fs2" style="left: 355px; top: 330px; color: #231F20;" id="w24x">37</div>
    <div class="pos fs2" style="left: 398px; top: 330px; color: #231F20;" id="w25x">more</div>
    <div class="pos fs2" style="left: 474px; top: 330px; color: #231F20;" id="w26x">is</div>
    <div class="pos fs6" style="left: 161px; top: 435px; color: #231F20;">Find My Food</div>
    <div class="pos fs2" style="left: 161px; top: 492px; color: #231F20;">Hey! I have something more interesting for you.</div>
    <div class="pos fs2" style="left: 161px; top: 531px; color: #231F20;">Ma told me, there are things to eat in some rooms.</div>
    <div class="pos fs2" style="left: 161px; top: 569px; color: #231F20;">Help me find those room numbers. Mark them in my home.</div>
    <div class="pos fs2" style="left: 161px; top: 607px; color: #231F20;">See what you get!</div>
    <div class="pos fs2" style="left: 673px; top: 165px; color: #231F20;">.</div>
    <div class="pos fs2" style="left: 725px; top: 220px; color: #231F20;">.</div>
    <div class="pos fs2" style="left: 741px; top: 275px; color: #231F20;">.</div>
    <div class="pos fs2" style="left: 641px; top: 330px; color: #231F20;">.</div>
    <div class="pos fs2" style="left: 208px; top: 386px; color: #231F20;" id="w27x">l)</div>
    <div class="pos fs2" style="left: 269px; top: 386px; color: #231F20;" id="w28x">Take</div>
    <div class="pos fs2" style="left: 341px; top: 386px; color: #231F20;" id="w29x">35</div>
    <div class="pos fs2" style="left: 384px; top: 386px; color: #231F20;" id="w30x">away</div>
    <div class="pos fs2" style="left: 460px; top: 386px; color: #231F20;" id="w31x">from</div>
    <div class="pos fs2" style="left: 531px; top: 386px; color: #231F20;" id="w32x">83.</div>
    <div class="pos fs2" style="left: 582px; top: 386px; color: #231F20;" id="w33x">We</div>
    <div class="pos fs2" style="left: 630px; top: 386px; color: #231F20;" id="w34x">get</div>
    <div class="pos fs2" style="left: 814px; top: 386px; color: #231F20;">.</div>
    <div class="pos fs3" style="left: 566px; top: 1429px; color: #231F20;">31</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000004.jpg');"> </div>
    <div class="pos fs2" style="left: 219px; top: 174px; color: #231F20;" id="w1x">E.g.,</div>
    <div class="pos fs2" style="left: 307px; top: 174px; color: #231F20;" id="w2x">47</div>
    <div class="pos fs2" style="left: 375px; top: 174px; color: #231F20;" id="w3x">=</div>
    <div class="pos fs2" style="left: 418px; top: 174px; color: #231F20;" id="w4x">37</div>
    <div class="pos fs2" style="left: 469px; top: 174px; color: #231F20;" id="w5x">+</div>
    <div class="pos fs2" style="left: 502px; top: 174px; color: #231F20;" id="w6x">10</div>
    <div class="pos fs2" style="left: 306px; top: 259px; color: #231F20;" id="w7x">37</div>
    <div class="pos fs2" style="left: 348px; top: 259px; color: #231F20;" id="w8x">+</div>
    <div class="pos fs2" style="left: 374px; top: 259px; color: #231F20;" id="w9x">9</div>
    <div class="pos fs2" style="left: 408px; top: 259px; color: #231F20;" id="w10x">=</div>
    <div class="pos fs7" style="left: 610px; top: 219px; color: #231F20;">Is there a</div>
    <div class="pos fs7" style="left: 610px; top: 247px; color: #231F20;">shortcut to</div>
    <div class="pos fs7" style="left: 610px; top: 278px; color: #231F20;">do this?</div>
    <div class="pos fs2" style="left: 308px; top: 440px; color: #231F20;">65 – 30 =</div>
    <div class="pos fs7" style="left: 814px; top: 427px; color: #231F20;">Will it be easier</div>
    <div class="pos fs7" style="left: 814px; top: 454px; color: #231F20;">to go to 46 + 20</div>
    <div class="pos fs7" style="left: 814px; top: 482px; color: #231F20;">first?</div>
    <div class="pos fs2" style="left: 866px; top: 576px; color: #231F20;">= 46 + 21</div>
    <div class="pos fs7" style="left: 529px; top: 690px; color: #231F20;">Similarly how</div>
    <div class="pos fs7" style="left: 529px; top: 720px; color: #231F20;">will you do this?</div>
    <div class="pos fs2" style="left: 865px; top: 770px; color: #231F20;">= 87 – 30</div>
    <div class="pos fs2" style="left: 307px; top: 944px; color: #231F20;">66 –</div>
    <div class="pos fs2" style="left: 300px; top: 1065px; color: #231F20;">45 +</div>
    <div class="pos fs2" style="left: 307px; top: 944px; color: #231F20;">= 11</div>
    <div class="pos fs2" style="left: 773px; top: 940px; color: #231F20;">36 =</div>
    <div class="pos fit fs2" style="left: 300px; top: 1065px; width: 239px; color: #231F20;">
      <span class="just">= 99</span>
    </div>
    <div class="pos fs2" style="left: 405px; top: 1177px; color: #231F20;" id="w11x">+</div>
    <div class="pos fs2" style="left: 439px; top: 1177px; color: #231F20;" id="w12x">26</div>
    <div class="pos fs2" style="left: 482px; top: 1177px; color: #231F20;" id="w13x">=</div>
    <div class="pos fs2" style="left: 507px; top: 1177px; color: #231F20;" id="w14x">75</div>
    <div class="pos fit fs2" style="left: 415px; top: 1285px; width: 124px; color: #231F20;">
      <span class="just">– 21 = 35</span>
    </div>
    <div class="pos fit fs2" style="left: 775px; top: 1064px; width: 58px; color: #231F20;">
      <span class="just">40 +</span>
    </div>
    <div class="pos fit fs2" style="left: 773px; top: 1178px; width: 58px; color: #231F20;">
      <span class="just">98 =</span>
    </div>
    <div class="pos fs2" style="left: 769px; top: 1283px; color: #231F20;">57 –</div>
    <div class="pos fs2" style="left: 773px; top: 940px; color: #231F20;">+ 9</div>
    <div class="pos fit fs2" style="left: 775px; top: 1064px; width: 213px; color: #231F20;">
      <span class="just">= 76</span>
    </div>
    <div class="pos fit fs2" style="left: 773px; top: 1178px; width: 212px; color: #231F20;">
      <span class="just">+ 50</span>
    </div>
    <div class="pos fs2" style="left: 769px; top: 1283px; color: #231F20;">= 20</div>
    <div class="pos fs3" style="left: 621px; top: 1430px; color: #231F20;">32</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000005.jpg');"> </div>
    <div class="pos fs6" style="left: 161px; top: 153px; color: #231F20;">Adding Made Easy</div>
    <div class="pos fit fs2" style="left: 610px; top: 226px; width: 382px; color: #231F20;">
      <span class="just">Anisha bought apples for 37</span>
    </div>
    <div class="pos fit fs2" style="left: 611px; top: 259px; width: 382px; color: #231F20;">
      <span class="just">rupees. Raja bought bananas</span>
    </div>
    <div class="pos fit fs2" style="left: 611px; top: 291px; width: 381px; color: #231F20;">
      <span class="just">for 21 rupees. The woman</span>
    </div>
    <div class="pos fs2" style="left: 612px; top: 323px; color: #231F20;">selling fruits said:</div>
    <div class="pos fs2" style="left: 615px; top: 389px; color: #231F20;">37 is 30 and 7</div>
    <div class="pos fs2" style="left: 615px; top: 457px; color: #231F20;" id="w1x">21</div>
    <div class="pos fs2" style="left: 658px; top: 457px; color: #231F20;" id="w2x">is</div>
    <div class="pos fs2" style="left: 699px; top: 457px; color: #231F20;" id="w3x">20</div>
    <div class="pos fs2" style="left: 743px; top: 457px; color: #231F20;" id="w4x">and</div>
    <div class="pos fs2" style="left: 802px; top: 457px; color: #231F20;" id="w5x">1</div>
    <div class="pos fs2" style="left: 616px; top: 521px; color: #231F20;">So 37 and 21 make 58.</div>
    <div class="pos fs7" style="left: 378px; top: 607px; color: #231F20;">The first two,</div>
    <div class="pos fs7" style="left: 378px; top: 634px; color: #231F20;">I can do.</div>
    <div class="pos fs7" style="left: 543px; top: 757px; color: #231F20;">How did she</div>
    <div class="pos fs7" style="left: 543px; top: 785px; color: #231F20;">add 37 and 21</div>
    <div class="pos fs7" style="left: 543px; top: 812px; color: #231F20;">so fast?</div>
    <div class="pos fs2" style="left: 207px; top: 1008px; color: #231F20;">Let us also try. Look at this sum.</div>
    <div class="pos fs8" style="left: 247px; top: 1058px; color: #231F20;">26</div>
    <div class="pos fs2" style="left: 318px; top: 1065px; color: #231F20;">+</div>
    <div class="pos fs2 " style="left: 214px; top: 1109px; color: #231F20;">
      <div class="just"><span style="font-family: 'kucmwo_tahoma';">20 6</span>+</div>
    </div>
    <div class="pos fs8 " style="left: 214px; top: 1171px; color: #231F20;">
      <div class="just">20<span style="font-family: 'vqwnse_bookman_light';"> +</span> 40</div>
    </div>
    <div class="pos fs8" style="left: 247px; top: 1226px; color: #231F20;">60</div>
    <div class="pos fs2" style="left: 318px; top: 1113px; color: #231F20;">+</div>
    <div class="pos fs2" style="left: 318px; top: 1175px; color: #231F20;">+</div>
    <div class="pos fs2" style="left: 318px; top: 1233px; color: #231F20;">+</div>
    <div class="pos fs8" style="left: 313px; top: 1283px; color: #231F20;">69</div>
    <div class="pos fs2" style="left: 205px; top: 1332px; color: #231F20;">The answer is 69.</div>
    <div class="pos fs3" style="left: 564px; top: 1429px; color: #231F20;">33</div>
    <div class="pos fs8" style="left: 379px; top: 1058px; color: #231F20;">43</div>
    <div class="pos fs2 fit " style="left: 346px; top: 1109px; width: 86px; color: #231F20;">
      <div class="just"><span style="font-family: 'kucmwo_tahoma';">40 3</span>+</div>
    </div>
    <div class="pos fs8 fit " style="left: 352px; top: 1171px; width: 80px; color: #231F20;">
      <div class="just">6<span style="font-family: 'vqwnse_bookman_light';"> +</span> 3</div>
    </div>
    <div class="pos fs8" style="left: 386px; top: 1226px; color: #231F20;">9</div>
    <div class="pos fs7" style="left: 539px; top: 1091px; color: #231F20;">We break 26 into 20 + 6</div>
    <div class="pos fs7" style="left: 539px; top: 1118px; color: #231F20;">and 43 into 40 + 3.</div>
    <div class="pos fs7" style="left: 672px; top: 1201px; color: #231F20;">Then it's easy:</div>
    <div class="pos fs7" style="left: 672px; top: 1228px; color: #231F20;">we add 20 + 40 and</div>
    <div class="pos fs7" style="left: 672px; top: 1256px; color: #231F20;">3 + 6 !</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000006.jpg');"> </div>
    <div class="pos fs9" style="left: 166px; top: 100px; color: #231F20;">17</div>
    <div class="pos fs9" style="left: 165px; top: 159px; color: #231F20;">16</div>
    <div class="pos fs9" style="left: 161px; top: 219px; color: #231F20;">15</div>
    <div class="pos fs9" style="left: 160px; top: 287px; color: #231F20;">14</div>
    <div class="pos fs9" style="left: 160px; top: 348px; color: #231F20;">13</div>
    <div class="pos fs9" style="left: 155px; top: 413px; color: #231F20;">12</div>
    <div class="pos fs9" style="left: 156px; top: 476px; color: #231F20;">11</div>
    <div class="pos fs9" style="left: 156px; top: 541px; color: #231F20;">10</div>
    <div class="pos fs9" style="left: 149px; top: 597px; color: #EC008C;">+10</div>
    <div class="pos fs9" style="left: 165px; top: 699px; color: #231F20;">9</div>
    <div class="pos fs9" style="left: 163px; top: 752px; color: #231F20;">8</div>
    <div class="pos fs9" style="left: 164px; top: 812px; color: #231F20;">7</div>
    <div class="pos fs9" style="left: 165px; top: 875px; color: #231F20;">6</div>
    <div class="pos fs9" style="left: 164px; top: 942px; color: #231F20;">5</div>
    <div class="pos fs9" style="left: 164px; top: 1006px; color: #231F20;">4</div>
    <div class="pos fs9" style="left: 164px; top: 1070px; color: #231F20;">3</div>
    <div class="pos fs9" style="left: 163px; top: 1134px; color: #231F20;">2</div>
    <div class="pos fs9" style="left: 170px; top: 1201px; color: #231F20;">1</div>
    <div class="pos fs9" style="left: 241px; top: 92px; color: #EC008C;">–7</div>
    <div class="pos fs9" style="left: 308px; top: 102px; color: #231F20;">18 19 20 21 22 23 24</div>
    <div class="pos fs9" style="left: 731px; top: 110px; color: #231F20;">25</div>
    <div class="pos fs2" style="left: 267px; top: 170px; color: #231F20;">Can you do it another way? Say how.</div>
    <div class="pos fs2" style="left: 266px; top: 256px; color: #231F20;">33</div>
    <div class="pos fs2" style="left: 358px; top: 256px; color: #231F20;">+</div>
    <div class="pos fit fs2" style="left: 427px; top: 256px; width: 85px; color: #231F20;">
      <span class="just">56 =</span>
    </div>
    <div class="pos fs2" style="left: 496px; top: 352px; color: #231F20;">=</div>
    <div class="pos fs2" style="left: 496px; top: 449px; color: #231F20;">=</div>
    <div class="pos fit fs2" style="left: 565px; top: 256px; width: 85px; color: #231F20;">
      <span class="just">30 +</span>
    </div>
    <div class="pos fit fs2" style="left: 565px; top: 352px; width: 85px; color: #231F20;">
      <span class="just">80 +</span>
    </div>
    <div class="pos fit fs2" style="left: 565px; top: 449px; width: 85px; color: #231F20;">
      <span class="just">80 +</span>
    </div>
    <div class="pos fs2" style="left: 703px; top: 256px; color: #231F20;">3</div>
    <div class="pos fs2" style="left: 703px; top: 352px; color: #231F20;">3</div>
    <div class="pos fs2" style="left: 703px; top: 449px; color: #231F20;">9</div>
    <div class="pos fs9" style="left: 806px; top: 111px; color: #EC008C;">+35</div>
    <div class="pos fs9" style="left: 877px; top: 105px; color: #231F20;">26 27 28 29</div>
    <div class="pos fs2" style="left: 772px; top: 256px; color: #231F20;">+</div>
    <div class="pos fs2" style="left: 772px; top: 352px; color: #231F20;">+</div>
    <div class="pos fs2" style="left: 772px; top: 449px; color: #231F20;">=</div>
    <div class="pos fs2" style="left: 841px; top: 256px; color: #231F20;">50 +</div>
    <div class="pos fs2" style="left: 841px; top: 352px; color: #231F20;">6</div>
    <div class="pos fs2" style="left: 841px; top: 449px; color: #231F20;">89</div>
    <div class="pos fs2" style="left: 265px; top: 533px; color: #231F20;">See if you can do the same with these sums.</div>
    <div class="pos fs2" style="left: 263px; top: 596px; color: #231F20;">37</div>
    <div class="pos fs2" style="left: 357px; top: 596px; color: #231F20;">+</div>
    <div class="pos fit fs2" style="left: 426px; top: 596px; width: 85px; color: #231F20;">
      <span class="just">22 =</span>
    </div>
    <div class="pos fs2" style="left: 495px; top: 693px; color: #231F20;">=</div>
    <div class="pos fs2" style="left: 495px; top: 790px; color: #231F20;">=</div>
    <div class="pos fit fs2" style="left: 564px; top: 596px; width: 85px; color: #231F20;">
      <span class="just">30 +</span>
    </div>
    <div class="pos fs2" style="left: 633px; top: 693px; color: #231F20;">+</div>
    <div class="pos fs2" style="left: 633px; top: 790px; color: #231F20;">+</div>
    <div class="pos fs2" style="left: 702px; top: 596px; color: #231F20;">7</div>
    <div class="pos fs2" style="left: 771px; top: 596px; color: #231F20;">+</div>
    <div class="pos fs2" style="left: 771px; top: 693px; color: #231F20;">+</div>
    <div class="pos fs2" style="left: 771px; top: 790px; color: #231F20;">=</div>
    <div class="pos fs2" style="left: 979px; top: 256px; color: #231F20;">6</div>
    <div class="pos fs2" style="left: 840px; top: 596px; color: #231F20;">20 +</div>
    <div class="pos fs2" style="left: 978px; top: 596px; color: #231F20;">2</div>
    <div class="pos fs2" style="left: 264px; top: 923px; color: #231F20;">73</div>
    <div class="pos fs2" style="left: 356px; top: 923px; color: #231F20;">+</div>
    <div class="pos fit fs2" style="left: 425px; top: 923px; width: 85px; color: #231F20;">
      <span class="just">24 =</span>
    </div>
    <div class="pos fs2" style="left: 494px; top: 1020px; color: #231F20;">=</div>
    <div class="pos fs2" style="left: 494px; top: 1117px; color: #231F20;">=</div>
    <div class="pos fs6" style="left: 219px; top: 1171px; color: #EC008C;">MANGO CHILLI GAME</div>
    <div class="pos fit fs7" style="left: 219px; top: 1209px; width: 828px; color: #231F20;">
      <span class="just">Use a pair of dice (or ten tamarind seeds). Keep a different coloured button for</span>
    </div>
    <div class="pos fit fs7" style="left: 219px; top: 1243px; width: 828px; color: #231F20;">
      <span class="just">each player. If you reach a mango you go forward (+). If you step on a chilli you</span>
    </div>
    <div class="pos fs7" style="left: 219px; top: 1278px; color: #231F20;">have to go back (–). See who reaches back home first !</div>
    <div class="pos fs9" style="left: 184px; top: 1350px; color: #231F20;">HOME</div>
    <div class="pos fs9" style="left: 322px; top: 1347px; color: #231F20;">79</div>
    <div class="pos fs9" style="left: 383px; top: 1347px; color: #231F20;">78 77</div>
    <div class="pos fs9" style="left: 501px; top: 1352px; color: #EC008C;">–50</div>
    <div class="pos fs9" style="left: 584px; top: 1328px; color: #231F20;">76 75 74 73 72 71</div>
    <div class="pos fs3" style="left: 622px; top: 1430px; color: #231F20;">34</div>
    <div class="pos fs9" style="left: 960px; top: 1358px; color: #EC008C;">+5</div>
    <div class="pos fs9" style="left: 1046px; top: 1347px; color: #231F20;">70</div>
    <div class="pos fs2" style="left: 632px; top: 923px; color: #231F20;">+</div>
    <div class="pos fs2" style="left: 631px; top: 1020px; color: #231F20;">+</div>
    <div class="pos fs2" style="left: 632px; top: 1117px; color: #231F20;">+</div>
    <div class="pos fs2" style="left: 770px; top: 923px; color: #231F20;">+</div>
    <div class="pos fs2" style="left: 770px; top: 1020px; color: #231F20;">+</div>
    <div class="pos fs2" style="left: 770px; top: 1117px; color: #231F20;">=</div>
    <div class="pos fs2" style="left: 908px; top: 923px; color: #231F20;">+</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000007.jpg');"> </div>
    <div class="pos fs9" style="left: 146px; top: 106px; color: #231F20;">30</div>
    <div class="pos fs9" style="left: 228px; top: 103px; color: #EC008C;">–9</div>
    <div class="pos fs9" style="left: 290px; top: 98px; color: #231F20;">31 32 33 34 35</div>
    <div class="pos fs2" style="left: 213px; top: 209px; color: #231F20;" id="w1x">56</div>
    <div class="pos fs2" style="left: 256px; top: 209px; color: #231F20;" id="w2x">+</div>
    <div class="pos fs2" style="left: 281px; top: 209px; color: #231F20;" id="w3x">21</div>
    <div class="pos fs2" style="left: 374px; top: 209px; color: #231F20;" id="w4x">=</div>
    <div class="pos fs2" style="left: 374px; top: 306px; color: #231F20;">=</div>
    <div class="pos fs2" style="left: 374px; top: 402px; color: #231F20;">=</div>
    <div class="pos fs7" style="left: 223px; top: 520px; color: #231F20;">56 + 21 = 56 + 20 + 1</div>
    <div class="pos fs7" style="left: 329px; top: 589px; color: #231F20;">= 57 + 20</div>
    <div class="pos fs7" style="left: 328px; top: 658px; color: #231F20;">= 77</div>
    <div class="pos fs7" style="left: 247px; top: 684px; color: #231F20;">Aha!</div>
    <div class="pos fs7" style="left: 247px; top: 711px; color: #231F20;">I can do it this way also!</div>
    <div class="pos fs2" style="left: 512px; top: 209px; color: #231F20;">+</div>
    <div class="pos fs2" style="left: 512px; top: 306px; color: #231F20;">+</div>
    <div class="pos fs2" style="left: 512px; top: 402px; color: #231F20;">+</div>
    <div class="pos fs9" style="left: 598px; top: 107px; color: #231F20;">36</div>
    <div class="pos fs2" style="left: 650px; top: 209px; color: #231F20;">+</div>
    <div class="pos fs2" style="left: 650px; top: 306px; color: #231F20;">+</div>
    <div class="pos fs2" style="left: 650px; top: 402px; color: #231F20;">=</div>
    <div class="pos fs9" style="left: 667px; top: 97px; color: #EC008C;">–14</div>
    <div class="pos fs9" style="left: 740px; top: 101px; color: #231F20;">37 38 39</div>
    <div class="pos fs2" style="left: 788px; top: 209px; color: #231F20;">+</div>
    <div class="pos fs9" style="left: 922px; top: 113px; color: #231F20;">40</div>
    <div class="pos fs9" style="left: 1006px; top: 113px; color: #EC008C;">+12</div>
    <div class="pos fs9" style="left: 1015px; top: 499px; color: #231F20;">46</div>
    <div class="pos fs9" style="left: 1016px; top: 431px; color: #231F20;">45</div>
    <div class="pos fs9" style="left: 1012px; top: 368px; color: #231F20;">44</div>
    <div class="pos fs9" style="left: 1015px; top: 303px; color: #231F20;">43</div>
    <div class="pos fs9" style="left: 1010px; top: 238px; color: #231F20;">42</div>
    <div class="pos fs9" style="left: 1012px; top: 173px; color: #231F20;">41</div>
    <div class="pos fs9" style="left: 1010px; top: 558px; color: #231F20;">47</div>
    <div class="pos fs9" style="left: 1013px; top: 661px; color: #EC008C;">–8</div>
    <div class="pos fs2" style="left: 161px; top: 881px; color: #231F20;">Now work out the steps in your mind.</div>
    <div class="pos fs2" style="left: 161px; top: 913px; color: #231F20;">Write the answers directly in the boxes.</div>
    <div class="pos fit fs2" style="left: 161px; top: 978px; width: 127px; color: #231F20;">
      <span class="just">33 + 42 =</span>
    </div>
    <div class="pos fit fs2" style="left: 161px; top: 1106px; width: 127px; color: #231F20;">
      <span class="just">19 + 61 =</span>
    </div>
    <div class="pos fs2" style="left: 274px; top: 1235px; color: #231F20;">= 48 + 42</div>
    <div class="pos fs9" style="left: 150px; top: 1340px; color: #231F20;">69 68 67 66</div>
    <div class="pos fs9" style="left: 398px; top: 1352px; color: #EC008C;">–13</div>
    <div class="pos fs9" style="left: 478px; top: 1335px; color: #231F20;">65</div>
    <div class="pos fs2" style="left: 575px; top: 978px; color: #231F20;" id="w5x">=</div>
    <div class="pos fs2" style="left: 600px; top: 978px; color: #231F20;" id="w6x">33</div>
    <div class="pos fs2" style="left: 642px; top: 978px; color: #231F20;" id="w7x">+</div>
    <div class="pos fs2" style="left: 667px; top: 978px; color: #231F20;" id="w8x">27</div>
    <div class="pos fs2" style="left: 746px; top: 978px; color: #231F20;" id="w9x">55</div>
    <div class="pos fs2" style="left: 788px; top: 978px; color: #231F20;" id="w10x">+</div>
    <div class="pos fs2" style="left: 813px; top: 978px; color: #231F20;" id="w11x">25</div>
    <div class="pos fs2" style="left: 855px; top: 978px; color: #231F20;" id="w12x">=</div>
    <div class="pos fs2" style="left: 575px; top: 1106px; color: #231F20;" id="w13x">=</div>
    <div class="pos fs2" style="left: 600px; top: 1106px; color: #231F20;" id="w14x">34</div>
    <div class="pos fs2" style="left: 643px; top: 1106px; color: #231F20;" id="w15x">+</div>
    <div class="pos fs2" style="left: 668px; top: 1106px; color: #231F20;" id="w16x">63</div>
    <div class="pos fs2" style="left: 749px; top: 1106px; color: #231F20;" id="w17x">67</div>
    <div class="pos fs2" style="left: 791px; top: 1106px; color: #231F20;" id="w18x">+</div>
    <div class="pos fs2" style="left: 816px; top: 1106px; color: #231F20;" id="w19x">25</div>
    <div class="pos fs2" style="left: 858px; top: 1106px; color: #231F20;" id="w20x">=</div>
    <div class="pos fs2" style="left: 575px; top: 1235px; color: #231F20;" id="w21x">=</div>
    <div class="pos fs2" style="left: 600px; top: 1235px; color: #231F20;" id="w22x">53</div>
    <div class="pos fs2" style="left: 642px; top: 1235px; color: #231F20;" id="w23x">+</div>
    <div class="pos fs2" style="left: 667px; top: 1235px; color: #231F20;" id="w24x">64</div>
    <div class="pos fs2" style="left: 746px; top: 1235px; color: #231F20;" id="w25x">72</div>
    <div class="pos fs2" style="left: 788px; top: 1235px; color: #231F20;" id="w26x">+</div>
    <div class="pos fs2" style="left: 813px; top: 1235px; color: #231F20;" id="w27x">56</div>
    <div class="pos fs2" style="left: 855px; top: 1235px; color: #231F20;" id="w28x">=</div>
    <div class="pos fs9" style="left: 1020px; top: 1042px; color: #231F20;">53</div>
    <div class="pos fs9" style="left: 1022px; top: 978px; color: #231F20;">52</div>
    <div class="pos fs9" style="left: 1021px; top: 914px; color: #231F20;">51</div>
    <div class="pos fs9" style="left: 1020px; top: 847px; color: #231F20;">50</div>
    <div class="pos fs9" style="left: 1018px; top: 785px; color: #231F20;">49</div>
    <div class="pos fs9" style="left: 1020px; top: 722px; color: #231F20;">48</div>
    <div class="pos fs9" style="left: 1021px; top: 1100px; color: #231F20;">54</div>
    <div class="pos fs9" style="left: 1012px; top: 1205px; color: #EC008C;">+15</div>
    <div class="pos fit fs9" style="left: 554px; top: 1325px; width: 495px; color: #231F20;">
      <span class="just">64 63 62 61 60 59 58 57 56</span>
    </div>
    <div class="pos fs9" style="left: 1021px; top: 1262px; color: #231F20;">55</div>
    <div class="pos fs3" style="left: 564px; top: 1429px; color: #231F20;">35</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000008.jpg');"> </div>
    <div class="pos fs6" style="left: 412px; top: 157px; color: #231F20;">Let Me Tell You a Story..........</div>
    <div class="pos fit fs2" style="left: 219px; top: 213px; width: 828px; color: #231F20;">
      <span class="just">Once a baby lion lost his way in the jungle. He started crying</span>
    </div>
    <div class="pos fit fs2" style="left: 219px; top: 251px; width: 828px; color: #231F20;">
      <span class="just">and called out for his mother. An old deer took pity on him. He</span>
    </div>
    <div class="pos fit fs2" style="left: 219px; top: 289px; width: 828px; color: #231F20;">
      <span class="just">took him to his place. But the other deer got really scared. So</span>
    </div>
    <div class="pos fit fs2" style="left: 219px; top: 328px; width: 828px; color: #231F20;">
      <span class="just">did their other friends — rabbits, squirrels and birds. A lion</span>
    </div>
    <div class="pos fit fs2" style="left: 219px; top: 366px; width: 828px; color: #231F20;">
      <span class="just">among us! Oh, no! He will eat up our babies. The old deer</span>
    </div>
    <div class="pos fit fs2" style="left: 219px; top: 404px; width: 828px; color: #231F20;">
      <span class="just">said — don't worry. I will warn him about this. In the morning</span>
    </div>
    <div class="pos fit fs2" style="left: 219px; top: 443px; width: 828px; color: #231F20;">
      <span class="just">the baby lion thanked every one and started to leave. But a</span>
    </div>
    <div class="pos fit fs2" style="left: 219px; top: 481px; width: 828px; color: #231F20;">
      <span class="just">rabbit said — wait, he cannot go like this! Let us count to see if</span>
    </div>
    <div class="pos fs2" style="left: 219px; top: 519px; color: #231F20;" id="w1x">he</div>
    <div class="pos fs2" style="left: 258px; top: 519px; color: #231F20;" id="w2x">has</div>
    <div class="pos fs2" style="left: 321px; top: 519px; color: #231F20;" id="w3x">done</div>
    <div class="pos fs2" style="left: 392px; top: 519px; color: #231F20;" id="w4x">any</div>
    <div class="pos fs2" style="left: 448px; top: 519px; color: #231F20;" id="w5x">mischief.</div>
    <div class="pos fs2" style="left: 574px; top: 519px; color: #231F20;" id="w6x">We</div>
    <div class="pos fs2" style="left: 620px; top: 519px; color: #231F20;" id="w7x">should</div>
    <div class="pos fs2" style="left: 717px; top: 519px; color: #231F20;" id="w8x">be</div>
    <div class="pos fs2" style="left: 756px; top: 519px; color: #231F20;" id="w9x">240</div>
    <div class="pos fs2" style="left: 813px; top: 519px; color: #231F20;" id="w10x">in</div>
    <div class="pos fs2" style="left: 847px; top: 519px; color: #231F20;" id="w11x">all.</div>
    <div class="pos fs2" style="left: 895px; top: 519px; color: #231F20;" id="w12x">Let's</div>
    <div class="pos fs2" style="left: 963px; top: 519px; color: #231F20;" id="w13x">count.</div>
    <div class="pos fs7" style="left: 268px; top: 1041px; color: #231F20;">Tillu counted rabbits and deer.</div>
    <div class="pos fs7" style="left: 268px; top: 1080px; color: #231F20;">There were 27</div>
    <div class="pos fs7" style="left: 399px; top: 1080px; color: #231F20;">and 48</div>
    <div class="pos fs7" style="left: 562px; top: 1175px; color: #231F20;">The old deer counted birds and squirrels.</div>
    <div class="pos fs7" style="left: 562px; top: 1214px; color: #231F20;">There were 124</div>
    <div class="pos fs7" style="left: 827px; top: 1214px; color: #231F20;">and 38</div>
    <div class="pos fs5" style="left: 243px; top: 1306px; color: #231F20;">In the chapter Fun with Numbers, children would have made token cards. The same token cards</div>
    <div class="pos fs5" style="left: 243px; top: 1333px; color: #231F20;">should be used for exercises in addition before children do written sums.</div>
    <div class="pos fs3" style="left: 622px; top: 1430px; color: #231F20;">36</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000009.jpg');"> </div>
    <div class="pos fs2" style="left: 208px; top: 169px; color: #231F20;">Let's add and find out how many deer and rabbits were</div>
    <div class="pos fs2" style="left: 207px; top: 207px; color: #231F20;">there...</div>
    <div class="pos fit fs2" style="left: 161px; top: 379px; width: 141px; color: #231F20;">
      <span class="just">Number of</span>
    </div>
    <div class="pos fit fs2" style="left: 161px; top: 449px; width: 141px; color: #231F20;">
      <span class="just">Number of</span>
    </div>
    <div class="pos fs2" style="left: 594px; top: 787px; color: #231F20;">Putting all the</div>
    <div class="pos fs2" style="left: 594px; top: 831px; color: #231F20;">we get fifteen</div>
    <div class="pos fs2" style="left: 594px; top: 787px; color: #231F20;">s together,</div>
    <div class="pos fs2" style="left: 594px; top: 831px; color: #231F20;">s</div>
    <div class="pos fs2" style="left: 594px; top: 875px; color: #231F20;" id="w1x">Ten</div>
    <div class="pos fs2" style="left: 689px; top: 875px; color: #231F20;" id="w2x">s</div>
    <div class="pos fs2" style="left: 712px; top: 875px; color: #231F20;" id="w3x">make</div>
    <div class="pos fs2" style="left: 793px; top: 875px; color: #231F20;" id="w4x">one</div>
    <div class="pos fs2" style="left: 886px; top: 875px; color: #231F20;" id="w5x">and</div>
    <div class="pos fs2" style="left: 594px; top: 918px; color: #231F20;">we are left with five</div>
    <div class="pos fs2" style="left: 594px; top: 918px; color: #231F20;">s</div>
    <div class="pos fs2" style="left: 566px; top: 1189px; color: #231F20;" id="w6x">Now</div>
    <div class="pos fs2" style="left: 630px; top: 1189px; color: #231F20;" id="w7x">putting</div>
    <div class="pos fs2" style="left: 732px; top: 1189px; color: #231F20;" id="w8x">together</div>
    <div class="pos fs2" style="left: 847px; top: 1189px; color: #231F20;" id="w9x">all</div>
    <div class="pos fs2" style="left: 886px; top: 1189px; color: #231F20;" id="w10x">the</div>
    <div class="pos fs2" style="left: 966px; top: 1189px; color: #231F20;" id="w11x">s,</div>
    <div class="pos fs2" style="left: 566px; top: 1237px; color: #231F20;" id="w12x">we</div>
    <div class="pos fs2" style="left: 611px; top: 1237px; color: #231F20;" id="w13x">get</div>
    <div class="pos fs2" style="left: 658px; top: 1237px; color: #231F20;" id="w14x">seven</div>
    <div class="pos fs2" style="left: 770px; top: 1237px; color: #231F20;" id="w15x">s</div>
    <div class="pos fs2" style="left: 380px; top: 1317px; color: #231F20;">So total number of</div>
    <div class="pos fs3" style="left: 564px; top: 1429px; color: #231F20;">37</div>
    <div class="pos fs2" style="left: 743px; top: 1317px; color: #231F20;">and</div>
    <div class="pos fs2" style="left: 380px; top: 1317px; color: #231F20;">= 75</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000010.jpg');"> </div>
    <div class="pos fs2" style="left: 270px; top: 167px; color: #231F20;">Similarly we add the number of birds and number of</div>
    <div class="pos fs2" style="left: 265px; top: 205px; color: #231F20;">squirrels.</div>
    <div class="pos fs10" style="left: 725px; top: 230px; color: #231F20;">Putting all the</div>
    <div class="pos fs10" style="left: 725px; top: 230px; color: #231F20;">s together</div>
    <div class="pos fs10" style="left: 725px; top: 268px; color: #231F20;">first and grouping them</div>
    <div class="pos fit fs10" style="left: 248px; top: 350px; width: 118px; color: #231F20;">
      <span class="just">Number of</span>
    </div>
    <div class="pos fit fs10" style="left: 249px; top: 421px; width: 118px; color: #231F20;">
      <span class="just">Number of</span>
    </div>
    <div class="pos fit fs10" style="left: 721px; top: 488px; width: 260px; color: #231F20;">
      <span class="just">Then putting together</span>
    </div>
    <div class="pos fs10" style="left: 721px; top: 526px; color: #231F20;" id="w1x">the</div>
    <div class="pos fs10" style="left: 799px; top: 526px; color: #231F20;" id="w2x">s</div>
    <div class="pos fs10" style="left: 821px; top: 526px; color: #231F20;" id="w3x">and</div>
    <div class="pos fs10" style="left: 874px; top: 526px; color: #231F20;" id="w4x">lastly</div>
    <div class="pos fs10" style="left: 945px; top: 526px; color: #231F20;" id="w5x">the</div>
    <div class="pos fs10" style="left: 763px; top: 565px; color: #231F20;">s we get</div>
    <div class="pos fs2" style="left: 220px; top: 685px; color: #231F20;">So together birds and squirrels were 162,</div>
    <div class="pos fs2" style="left: 220px; top: 723px; color: #231F20;">and deer and rabbits were 75.</div>
    <div class="pos fs2" style="left: 220px; top: 773px; color: #231F20;">The old deer said — we were 240 in number, now how many are</div>
    <div class="pos fs2" style="left: 220px; top: 811px; color: #231F20;">we in all?</div>
    <div class="pos fs7" style="left: 303px; top: 880px; color: #231F20;">Can you guess now,</div>
    <div class="pos fs7" style="left: 303px; top: 919px; color: #231F20;">if the baby lion will go back home?</div>
    <div class="pos fs7" style="left: 303px; top: 957px; color: #231F20;">Has the baby lion eaten up any animal?</div>
    <div class="pos fs2" style="left: 217px; top: 1038px; color: #231F20;">To find out, do the addition in the box below:</div>
    <div class="pos fit fs2" style="left: 217px; top: 1164px; width: 138px; color: #231F20;">
      <span class="just">Number of</span>
    </div>
    <div class="pos fit fs2" style="left: 218px; top: 1235px; width: 138px; color: #231F20;">
      <span class="just">Number of</span>
    </div>
    <div class="pos fs2" style="left: 217px; top: 1164px; color: #231F20;">and</div>
    <div class="pos fs2" style="left: 218px; top: 1235px; color: #231F20;">and</div>
    <div class="pos fs3" style="left: 622px; top: 1430px; color: #231F20;">38</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000011.jpg');"> </div>
    <div class="pos fs6" style="left: 161px; top: 153px; color: #231F20;">How Many Bulbs?</div>
    <div class="pos fs2" style="left: 161px; top: 211px; color: #231F20;">1. A factory made 270 bulbs on the first day.</div>
    <div class="pos fs2" style="left: 201px; top: 243px; color: #231F20;">On the second day it made 123 bulbs.</div>
    <div class="pos fs2" style="left: 161px; top: 308px; color: #231F20;">How many bulbs did the factory make altogether?</div>
    <div class="pos fs10" style="left: 162px; top: 613px; color: #231F20;">First day - 270 bulbs</div>
    <div class="pos fs7" style="left: 731px; top: 608px; color: #231F20;">270 + 123</div>
    <div class="pos fs10" style="left: 414px; top: 653px; color: #231F20;">Second day - 123 bulbs</div>
    <div class="pos fs7" style="left: 731px; top: 638px; color: #231F20;">Is the sum more than</div>
    <div class="pos fs7" style="left: 731px; top: 669px; color: #231F20;">350</div>
    <div class="pos fs7" style="left: 731px; top: 700px; color: #231F20;">or less than 350?</div>
    <div class="pos fs7" style="left: 731px; top: 742px; color: #231F20;">I think..</div>
    <div class="pos fs7" style="left: 502px; top: 830px; color: #231F20;">How many</div>
    <div class="pos fs7" style="left: 502px; top: 864px; color: #231F20;">altogether?</div>
    <div class="pos fs7" style="left: 731px; top: 772px; color: #231F20;">270 and 100 is 370?</div>
    <div class="pos fs7" style="left: 731px; top: 803px; color: #231F20;">The sum is more than</div>
    <div class="pos fs7" style="left: 731px; top: 834px; color: #231F20;">350.</div>
    <div class="pos fs10" style="left: 161px; top: 1038px; color: #231F20;">Solution:</div>
    <div class="pos fs10" style="left: 163px; top: 1148px; color: #231F20;">Bulbs made</div>
    <div class="pos fs10" style="left: 163px; top: 1176px; color: #231F20;">on first day</div>
    <div class="pos fs10" style="left: 160px; top: 1221px; color: #231F20;">Bulbs made on</div>
    <div class="pos fs10" style="left: 160px; top: 1248px; color: #231F20;">second day</div>
    <div class="pos fs10" style="left: 161px; top: 1324px; color: #231F20;">Sum</div>
    <div class="pos fs3" style="left: 564px; top: 1429px; color: #231F20;">39</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000012.jpg');"> </div>
    <div class="pos fs2" style="left: 219px; top: 163px; color: #231F20;">2. A shopkeeper Rafi had 153 candles. Paras gave him 237</div>
    <div class="pos fs2" style="left: 265px; top: 195px; color: #231F20;">more candles. How many candles does Rafi have now?</div>
    <div class="pos fs7" style="left: 777px; top: 286px; color: #231F20;">237 + 153 = ?</div>
    <div class="pos fs7" style="left: 777px; top: 332px; color: #231F20;">Is the sum more than 400</div>
    <div class="pos fs7" style="left: 777px; top: 368px; color: #231F20;">or less than 400?</div>
    <div class="pos fs10" style="left: 753px; top: 619px; color: #231F20;">Solution:</div>
    <div class="pos fs10" style="left: 754px; top: 904px; color: #231F20;">Sum</div>
    <div class="pos fs2" style="left: 219px; top: 962px; color: #231F20;">Work out the following story problems in the same way.</div>
    <div class="pos fs2" style="left: 219px; top: 1006px; color: #231F20;">Read each problem and say it in your words.</div>
    <div class="pos fs2" style="left: 219px; top: 1049px; color: #231F20;">Guess the answer before writing it.</div>
    <div class="pos fit fs2" style="left: 219px; top: 1118px; width: 828px; color: #231F20;">
      <span class="just">A. A train compartment is carrying 132 people. Another</span>
    </div>
    <div class="pos fs2" style="left: 266px; top: 1150px; color: #231F20;" id="w1x">compartment</div>
    <div class="pos fs2" style="left: 446px; top: 1150px; color: #231F20;" id="w2x">is</div>
    <div class="pos fs2" style="left: 474px; top: 1150px; color: #231F20;" id="w3x">carrying</div>
    <div class="pos fs2" style="left: 587px; top: 1150px; color: #231F20;" id="w4x">129</div>
    <div class="pos fs2" style="left: 642px; top: 1150px; color: #231F20;" id="w5x">people.</div>
    <div class="pos fs2" style="left: 740px; top: 1150px; color: #231F20;" id="w6x">In</div>
    <div class="pos fs2" style="left: 773px; top: 1150px; color: #231F20;" id="w7x">all,</div>
    <div class="pos fs2" style="left: 819px; top: 1150px; color: #231F20;" id="w8x">how</div>
    <div class="pos fs2" style="left: 878px; top: 1150px; color: #231F20;" id="w9x">many</div>
    <div class="pos fs2" style="left: 963px; top: 1150px; color: #231F20;" id="w10x">people</div>
    <div class="pos fs2" style="left: 266px; top: 1182px; color: #231F20;">are there in both the compartments?</div>
    <div class="pos fs3" style="left: 622px; top: 1430px; color: #231F20;">40</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000013.jpg');"> </div>
    <div class="pos fs2" style="left: 161px; top: 163px; color: #231F20;" id="w1x">B.</div>
    <div class="pos fs2" style="left: 208px; top: 163px; color: #231F20;" id="w2x">Shanu</div>
    <div class="pos fs2" style="left: 319px; top: 163px; color: #231F20;" id="w3x">found</div>
    <div class="pos fs2" style="left: 407px; top: 163px; color: #231F20;" id="w4x">138</div>
    <div class="pos fs2" style="left: 468px; top: 163px; color: #231F20;" id="w5x">pebbles.</div>
    <div class="pos fs2" style="left: 208px; top: 195px; color: #231F20;">Karim found 44 pebbles.</div>
    <div class="pos fs2" style="left: 208px; top: 227px; color: #231F20;">How many pebbles did they find</div>
    <div class="pos fs2" style="left: 208px; top: 260px; color: #231F20;">in all?</div>
    <div class="pos fs2" style="left: 161px; top: 646px; color: #231F20;">C. A teacher kept a note of which fruits students like in her</div>
    <div class="pos fs2" style="left: 208px; top: 678px; color: #231F20;">school. This is what she found:</div>
    <div class="pos fs2" style="left: 159px; top: 1053px; color: #231F20;">Find out:</div>
    <div class="pos fs2" style="left: 159px; top: 1103px; color: #231F20;">(a) How many students in the school like oranges?</div>
    <div class="pos fs2" style="left: 159px; top: 1152px; color: #231F20;">(b) How many students in the school like mangoes?</div>
    <div class="pos fit fs2" style="left: 159px; top: 1202px; width: 785px; color: #231F20;">
      <span class="just">(c) Altogether, how many students are there in the school?</span>
    </div>
    <div class="pos fit fs2" style="left: 159px; top: 1252px; width: 784px; color: #231F20;">
      <span class="just">(d) Is the number of girls more than 350 or less than 350?</span>
    </div>
    <div class="pos fs3" style="left: 567px; top: 1429px; color: #231F20;">41</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000014.jpg');"> </div>
    <div class="pos fs7" style="left: 365px; top: 118px; color: #231F20;" id="w1x">20</div>
    <div class="pos fs7" style="left: 416px; top: 118px; color: #231F20;" id="w2x">+</div>
    <div class="pos fs7" style="left: 442px; top: 118px; color: #231F20;" id="w3x">11</div>
    <div class="pos fs11" style="left: 499px; top: 118px; color: #231F20;" id="w4x">+</div>
    <div class="pos fs7" style="left: 557px; top: 118px; color: #231F20;" id="w5x">9</div>
    <div class="pos fs7" style="left: 585px; top: 118px; color: #231F20;" id="w6x">-</div>
    <div class="pos fs7" style="left: 608px; top: 118px; color: #231F20;" id="w7x">13</div>
    <div class="pos fs11" style="left: 674px; top: 118px; color: #231F20;" id="w8x">+</div>
    <div class="pos fs7" style="left: 729px; top: 118px; color: #231F20;" id="w9x">3</div>
    <div class="pos fs7" style="left: 757px; top: 118px; color: #231F20;" id="w10x">+</div>
    <div class="pos fs7" style="left: 780px; top: 118px; color: #231F20;" id="w11x">16</div>
    <div class="pos fs11" style="left: 850px; top: 118px; color: #231F20;" id="w12x">+</div>
    <div class="pos fs7" style="left: 908px; top: 118px; color: #231F20;" id="w13x">5</div>
    <div class="pos fs7" style="left: 936px; top: 118px; color: #231F20;" id="w14x">-</div>
    <div class="pos fs7" style="left: 959px; top: 118px; color: #231F20;" id="w15x">10</div>
    <div class="pos fs6" style="left: 253px; top: 240px; color: #231F20;">Practice Time</div>
    <div class="pos fit fs2" style="left: 257px; top: 327px; width: 192px; color: #231F20;">
      <span class="just">A. (i) 345 + 52</span>
    </div>
    <div class="pos fit fs2" style="left: 287px; top: 373px; width: 162px; color: #231F20;">
      <span class="just">(ii) 492 + 29</span>
    </div>
    <div class="pos fit fs2" style="left: 278px; top: 419px; width: 171px; color: #231F20;">
      <span class="just">(iii) 245 + 93</span>
    </div>
    <div class="pos fit fs2" style="left: 257px; top: 506px; width: 120px; color: #231F20;">
      <span class="just">B. 319</span>
    </div>
    <div class="pos fit fs2" style="left: 300px; top: 552px; width: 76px; color: #231F20;">
      <span class="just">+ 323</span>
    </div>
    <div class="pos fs11" style="left: 162px; top: 637px; color: #231F20;">+</div>
    <div class="pos fs11" style="left: 164px; top: 794px; color: #231F20;">+</div>
    <div class="pos fs12 " style="left: 529px; top: 904px; color: #EC008C;">
      <div class="just">P<span style="color: #00AEEF;">u</span></div>
    </div>
    <div class="pos fs13" style="left: 167px; top: 976px; color: #231F20;">+</div>
    <div class="pos fs14" style="left: 564px; top: 879px; color: #00904C;">zz</div>
    <div class="pos fs12 " style="left: 622px; top: 904px; color: #EC008C;">
      <div class="just"><span style="color: #00AEEF;">l</span>e</div>
    </div>
    <div class="pos fs2" style="left: 471px; top: 950px; color: #231F20;">Addition is my best friend</div>
    <div class="pos fs2" style="left: 471px; top: 982px; color: #231F20;">We never have a fight</div>
    <div class="pos fs2" style="left: 471px; top: 1014px; color: #231F20;">When I am done</div>
    <div class="pos fs2" style="left: 471px; top: 1047px; color: #231F20;">Call out to him</div>
    <div class="pos fs2" style="left: 471px; top: 1079px; color: #231F20;">And check if I am right</div>
    <div class="pos fs9" style="left: 220px; top: 1167px; color: #EC008C;">MIND TRAIN GAME :</div>
    <div class="pos fs15" style="left: 220px; top: 1196px; color: #FFFFFF;">Mind train game</div>
    <div class="pos fit fs7" style="left: 219px; top: 1206px; width: 828px; color: #231F20;">
      <span class="just">Two friends play this game. You look at each train. Some people come in (+) and</span>
    </div>
    <div class="pos fit fs7" style="left: 219px; top: 1241px; width: 828px; color: #231F20;">
      <span class="just">some leave (–). How many are there in all? Solve in your MIND! Discuss your</span>
    </div>
    <div class="pos fit fs7" style="left: 219px; top: 1275px; width: 828px; color: #231F20;">
      <span class="just">answer. The friend who gets the right answer first wins some points. List down</span>
    </div>
    <div class="pos fs7" style="left: 219px; top: 1310px; color: #231F20;">your points. Add to find who wins the most!</div>
    <div class="pos fs3" style="left: 622px; top: 1430px; color: #231F20;">42</div>
    <div class="pos fs2" style="left: 325px; top: 681px; color: #231F20;">427</div>
    <div class="pos fit fs2" style="left: 300px; top: 727px; width: 76px; color: #231F20;">
      <span class="just">+ 248</span>
    </div>
    <div class="pos fit fs2" style="left: 587px; top: 327px; width: 185px; color: #231F20;">
      <span class="just">(iv) 643 + 345</span>
    </div>
    <div class="pos fit fs2" style="left: 596px; top: 373px; width: 177px; color: #231F20;">
      <span class="just">(v) 750 + 219</span>
    </div>
    <div class="pos fs2" style="left: 632px; top: 506px; color: #231F20;">304</div>
    <div class="pos fit fs2" style="left: 598px; top: 552px; width: 85px; color: #231F20;">
      <span class="just">+ 406</span>
    </div>
    <div class="pos fs2" style="left: 632px; top: 681px; color: #231F20;">684</div>
    <div class="pos fit fs2" style="left: 598px; top: 727px; width: 85px; color: #231F20;">
      <span class="just">+ 232</span>
    </div>
    <div class="pos fs2" style="left: 944px; top: 506px; color: #231F20;">363</div>
    <div class="pos fit fs2" style="left: 908px; top: 552px; width: 87px; color: #231F20;">
      <span class="just">+ 456</span>
    </div>
    <div class="pos fs7" style="left: 161px; top: 1152px; -webkit-transform: rotate(-90.0deg); color: #231F20;">8 + 10</div>
    <div class="pos fit fs7" style="left: 157px; top: 947px; -webkit-transform: rotate(-90.0deg); width: 75px; color: #231F20;">
      <span class="just">3 - 6</span>
    </div>
    <div class="pos fit fs7" style="left: 155px; top: 784px; -webkit-transform: rotate(-90.0deg); width: 75px; color: #231F20;">
      <span class="just">7 - 3</span>
    </div>
    <div class="pos fit fs7" style="left: 154px; top: 630px; -webkit-transform: rotate(-90.0deg); width: 75px; color: #231F20;">
      <span class="just">7 - 6</span>
    </div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000015.jpg');"> </div>
    <div class="pos fs7" style="left: 424px; top: 112px; color: #231F20;">19 - 5</div>
    <div class="pos fs11" style="left: 564px; top: 104px; color: #231F20;">+</div>
    <div class="pos fs7" style="left: 630px; top: 115px; color: #231F20;">6 - 15</div>
    <div class="pos fs11" style="left: 760px; top: 112px; color: #231F20;" id="w1x">+</div>
    <div class="pos fs7" style="left: 820px; top: 112px; color: #231F20;" id="w2x">7</div>
    <div class="pos fs7" style="left: 850px; top: 112px; color: #231F20;" id="w3x">-</div>
    <div class="pos fs7" style="left: 874px; top: 112px; color: #231F20;" id="w4x">3</div>
    <div class="pos fs2" style="left: 161px; top: 230px; color: #231F20;">Work out four different ways to write the numbers.</div>
    <div class="pos fs7" style="left: 464px; top: 328px; color: #231F20;">If you add all the numbers in the first</div>
    <div class="pos fs7" style="left: 464px; top: 367px; color: #231F20;">box, you will always get 59.</div>
    <div class="pos fs11" style="left: 1042px; top: 436px; color: #231F20;">+</div>
    <div class="pos fs11" style="left: 943px; top: 110px; color: #231F20;">+</div>
    <div class="pos fs11" style="left: 1043px; top: 212px; color: #231F20;">+</div>
    <div class="pos fs11" style="left: 1042px; top: 631px; color: #231F20;">+</div>
    <div class="pos fs16 " style="left: 161px; top: 1052px; color: #231F20;">
      <div class="just">Can You Solve this<span style="color: #DB2128; font-family: 'kfrjqj_courier_bold'; font-size: 31px;"> P</span><span style="color: #00AEEF; font-family: 'kfrjqj_courier_bold'; font-size: 31px;">u</span></div>
    </div>
    <div class="pos fs14" style="left: 472px; top: 1028px; color: #00904C;">zz</div>
    <div class="pos fs12 " style="left: 529px; top: 1048px; color: #00AEEF;">
      <div class="just">l<span style="color: #DB2128;">e</span><span style="color: #231F20; font-size: 35px;">?</span></div>
    </div>
    <div class="pos fs2" style="left: 161px; top: 1095px; color: #231F20;">Write the numbers 1, 2, 3,</div>
    <div class="pos fs2" style="left: 161px; top: 1127px; color: #231F20;">4, 5, 6 in the circles, so that</div>
    <div class="pos fs2" style="left: 161px; top: 1159px; color: #231F20;">the sum of the numbers on</div>
    <div class="pos fs2" style="left: 161px; top: 1191px; color: #231F20;">each side of the figure is 12.</div>
    <div class="pos fs7" style="left: 242px; top: 1334px; color: #231F20;" id="w5x">17</div>
    <div class="pos fs7" style="left: 286px; top: 1334px; color: #231F20;" id="w6x">-</div>
    <div class="pos fs7" style="left: 308px; top: 1334px; color: #231F20;" id="w7x">20</div>
    <div class="pos fs11" style="left: 375px; top: 1334px; color: #231F20;" id="w8x">+</div>
    <div class="pos fs7" style="left: 445px; top: 1344px; color: #231F20;">3 - 19</div>
    <div class="pos fs11" style="left: 570px; top: 1334px; color: #231F20;">+</div>
    <div class="pos fs3" style="left: 564px; top: 1429px; color: #231F20;">43</div>
    <div class="pos fs7" style="left: 633px; top: 1346px; color: #231F20;">9 + 12</div>
    <div class="pos fs11" style="left: 768px; top: 1334px; color: #231F20;">+</div>
    <div class="pos fs7" style="left: 836px; top: 1348px; color: #231F20;">9 - 20</div>
    <div class="pos fs7" style="left: 1070px; top: 301px; -webkit-transform: rotate(90.0deg); color: #231F20;">4 + 20</div>
    <div class="pos fs7" style="left: 1065px; top: 520px; -webkit-transform: rotate(90.0deg); color: #231F20;">1 - 7</div>
    <div class="pos fs7" style="left: 1065px; top: 698px; -webkit-transform: rotate(90.0deg); color: #231F20;">5 - 4</div>
    <div class="pos fs7" style="left: 1010px; top: 136px; -webkit-transform: rotate(45.0deg); color: #231F20;">6 - 10</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000016.jpg');"> </div>
    <div class="pos fs6" style="left: 219px; top: 153px; color: #231F20;">Find Mithoo's Bag</div>
    <div class="pos fs2" style="left: 219px; top: 220px; color: #231F20;">Do all the sums mentally:</div>
    <div class="pos fs2" style="left: 219px; top: 360px; color: #231F20;" id="w1x">a)</div>
    <div class="pos fs2" style="left: 269px; top: 360px; color: #231F20;" id="w2x">75</div>
    <div class="pos fs2" style="left: 312px; top: 360px; color: #231F20;" id="w3x">+</div>
    <div class="pos fs2" style="left: 337px; top: 360px; color: #231F20;" id="w4x">20</div>
    <div class="pos fs2" style="left: 380px; top: 360px; color: #231F20;" id="w5x">=</div>
    <div class="pos fs2" style="left: 219px; top: 443px; color: #231F20;" id="w6x">b)</div>
    <div class="pos fs2" style="left: 269px; top: 443px; color: #231F20;" id="w7x">90</div>
    <div class="pos fs2" style="left: 312px; top: 443px; color: #231F20;" id="w8x">+</div>
    <div class="pos fs2" style="left: 337px; top: 443px; color: #231F20;" id="w9x">60</div>
    <div class="pos fs2" style="left: 380px; top: 443px; color: #231F20;" id="w10x">=</div>
    <div class="pos fs2" style="left: 219px; top: 525px; color: #231F20;" id="w11x">c)</div>
    <div class="pos fs2" style="left: 269px; top: 525px; color: #231F20;" id="w12x">25</div>
    <div class="pos fs2" style="left: 311px; top: 525px; color: #231F20;" id="w13x">+</div>
    <div class="pos fs2" style="left: 337px; top: 525px; color: #231F20;" id="w14x">30</div>
    <div class="pos fs2" style="left: 379px; top: 525px; color: #231F20;" id="w15x">+</div>
    <div class="pos fs2" style="left: 404px; top: 525px; color: #231F20;" id="w16x">3</div>
    <div class="pos fs2" style="left: 430px; top: 525px; color: #231F20;" id="w17x">=</div>
    <div class="pos fs2" style="left: 219px; top: 608px; color: #231F20;" id="w18x">d)</div>
    <div class="pos fs2" style="left: 269px; top: 608px; color: #231F20;" id="w19x">9</div>
    <div class="pos fs2" style="left: 295px; top: 608px; color: #231F20;" id="w20x">+</div>
    <div class="pos fs2" style="left: 320px; top: 608px; color: #231F20;" id="w21x">40</div>
    <div class="pos fs2" style="left: 362px; top: 608px; color: #231F20;" id="w22x">+</div>
    <div class="pos fs2" style="left: 387px; top: 608px; color: #231F20;" id="w23x">31</div>
    <div class="pos fs2" style="left: 430px; top: 608px; color: #231F20;" id="w24x">=</div>
    <div class="pos fs2" style="left: 219px; top: 690px; color: #231F20;" id="w25x">e)</div>
    <div class="pos fs2" style="left: 269px; top: 690px; color: #231F20;" id="w26x">500</div>
    <div class="pos fs2" style="left: 329px; top: 690px; color: #231F20;" id="w27x">+</div>
    <div class="pos fs2" style="left: 355px; top: 690px; color: #231F20;" id="w28x">200</div>
    <div class="pos fs2" style="left: 414px; top: 690px; color: #231F20;" id="w29x">=</div>
    <div class="pos fs2" style="left: 219px; top: 772px; color: #231F20;" id="w30x">f)</div>
    <div class="pos fs2" style="left: 269px; top: 772px; color: #231F20;" id="w31x">400</div>
    <div class="pos fs2" style="left: 329px; top: 772px; color: #231F20;" id="w32x">+</div>
    <div class="pos fs2" style="left: 355px; top: 772px; color: #231F20;" id="w33x">350</div>
    <div class="pos fs2" style="left: 414px; top: 772px; color: #231F20;" id="w34x">=</div>
    <div class="pos fit fs2" style="left: 219px; top: 856px; width: 230px; color: #231F20;">
      <span class="just">Find Mithoo's bag</span>
    </div>
    <div class="pos fit fs2" style="left: 219px; top: 888px; width: 230px; color: #231F20;">
      <span class="just">and check your</span>
    </div>
    <div class="pos fs2" style="left: 219px; top: 921px; color: #231F20;">answers.</div>
    <div class="pos fs2" style="left: 219px; top: 981px; color: #231F20;">Draw a line</div>
    <div class="pos fs2" style="left: 219px; top: 1014px; color: #231F20;">through the</div>
    <div class="pos fs2" style="left: 219px; top: 1046px; color: #231F20;">numbers which</div>
    <div class="pos fs2" style="left: 219px; top: 1078px; color: #231F20;">are answers</div>
    <div class="pos fs2" style="left: 219px; top: 1110px; color: #231F20;">written in the</div>
    <div class="pos fs2" style="left: 219px; top: 1142px; color: #231F20;">boxes above.</div>
    <div class="pos fs2" style="left: 515px; top: 364px; color: #231F20;">95</div>
    <div class="pos fs2" style="left: 507px; top: 440px; color: #231F20;">150</div>
    <div class="pos fs2" style="left: 671px; top: 360px; color: #231F20;" id="w35x">g)</div>
    <div class="pos fs2" style="left: 719px; top: 360px; color: #231F20;" id="w36x">670</div>
    <div class="pos fs2" style="left: 779px; top: 360px; color: #231F20;" id="w37x">+</div>
    <div class="pos fs2" style="left: 804px; top: 360px; color: #231F20;" id="w38x">120</div>
    <div class="pos fs2" style="left: 864px; top: 360px; color: #231F20;" id="w39x">=</div>
    <div class="pos fs2" style="left: 671px; top: 443px; color: #231F20;" id="w40x">h)</div>
    <div class="pos fs2" style="left: 719px; top: 443px; color: #231F20;" id="w41x">380</div>
    <div class="pos fs2" style="left: 779px; top: 443px; color: #231F20;" id="w42x">+</div>
    <div class="pos fs2" style="left: 804px; top: 443px; color: #231F20;" id="w43x">210</div>
    <div class="pos fs2" style="left: 864px; top: 443px; color: #231F20;" id="w44x">=</div>
    <div class="pos fs2" style="left: 671px; top: 525px; color: #231F20;" id="w45x">i)</div>
    <div class="pos fs2" style="left: 719px; top: 525px; color: #231F20;" id="w46x">205</div>
    <div class="pos fs2" style="left: 779px; top: 525px; color: #231F20;" id="w47x">+</div>
    <div class="pos fs2" style="left: 804px; top: 525px; color: #231F20;" id="w48x">650</div>
    <div class="pos fs2" style="left: 864px; top: 525px; color: #231F20;" id="w49x">=</div>
    <div class="pos fs2" style="left: 671px; top: 608px; color: #231F20;" id="w50x">j)</div>
    <div class="pos fs2" style="left: 719px; top: 608px; color: #231F20;" id="w51x">128</div>
    <div class="pos fs2" style="left: 779px; top: 608px; color: #231F20;" id="w52x">+</div>
    <div class="pos fs2" style="left: 804px; top: 608px; color: #231F20;" id="w53x">600</div>
    <div class="pos fs2" style="left: 864px; top: 608px; color: #231F20;" id="w54x">=</div>
    <div class="pos fs2" style="left: 671px; top: 690px; color: #231F20;" id="w55x">k)</div>
    <div class="pos fs2" style="left: 719px; top: 690px; color: #231F20;" id="w56x">150</div>
    <div class="pos fs2" style="left: 778px; top: 690px; color: #231F20;" id="w57x">+</div>
    <div class="pos fs2" style="left: 804px; top: 690px; color: #231F20;" id="w58x">69</div>
    <div class="pos fs2" style="left: 846px; top: 690px; color: #231F20;" id="w59x">=</div>
    <div class="pos fs2" style="left: 671px; top: 772px; color: #231F20;" id="w60x">l)</div>
    <div class="pos fs2" style="left: 719px; top: 772px; color: #231F20;" id="w61x">37</div>
    <div class="pos fs2" style="left: 761px; top: 772px; color: #231F20;" id="w62x">+</div>
    <div class="pos fs2" style="left: 786px; top: 772px; color: #231F20;" id="w63x">46</div>
    <div class="pos fs2" style="left: 829px; top: 772px; color: #231F20;" id="w64x">+</div>
    <div class="pos fs2" style="left: 854px; top: 772px; color: #231F20;" id="w65x">3</div>
    <div class="pos fs2" style="left: 880px; top: 772px; color: #231F20;" id="w66x">=</div>
    <div class="pos fs3" style="left: 622px; top: 1430px; color: #231F20;">44</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000017.jpg');"> </div>
    <div class="pos fs6" style="left: 162px; top: 153px; color: #231F20;">Card Game</div>
    <div class="pos fs2" style="left: 162px; top: 210px; color: #231F20;">One day Bubbly and Gopu were playing. Bubbly gave three</div>
    <div class="pos fs2" style="left: 162px; top: 249px; color: #231F20;">number cards to Gopu. He arranged the cards in two ways.</div>
    <div class="pos fs7" style="left: 225px; top: 395px; color: #231F20;">Can you arrange these cards</div>
    <div class="pos fs7" style="left: 225px; top: 433px; color: #231F20;">other than these two ways?</div>
    <div class="pos fs2" style="left: 164px; top: 560px; color: #231F20;">Bubbly arranged</div>
    <div class="pos fs2" style="left: 164px; top: 599px; color: #231F20;">them this way:</div>
    <div class="pos fs2" style="left: 615px; top: 690px; color: #231F20;">Isn't it interesting?</div>
    <div class="pos fs2" style="left: 161px; top: 801px; color: #231F20;">You can also play it. Here are the cards for you. Work out the</div>
    <div class="pos fs2" style="left: 161px; top: 840px; color: #231F20;">combination. Place the cards in the right boxes.</div>
    <div class="pos fs2" style="left: 163px; top: 935px; color: #231F20;">a)</div>
    <div class="pos fs18" style="left: 272px; top: 952px; color: #231F20;">50</div>
    <div class="pos fs18" style="left: 348px; top: 952px; color: #231F20;">70</div>
    <div class="pos fs18" style="left: 424px; top: 954px; color: #231F20;">20</div>
    <div class="pos fs18" style="left: 728px; top: 954px; color: #231F20;">50</div>
    <div class="pos fs10" style="left: 745px; top: 1008px; ">_</div>
    <div class="pos fs10" style="left: 743px; top: 1070px; ">_</div>
    <div class="pos fs2" style="left: 163px; top: 1180px; color: #231F20;">b)</div>
    <div class="pos fs18" style="left: 804px; top: 954px; color: #231F20;">20</div>
    <div class="pos fs18" style="left: 880px; top: 956px; color: #231F20;">70</div>
    <div class="pos fs10" style="left: 879px; top: 1015px; ">=</div>
    <div class="pos fs10" style="left: 880px; top: 1077px; ">=</div>
    <div class="pos fs18" style="left: 279px; top: 1200px; color: #231F20;">30</div>
    <div class="pos fs18" style="left: 355px; top: 1200px; color: #231F20;">42</div>
    <div class="pos fs18" style="left: 431px; top: 1201px; color: #231F20;">12</div>
    <div class="pos fs18" style="left: 728px; top: 1196px; color: #231F20;">30</div>
    <div class="pos fs10" style="left: 741px; top: 1254px; ">_</div>
    <div class="pos fs10" style="left: 739px; top: 1316px; ">_</div>
    <div class="pos fs3" style="left: 564px; top: 1429px; color: #231F20;">45</div>
    <div class="pos fs18" style="left: 804px; top: 1197px; color: #231F20;">42</div>
    <div class="pos fs18" style="left: 880px; top: 1198px; color: #231F20;">12</div>
    <div class="pos fs10" style="left: 875px; top: 1261px; ">=</div>
    <div class="pos fs10" style="left: 876px; top: 1323px; ">=</div>
  </body>
</html>

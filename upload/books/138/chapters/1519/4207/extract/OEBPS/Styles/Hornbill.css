html, body {
font-family:Arial, Helvetica, sans-serif;
}

body {
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}

.chapter {
text-align:right;
}

.image {
text-align:center;
}

.subjectHead {
text-align:right;
}

.readfind {
color:rgb(0, 174, 239);
margin:2% 0;
padding:2% 0 2% 10%;
}

.glossaryText {
color:rgb(0, 174, 239);
}

.author {
text-align:right;
}
.bg
{
	background-color: #39F;
	}
span.char-style-override-8 {
	color:#777;
}
span.char-style-override-6 {
	color:#555;
	font-style:normal;
	font-weight:500;
}
.lining_box
{
	border:1px solid #03F;
	padding:7px;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:80%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#000000;

padding:10px;
line-height:120%;
width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.footer


{


display:none;


}
.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {

div.chapter_pos

{
top:50%;
font-size:1em;
}
div.chapter_pos div

{
width:70%;
}
.cover_img_small
{
width:90%;
}
}
body {
font-family:"Arial";
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}
#prelims
{
	line-height:200%;
}

#prelims .char-style-override-16, .para-style-override-21, .char-style-override-26
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color:#00aeef;
}
#prelims .char-style-override-2
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:#00aeef;
}
table
{
    width:100%;
    border:1px solid #000;
    border-collapse:collapse;
}
td
{
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}
.underline_txt
{
font-decoration:underline;
}
.bold_txt
{
font-weight:bold;
}
.center_element
{
margin:auto;
}
.italics_txt
{
font-style:italic;
}
.block_element
{
display:block;
}
.img_rt
{
float:right;
clear:both;
}
.img_lft
{
float:left;
}
h2{
	color: #FFF;
	font-size: 1.5em;
	background: #0033FF;
	padding: 5px;
	}
.eHeading {
	color: #0033FF;
	font-size: 1.3em;
	font-weight: bold;
	}
.ebox {
	border: 2px solid #03F;
	padding: 15px;
	border-radius: 15px;
	}

@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
	font-family:"Walkman-Chanakya-905";
}

body {
font-size:120%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}

.image {
text-align:center;
}
.author {
text-align:right;
}

.chapterHeading {
font-size:160%;
color: gray;
margin-bottom:20px;
}

.chapterNumber {
	font-size: 125%;
}


.center {
	text-align: center;
}

.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}



.img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
ul
{
	margin-left:45px;
}
.caption
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
	text-align:center;
}
p
{
	margin-top:10px;
}


.footer
{
	display:none;
}
table td
{
	padding:10px;
}
.conc
{
	color:#006699;
}
.englishMeaning
{
	font-family:arial;
}
.right
{
	display:inline;
	float:right;
	clear:both;
}
.bold
{
	font-size:115%;
	font-family: Walkman-Chanakya-905;
		font-weight:bold;
}
.italic
{
	font-weight:bold;
	font-size:100%;
	color:#03C;
}
.center
{
	text-align:center;
}
.right
{
	text-align:right;
}
.background
{
	background:#999;
	font-weight:bold;
	
}
span.char-style-override-1 {
	color:#a2550b;
	font-size:2.5em;
}
p.Hading {
	color:#b10069;
	font-family:"Walkman-Chanakya-905", serif;
	font-size:2em;
	font-weight:bold;
	text-align:center;
}
p.para-style-override-2 {
	color:#00a651;
	text-align:left;
}
p.Ques-Sub-hadin {
	color:#b10069;
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1.167em;
	font-weight:bold;
}
span.char-style-override-5 {
	color:#000000;
}
span.char-style-override-7 {
	color:#00a651;
	font-size:1.143em;
}
span.char-style-override-11 {
	font-family:"Walkman-Chanakya-905";
	font-size:1.167em;
	font-weight:bold;
}
.superscript{
position:relative;
top:-15%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}

.subscript{
position:relative;
bottom:-25%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}
.work
{
	font-size:105% ;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:40%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#CF5300;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#FFCC00;

font-weight:normal;

}

.note
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
}
	.underline_txt
{
font-decoration:underline;
}
.bold_txt
{
font-weight:bold;
}
.center_element
{
margin:auto;
}
.italics_txt
{
font-style:italic;
}
.block_element
{
display:block;
}
.img_rt
{
float:right;
clear:both;
}
.img_lft
{
float:left;
}
h2
{
color:#fff;
font-size:1.5em;
background:#ff6600;
padding:15px;
}

/* Chapter number */
h4
{
color:#00aeef;
font-size:1.3em;
font-weight:bold;
}
/* Concept Heading */
.ConceptHeading
{
color:#fff;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
.lining_box
{
border:2px solid #00CED1; border-radius:15px;
padding:15px;
}
/* Sub Heading */
.subHeading
{
color:#ff0080;
font-size:1.1em;
font-weight:bold;
}
/* Sub Heading 2*/
.subHeading2
{
color:#ff0080;
font-size:1em;
font-weight:bold;
}
/* Hightlisght Boxes */
.NewWordBox{
background-color:#F7E7BD;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.activityBox{
background-color:#DCDCDC;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.box{
background-color:#C8E9ED;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.footer
{
	display:none;
}
table td
{
	padding:10px;
}
.conc
{
	color:#006699;
}
.englishMeaning
{
	font-family:arial;
}
.right
{
	display:inline;
	float:right;
	clear:both;
}
.bold
{
	font-size:115%;
	font-family: Walkman-Chanakya-905;
		font-weight:bold;
}
.italic
{
	font-weight:bold;
	font-size:100%;
	color:#03C;
}
.center
{
	text-align:center;
}
.right
{
	text-align:right;
}
.background
{
	background:#999;
	font-weight:bold;
	
}
.superscript{
position:relative;
top:-15%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}

.subscript{
position:relative;
bottom:-25%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}
.work
{
	font-size:105% ;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:30%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#CF5300;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#FFCC00;

font-weight:normal;

}

.note
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
}
	.underline_txt
{
font-decoration:underline;
}
.bold_txt
{
font-weight:bold;
}
.center_element
{
margin:auto;
}
.italics_txt
{
font-style:italic;
}
.block_element
{
display:block;
}
.img_rt
{
float:right;
clear:both;
}
.img_lft
{
float:left;
}



.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {

div.chapter_pos

{
top:16%;
font-size:1em;
}
div.chapter_pos div

{
width:70%;
}
.cover_img_small
{
width:90%;
}
}
	
	

table
{
    width:100%;
    border:1px solid #000;
    border-collapse:collapse;
}
td
{
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}
#prelims .char-style-override-28
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color: rgb(175, 123, 10); 
	
}
#prelims .char-style-override-3
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:rgb(236, 0, 140); 
}

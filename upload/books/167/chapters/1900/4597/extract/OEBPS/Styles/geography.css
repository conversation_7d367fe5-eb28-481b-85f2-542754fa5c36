@charset "utf-8";

body {
	font-family:Arial, Helvetica, sans-serif;
	font-size:100%;
	line-height:130%;
	padding:2%;
	text-align:justify;
}

* {
	margin:0;
	padding:0;
}

.images {
	text-align:center;
}

.unitHeading {
	text-transform:uppercase;
	font-size:130%;
	text-align:center;
	margin:1% 0;
}

.unitNotes {
	text-align:center;
	font-size:115%;
}

.chapterNo {
	text-transform:uppercase;
	text-align:left;
}

.chapterHeading {
	text-transform:uppercase;
	font-size:140%;
	text-align:right;
	margin:2% 0;
}

.subHeading {
	text-transform:uppercase;
	margin:0.7% 0;
}

.topicHeading {
	margin:0.5% 0;
}

.exercise {
	text-transform:uppercase;
	text-align:left;
	margin:1.5% 0;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:50%;
line-height:110%;
font-weight:bold;

font-size:180%;

color:#fff;

}

div.chapter_pos div

{

background:#601407;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.activitybox2{
background-color:#A1B7BF;
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.lining_box
{
border:2px solid #FF9900;
padding:5px;
}
img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}



.footer

{

display:none;

}

table td

{

padding:10px;

}
table
{
	width:100%;
	border:1px solid #000;
	border-collapse:collapse;
}
td
{
	padding:10px;
	border:1px solid #000;
	border-collapse:collapse;
}
table.no_border, table.no_border td
{
	border:0px solid #000;
}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

h2
{
color:#000;
font-size:1.5em;
background:#EDEAC7;
padding:15px;
}
/* Chapter number */
h4
{
color:#d1640f;
font-size:1.3em;
padding:15px;
}
/* Concept Heading */
.ConceptHeading
{
color:#878352;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
.SubHeading
{
color:#d1640f;
font-size:1.1em;
font-weight:bold;
}
/* Sub Heading 2*/
.SubHeading2
{
color:#d1640f;
font-size:1em;
font-weight:bold;
}
/* Hightlisght Boxes */
.NewWordBox{
background-color:#F7E7BD;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 15px;
font-size:0.9em;
line-height:120%;
}


.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {

div.chapter_pos

{
top:50%;
font-size:1em;
}
div.chapter_pos div

{
width:70%;
}
.cover_img_small
{
width:90%;
}
}


.underline_txt
{
font-decoration:underline;
}
.bold_txt
{
font-weight:bold;
}
.center_element
{
margin:auto;
}
.italics_txt
{
font-style:italic;
}
.block_element
{
display:block;
}
.img_rt
{
float:right;
clear:both;
}
.img_lft
{
float:left;
}
body {
font-family:"Arial";
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}
#prelims
{
	line-height:200%;
}
#prelims .char-style-override-22, .char-style-override-18, .char-style-override-27
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color:#00aeef;
}
#prelims .char-style-override-2
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:#00aeef;
}


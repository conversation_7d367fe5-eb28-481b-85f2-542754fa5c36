<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg viewBox="0 0 935 1210" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
<style type="text/css"><![CDATA[
.g0_6{
fill: #B8292F;
}
.g1_6{
fill: none;
stroke: #B8292F;
stroke-width: 6.05;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g2_6{
fill: #EFEEEE;
}
.g3_6{
fill: #FFFFFF;
}
.g4_6{
fill: #231F20;
}
.g5_6{
fill: #EAF0F0;
}
.g6_6{
fill: #D2D9D8;
}
.g7_6{
fill: #536161;
}
.g8_6{
fill: #F8F1DE;
}
.g9_6{
fill: #D5222C;
}
.g10_6{
fill: #EE2229;
}
.g11_6{
fill: #AD2930;
}
.g12_6{
fill: #BFC8C7;
}
.g13_6{
fill: #F15D59;
}
.g14_6{
fill: #E93233;
}
.g15_6{
fill: none;
stroke: #231F20;
stroke-width: 1.4666667;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g16_6{
fill: #2E3092;
}
.g17_6{
fill: none;
stroke: #2E3092;
stroke-width: 1.4666667;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g18_6{
fill: #B8292F;
stroke: #B8292F;
stroke-width: 1.4666667;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 1000;
}
.g19_6{
fill: #2E3092;
stroke: #2E3092;
stroke-width: 1.4666667;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 1000;
}
]]></style>
</defs>
<path d="M628.1,111.5H826.7v-29H628.1v29Z" class="g0_6" />
<path d="M203.8,82.5V1121.1" class="g1_6" />
<path fill-rule="evenodd" d="M134.6,258.5L117.8,184.2l-7.3,-7.8L68.8,160l-13,71.1l78.8,27.4Z" class="g2_6" />
<path fill-rule="evenodd" d="M133.8,255.3L117.4,186.6l-7.5,-8.3l-38.5,-18L58.5,229.5l75.3,25.8Z" class="g3_6" />
<path fill-rule="evenodd" d="M117.4,184.5l.2,.3l15.7,68.4l-.5,-.3L116.9,184.7l.2,.3l.3,-.5Z" class="g4_6" />
<path fill-rule="evenodd" d="M109.9,176.8l.1,.1l7.4,7.6l-.3,.6l-7.5,-7.7l.2,.3l.1,-.9Z" class="g4_6" />
<path fill-rule="evenodd" d="M70.5,160.5l39.4,16.3l-.1,.8L70.4,161.4l.1,-.9Z" class="g4_6" />
<path fill-rule="evenodd" d="M58.3,227.4L70.5,160.9l.7,.4L59.3,227.7l-1,-.3Z" class="g4_6" />
<path fill-rule="evenodd" d="M133.4,253.2L59,227.6v-.8l74.3,25.8l.1,.6Z" class="g4_6" />
<path fill-rule="evenodd" d="M117.2,184.9l-.4,-.4c-2.2,-2.2,-4.3,-4.5,-6.5,-6.7l-.4,-.5l.1,1.2c.8,2.6,-.3,3.6,1.8,4.1l.9,.4c1.2,.5,2.4,1,3.7,1.5l.8,.4Z" class="g5_6" />
<path fill-rule="evenodd" d="M109.4,177.2l.6,-.1l.5,.5c2.1,2.2,4.3,4.3,6.5,6.6l.5,.4l-.4,.6l-.5,-.5c-2.1,-2.2,-4.3,-4.5,-6.6,-6.6l-.4,-.5l.6,-.2l-.8,-.2Z" class="g4_6" />
<path fill-rule="evenodd" d="M110.6,182.6l-.3,-.5c-.3,-1.3,-.6,-2.8,-.7,-4.2l-.2,-.7l.8,.2l.1,.5c.2,1.3,.3,2.5,.6,3.7l.1,.6l-.3,-.4l-.1,.8Z" class="g4_6" />
<path fill-rule="evenodd" d="M117.5,184.6l-.3,.7l-.4,-.1c-1.9,-.8,-3.9,-1.6,-5.8,-2.4l-.4,-.2l.1,-.8l.4,.2c1.9,.8,3.8,1.6,5.7,2.4l.5,.1l-.2,.7l.4,-.6Z" class="g4_6" />
<path fill-rule="evenodd" d="M115,191.1L74.4,174.3l-.3,1.7l41.4,17.2l-.5,-2.1Z" class="g6_6" />
<path fill-rule="evenodd" d="M116.2,197L73.6,179.4l-.2,2l43.3,18l-.5,-2.4Z" class="g6_6" />
<path fill-rule="evenodd" d="M117.5,203.7L72.8,185.1l-.3,2.3l45.4,18.8l-.4,-2.5Z" class="g6_6" />
<path fill-rule="evenodd" d="M118.8,210.9L71.9,191.4l-.4,2.5l47.8,19.8l-.5,-2.8Z" class="g6_6" />
<path fill-rule="evenodd" d="M120.2,218.8L70.9,198.4l-.4,2.8l50.4,20.9l-.7,-3.3Z" class="g6_6" />
<path fill-rule="evenodd" d="M121.9,227.7L69.8,206.1l-.4,3.2l53.2,22l-.7,-3.6Z" class="g6_6" />
<path fill-rule="evenodd" d="M94.7,184.2l-.9,1.4l-1.5,-2.5v-.2l-.1,-.1v-.2l.1,-.1l.1,-.1l.1,-.1h.1l.2,-.1l2.6,.5l-.7,1.5Z" class="g7_6" />
<path fill-rule="evenodd" d="M93.8,185.6l4.2,7.2l3.4,-3.7l1.7,-5.3l-7.7,-1.1l-1.6,2.9Z" class="g8_6" />
<path fill-rule="evenodd" d="M101.9,186.4l-.4,.1c-1.6,.3,-2.2,2,-2,3.5v.3l27.7,19.1l2.3,-3.9L101.9,186.4Z" class="g9_6" />
<path fill-rule="evenodd" d="M101.7,186.4v-.3l-.1,-.2v-.6l.1,-.2l.1,-.2v-.2l.1,-.3l.3,-.2l.1,-.1l.1,-.1l.2,-.1l.1,-.1l.2,-.1l.2,.1h.2L131,202.9l-1.5,2.6L101.7,186.4Z" class="g10_6" />
<path fill-rule="evenodd" d="M98.1,192.8l-.1,-.3v-.2c-.3,-.4,-.2,-.8,0,-1.1l.2,-.3l.1,-.1l.1,-.2l.2,-.1l.1,-.1l.2,-.1l.2,-.1l.3,-.1l.2,.1l27.7,19.1l-1.5,2.6L98.1,192.8Z" class="g11_6" />
<path fill-rule="evenodd" d="M131,202.9l-5.2,9l5,3.5l5.2,-9l-5,-3.5Z" class="g5_6" />
<path fill-rule="evenodd" d="M126.7,210.2l-1,1.7l4.6,3.1l1,-1.6l-4.6,-3.2Z" class="g12_6" />
<path fill-rule="evenodd" d="M140.1,211.5l-3.3,5.6c-.5,.8,-1.4,1.4,-2.3,.8l-3.7,-2.5l5.3,-9l3.6,2.5h.1l.1,.2l.2,.1l.1,.1v.2l.1,.2v.3l.1,.1v.3l-.1,.2v.3l-.1,.3l-.1,.1v.2Z" class="g13_6" />
<path fill-rule="evenodd" d="M131.9,213.6l3.5,2.6c.5,.2,1.3,.6,1.8,.1l.2,-.1l-.6,1l-.3,.4l-.3,.2l-.3,.2h-.4l-.3,.1h-.3l-.4,-.2l-.2,-.2l-3.5,-2.3l1.1,-1.8Z" class="g14_6" />
<path fill-rule="evenodd" d="M136.2,217.2h-.1L138,214l.6,.4l-1.8,3.3l-.1,.1l-.5,-.6Z" class="g4_6" />
<path fill-rule="evenodd" d="M134.8,217.7l-.2,-.1l.2,.1c.4,0,.8,-.1,1.2,-.3l.2,-.2l.5,.6l-.3,.2c-.7,.4,-1.2,.5,-2,.4l-.1,-.1l.5,-.6Z" class="g4_6" />
<path fill-rule="evenodd" d="M98.3,192.7l-.1,-.1l36.6,25.1l-.5,.6L97.9,193.2l-.2,-.2l.6,-.3Z" class="g4_6" />
<path fill-rule="evenodd" d="M92.6,182.9l5.7,9.8l-.6,.3L92,183.2l.6,-.3Z" class="g4_6" />
<path fill-rule="evenodd" d="M92.6,182.8v.1l-.6,.3v-.1l-.1,-.1l-.1,-.2v-.3l.2,-.1l-.1,-.1l.7,.5Z" class="g4_6" />
<path fill-rule="evenodd" d="M92.7,182.7h-.1v.1l-.7,-.5l.2,-.1l.2,-.1l.2,-.1h.2v.7Z" class="g4_6" />
<path fill-rule="evenodd" d="M103,184.1l.3,.1L92.7,182.7V182l10.6,1.4l.2,.2l-.5,.5Z" class="g4_6" />
<path fill-rule="evenodd" d="M139.5,209.1l.1,.2L103,184.1l.5,-.5l36.4,25l.2,.1l-.6,.4Z" class="g4_6" />
<path fill-rule="evenodd" d="M139.9,210.9h-.1l.1,-.3c-.1,-.5,0,-.8,-.3,-1.2l-.1,-.3l.6,-.4l.1,.3c.4,.7,.4,1.4,.3,2.2l-.1,.1l-.5,-.4Z" class="g4_6" />
<path fill-rule="evenodd" d="M138,214l1.9,-3.1l.5,.4l-1.8,3.1L138,214Z" class="g4_6" />
<path fill-rule="evenodd" d="M123.2,237.7L68.4,215l-.5,3.3l55.9,23.1l-.6,-3.7Z" class="g6_6" />
<path d="M132.4,246.6h68.2M55,246.6H97.2" class="g15_6" />
<path d="M53.9,1157.8h50.2v-24.9H53.9v24.9Z" class="g16_6" />
<path d="M755.7,1157h75.7m-735.1,0h15.2m4.7,0h15.3m4.7,0h15.2m4.8,0h15.2m4.8,0h15.2m4.8,0h15.2m4.8,0h15.2m4.7,0h15.3m4.7,0h15.2m4.8,0h15.2m4.8,0h15.2m4.8,0h15.2m4.8,0h15.2m4.7,0h15.3m4.7,0h15.2m4.8,0h15.2m4.8,0h15.2m4.8,0h15.2m4.7,0h15.3m4.7,0h15.2m4.8,0h15.2m4.8,0h15.2m4.8,0h15.2m4.8,0h15.2m4.7,0h15.3m4.7,0H611m4.8,0H631m4.8,0H651m4.8,0H671m4.8,0H691m4.7,0H711m4.5,0h15.3m4.5,0h15.3" class="g17_6" />
<path fill-rule="evenodd" d="M273.1,1002.7H569v29H273.1v-29Z" class="g18_6" />
<image preserveAspectRatio="none" x="220" y="975" width="59" height="59" xlink:href="img/1.png" />
<path fill-rule="evenodd" d="M220.7,235.4h515v30.3h-515V235.4Z" class="g19_6" />
<path fill-rule="evenodd" d="M279.6,777.1H545v28.8H279.6V777.1Z" class="g19_6" />
<image preserveAspectRatio="none" x="220" y="746" width="59" height="61" xlink:href="img/2.png" />
<path d="M220,213.4H825M220,956.8H825" class="g15_6" />
</svg>
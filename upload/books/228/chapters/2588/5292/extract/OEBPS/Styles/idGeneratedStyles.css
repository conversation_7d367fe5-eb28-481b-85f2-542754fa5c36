
body {
font-size:120%;
line-height:150%;
padding:2%;
text-align:justify;
font-family:"Walkman-Chanakya-905";
}
* {
margin:0;
padding:0;
}
@font-face {

font-family:"Walkman-Chanakya-905";

font-style:normal;

font-weight:bold;

src : url("../Fonts/wcb.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:italic;

font-weight:bold;

src : url("../Fonts/wcbi.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:oblique;

font-weight:bold;

src : url("../Fonts/wcbi.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:normal;

font-weight:normal;

src : url("../Fonts/wcn.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:italic;

font-weight:normal;

src : url("../Fonts/wcni.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:oblique;

font-weight:normal;

src : url("../Fonts/wcni.ttf");

}
.Hindi-Runing-Text
{
     font-family: "Walkman-Chanakya-905";
	font-size:120%;
	line-height:150%;
}
body#hindi .char-style-override-1
{
	font-family:arial;
}
/* Hightlisght Boxes */
.Box-Matter1{
background-color:rgb(242,228,206);
padding: 15px;
font-size:0.9em;
line-height:150%;
}
.akd-1, .Box-Matter, .Mid-day-meal{
background-color:rgb(242,228,206);
padding: 15px;
line-height:150%;
border-top:2px solid rgb(201,90,55);
}
.red{
color:rgb(158,49,35);
}
img
{
	
	margin-left: auto;
    margin-right: auto;
max-width:100%;
margin:5px;
}
.caption, .bold-and-italic-hindi, .Fig-caption, .Caption
{
	font-style: italic; 
	font-size: 0.83em; 	
font-weight:bold;
color:#4A4C52;
}
.CharOverride-13{
font-weight:bold;
}
.box{
background-color:#004E6B;
padding: 15px;
line-height:150%;
color:#fff;
font-weight:bold;
}
.box1{
border-top:2px solid #8CE0F9;
padding: 15px;
line-height:150%;
}
.box2
{
border-left:18px solid #8CE0F9;
padding: 15px;
line-height:150%;
}
{
color:#004E6B;
font-weight:bold;
}
/* Chapter Name */
h1
{
color:#000;
font-size:1.5em;
background:#D8D8DA;
padding:10px;
}
h2
{
color:#000;
font-size:1.3em;
padding:10px;
border-bottom:2px solid #D8D8DA;
border-width: 30%;
border-left: 25px solid #8CE0F9;
}
/* Chapter number */
h3{
color:#000;
font-size:1.1em;
}
h4
{
color:#004E6B;
font-size:1.0em;
}
h5
{
color:#004E6B;
font-size:0.9em;
}
/* Concept Heading */
.ConceptHeading, .Heading, .Heading-hindi, .Example-style,  .kyun, .Box-item-Heading
{
color:#333;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
.SubHeading, .Heading-2-hindi, .box-text-heading-hindi, .Box-text-heading-style, .Heading-2, .Heading-1, .subhead
{
color:#0094D9;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
.CharOverride-15, .CharOverride-5, .CharOverride-9, .ParaOverride-5
{
color:#6F7176;
font-weight:bold;
}
/* Sub Heading 2*/
.SubHeading2, .Sub-Heading
{
color:#0094D9;
font-size:1.1em;
font-weight:bold;
}
.CharOverride-32, .CharOverride-64, .CharOverride-65{
vertical-align:sub;
font-size:smaller;
}
.CharOverride-35, .CharOverride-40, .CharOverride-54{
vertical-align:super;
font-size:smaller;
}
.lining_box
{
border:5px solid #AFB0B3;

border-radius:15px;

padding:10px;
}
.lining_box:before 
{
    content:"";
    display:block;
    position:absolute;
    z-index:-1;
    top:2px;
    left:2px;
    right:2px;
    bottom:2px;
  
}
.lining_box3
{
border:2px solid #000;
padding:10px;
padding:10px;
}


.lining_box1
{
border-top:2px solid rgb(115,54,158);
border-bottom:2px solid rgb(115,54,158);
border-left:2px solid #fff;
border-right:2px solid #fff;
}
.lining_box2
{
border:2px solid #000;
border-radius:5px;
padding:15px;
}
p
{
	margin-top:10px;
}



.englishMeaning, .CharOverride-6, .CharOverride-22, .CharOverride-32, .CharOverride-34, .CharOverride-35, .CharOverride-37, .CharOverride-14, .CharOverride-17, .CharOverride-20, .CharOverride-27, .CharOverride-26, .CharOverride-41, .CharOverride-47, .ParaOverride-36, .CharOverride-52, .CharOverride-53, .CharOverride-56, .CharOverride-60, .CharOverride-66
{
	font-family:arial;
font-size:0.9em;
}
.CharOverride-28
{
	COLOR:#004E6B;
font-weight:bold;
}
table, table td
{
		border-collapse: collapse;
		border:3px solid #7FDBF8;
	padding:5px;
vertical-align: text-top;
	background:#fff;
margin:auto;
}
table th{
background:#D4F3FC;
border:3px solid #7FDBF8;
}
.table1{
background:rgb(130,209,112);
}
  p.resize img, .resize img
{

position:relative;
top:20px;
}
p.resize1 img, .resize1 img
{
position:relative;
top:40px;
}
p.resize2 img, .resize2 img, img.resize2
{

position:relative;
top:15px;
}
p.resize3 img, .resize3 img
{
height:75px;
position:relative;
top:25px;
} 
li
{
	margin-left:35px;
}

.color-hindi
{
	color:#41924B;
}
.Italic
{
	font-style:italic;
}
.activity-style, .Activity-Heading
{
	color:#000;
	font-weight:bold;
	font-size:1.1em;
text-transform:uppercase;
}
table td.Cell-Style-Head-english
{
	background:#fff;
	color:#fff;
	font-weight:bold;
}
.clear
{
	clear:both;
}
.golden
{
	color:#706627;
	font-weight:bold;
}
.cover_img_small

{
width:50%;
}
div.layout
{
text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:70%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#000;

padding:10px;

width:50%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#fff;

font-weight:normal;

}
@media only screen and (max-width: 767px) {

div.chapter_pos

{

font-size:0.8em;
line-height:120%;
top:50%;
}

div.chapter_pos div span

{

font-size:0.7em;

}
}
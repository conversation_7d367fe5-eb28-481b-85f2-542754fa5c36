<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<package xmlns="http://www.idpf.org/2007/opf" dir="ltr" id="package" prefix="cc: http://creativecommons.org/ns# rendition: http://www.idpf.org/vocab/rendition/#" unique-identifier="bookid" version="3.0" xml:lang="en">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <!-- unique identifier -->
    <dc:identifier id="bookid">Aaspaas- Chapter 13</dc:identifier>
    <!-- language of the book -->
    <!-- title -->
    <!-- creator -->
    <!-- 1 -->
    <!-- publisher -->
    <!-- date -->
    <!-- rights -->
    <!-- contributor -->
    <!-- fixed-layout options -->
    <!-- meta for the cover -->
    <dc:language>en</dc:language>
    <dc:title>Aaspaas- Chapter 13</dc:title>
    <dc:creator xmlns:opf="http://www.idpf.org/2007/opf" opf:role="aut">NCERT CIET</dc:creator>
    <dc:publisher>NCERT CIET</dc:publisher>
    <dc:date xmlns:opf="http://www.idpf.org/2007/opf" opf:event="">2015-05-21</dc:date>
    <dc:rights>All rights with NCERT</dc:rights>
    <dc:date xmlns:opf="http://www.idpf.org/2007/opf" opf:event="modification">2015-11-18</dc:date>
    <meta property="identifier-type" refines="#bookid" scheme="onix:codelist5">01</meta>
    <meta property="title-type" refines="#title">main</meta>
    <meta property="file-as" refines="#creator1">NCERT CIET</meta>
    <meta property="role" refines="#creator1" scheme="marc:relators">aut</meta>
    <meta property="display-seq" refines="#creator1">1</meta>
    <meta property="rendition:layout">pre-paginated</meta>
    <meta property="rendition:orientation">auto</meta>
    <meta property="rendition:spread">auto</meta>
    <meta name="cover" content="cover" />
    <meta content="0.8.2" name="Sigil version" />
  </metadata>
  <manifest>
    <item href="Styles/style.css" id="css" media-type="text/css" />
    <item href="toc.ncx" id="ncx" media-type="application/x-dtbncx+xml" />
    <item href="Text/cover.xhtml" id="id-cover-xhtml" media-type="application/xhtml+xml" />
    <item href="Text/Page1.xhtml" id="Page1.xhtml" media-type="application/xhtml+xml" />
    <item href="Text/Page2.xhtml" id="Page2.xhtml" media-type="application/xhtml+xml" />
    <item href="Text/Page3.xhtml" id="Page3.xhtml" media-type="application/xhtml+xml" />
    <item href="Text/Page4.xhtml" id="Page4.xhtml" media-type="application/xhtml+xml" />
    <item href="Text/Page5.xhtml" id="Page5.xhtml" media-type="application/xhtml+xml" />
    <item href="Text/Page6.xhtml" id="Page6.xhtml" media-type="application/xhtml+xml" />
    <item href="Text/Page7.xhtml" id="Page7.xhtml" media-type="application/xhtml+xml" />
    <item href="Text/Page8.xhtml" id="Page8.xhtml" media-type="application/xhtml+xml" />
    <item href="Text/Page9.xhtml" id="Page9.xhtml" media-type="application/xhtml+xml" />
    <item href="Text/Page10.xhtml" id="Page10.xhtml" media-type="application/xhtml+xml" />
    <item href="Images/Cover.jpg" id="Cover.jpg" media-type="image/jpeg" />
    <item href="Images/License.jpg" id="License.jpg" media-type="image/jpeg" />
    <item href="Images/Prelims.jpg" id="Prelims.jpg" media-type="image/jpeg" />
    <item href="Text/Page11.xhtml" id="Page11.xhtml" media-type="application/xhtml+xml" />
    <item href="Images/Image04.jpg" id="Image04.jpg" media-type="image/jpeg" />
    <item href="Images/Image03.jpg" id="Image03.jpg" media-type="image/jpeg" />
    <item href="Images/Image02.jpg" id="Image02.jpg" media-type="image/jpeg" />
    <item href="Images/Image01.jpg" id="Image01.jpg" media-type="image/jpeg" />
    <item href="Images/Image09.jpg" id="Image09.jpg" media-type="image/jpeg" />
    <item href="Images/Image08.jpg" id="Image08.jpg" media-type="image/jpeg" />
    <item href="Images/Image06.jpg" id="Image06.jpg" media-type="image/jpeg" />
    <item href="Images/Image05.jpg" id="Image05.jpg" media-type="image/jpeg" />
    <item href="Images/Image07.jpg" id="Image07.jpg" media-type="image/jpeg" />
    <item href="Images/Image11.jpg" id="Image11.jpg" media-type="image/jpeg" />
    <item href="Images/Image10.jpg" id="Image10.jpg" media-type="image/jpeg" />
  </manifest>
  <spine toc="ncx">
    <itemref idref="id-cover-xhtml" properties="page-spread-right" />
    <itemref idref="Page1.xhtml" properties="page-spread-left" />
    <itemref idref="Page2.xhtml" properties="page-spread-right" />
    <itemref idref="Page3.xhtml" properties="page-spread-left" />
    <itemref idref="Page4.xhtml" properties="page-spread-right" />
    <itemref idref="Page5.xhtml" properties="page-spread-left" />
    <itemref idref="Page6.xhtml" properties="page-spread-right" />
    <itemref idref="Page7.xhtml" properties="page-spread-left" />
    <itemref idref="Page8.xhtml" properties="page-spread-right" />
    <itemref idref="Page9.xhtml" properties="page-spread-left" />
    <itemref idref="Page10.xhtml" properties="page-spread-right" />
    <itemref idref="Page11.xhtml" />
  </spine>
  <guide />
</package>

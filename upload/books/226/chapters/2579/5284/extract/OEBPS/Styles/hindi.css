@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

body {
	font-family:"Walkman-Chanakya-905";
	font-size:120%;
     line-height:150%;
	padding:2%;
	text-align:justify;
}

* {
	margin:0;
	padding:0;
}
img
{
	max-width:100%;
}
.image {
	text-align:center;
}
.gray{
	font-size:1em;
	color:#ff0080;
	font-weight:bold;
}
.note
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
}
.chapterHeading {
	font-size:140%;
	text-align:right;
	margin:2%;
	color:#F0F;
    font-weight:bold;
}
.right{
	font-size:24px;
	font-weight:bold;
	text-align:right;
}

.chapterNo {
	font-size:150%;
	text-align:right;
    font-weight:bold;
	color:#FFF;
	background-color:#06F;
	margin:1%;
}

.textHeading {
	font-size:120%;
	text-align:center;
	font-weight:bold;
	margin:1% 0;
}
.textSubHeading {
	font-size:105%;
	text-align:left;
	color:#060;
	font-weight:bold;
	margin:1% 0;
}
.blue{
	color:#09F;
	font-size:24px;
	font-weight:bold;
}
.b1back{
	background-color:#09F;
}
.b2back{
	background-color:#0CF;
}
.front{
	background-color:#999;
}

.bblue{
	background-color:#0CF;
}
.exercise {
	font-size:115%;
	text-align:left;
	color:rgb(180, 62, 151);
	margin:1%;
}

.glossary {
	margin:1%;
	padding:7% 0 1% 0;
	background:url(./glossary.jpeg) no-repeat top left;
}

.box {
	margin:1%;
	padding:1%;
	background:rgba(151, 0, 82, 0.4);
}
 .superscript{
	 vertical-align:super;
     font-size: 0.7em;

}
 .super{
 vertical-align:super;
     font-size: 0.7em;

}
.subscript{
	  vertical-align:sub;
     font-size: 0.7em;

}
th
{
	text-align:center;
}
	.EnglishMeaning
	{
		font-family:Tahoma, Geneva, sans-serif;
		font-size:0.7em;
	}

div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 94%;

position:absolute;

top:50%;
line-height:110%;
font-weight:bold;

font-size:180%;

color:#fff;

}

div.chapter_pos div

{

background:#222;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.activitybox2{
background-color:#F4A460;
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}

h2
{
color:#fff;
font-size:1.5em;
background:#ff6600;
padding:15px;
}

/* Chapter number */
h4
{
color:#00aeef;
font-size:1.3em;
font-weight:bold;
}
/* Concept Heading */
.ConceptHeading
{
color:#C71585;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
.lining_box
{
border:2px solid #00CED1; border-radius:15px;
padding:15px;
}
/* Sub Heading */
.subheading
{
color:#ff0080;
font-size:1.1em;
font-weight:bold;
}
.subheadingh
{
color:#ff0080;
font-size:1.3em;
font-weight:bold;
margin-top:40px;
}
/* Sub Heading 2*/
.subheading2
{
color:#ff0080;
font-size:1em;
font-weight:bold;
}
/* Hightlisght Boxes */
.NewWordBox{
background-color:#F7E7BD;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.activityBox{
background-color:#DCDCDC;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.box{
background-color:#C8E9ED;
padding: 15px;
font-size:0.9em;
line-height:120%;
}

.footer

{

display:none;

}

table td

{

padding:10px;

}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {

div.chapter_pos

{
top:16%;
font-size:1em;
}
div.chapter_pos div

{
width:70%;
}
.cover_img_small
{
width:90%;
}
}

div.chapter_pos div span

{

font-size:0.5em;

color:#eaeaea;

font-weight:normal;

}
   
}
#prelims .para-style-override-17
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-2
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:#b55414;
}

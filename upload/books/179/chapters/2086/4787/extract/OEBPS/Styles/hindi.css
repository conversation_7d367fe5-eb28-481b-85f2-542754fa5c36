@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
	font-family:"Walkman-Chanakya-905";
}

body {
	font-size:120%;
	line-height:140%;
	padding:2%;
	text-align:justify;
}

.chapterHeading {
	text-align:center;
	font-size:150%;
	font-weight:bold;
	color:#F00;
	
}

.chapterNumber {
	font-size:18px;
	font-weight:bold;
	text-align:center;
	color:#00F;
}
.subHeading3 {
	font-size:110%;
	color:#ff0080;
	margin-bottom:1%;
	font-weight:bold;
}

.subHeading {
	font-size:140%;
	color:#930;
	margin-bottom:1%;
	font-weight:bold;
	text-align:center;
}
.meaning {
	font-size:95%;
}

.image {
	text-align:center;
}
.author {
	text-align:right;
}

.activity {
	background: #e1def0;
	padding:2%;
}

.englishMeaning{
	font-family:Arial, Helvetica, sans-serif;
	font-size:90%;
	font-weight:bold;
}
.bold
{
	font-size:115%;
	font-family: Walkman-Chanakya-905;
		font-weight:bold;
}
.italic
{
	font-weight:bold;
	font-size:100%;
	color:#03C;
}
.subHeading1 {
	font-size:120%;
	color:blue;
	margin-bottom:1%;
	font-weight:bold;
	text-align:center;
}

.subHeading2 {
	font-size:140%;
	color:#930;
	margin-bottom:1%;
	font-weight:bold;
}
.underline{
	text-decoration:underline;
	}
.green {
	font-size:140%;
	color:green;
	margin-bottom:1%;
	font-weight:bold;
	text-align:center;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:50%;
line-height:110%;
font-weight:bold;

font-size:180%;

color:#fff;

}

div.chapter_pos div

{

background:#266A2E;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.activitybox2{
background-color:#F4A460;
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.lining_box
{
border:2px solid #00aeef;
padding:15px;
border-radius:15px;
}
.img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
ul

{

margin-left:45px;

}

.caption
.lining_box
{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}
.blue
{
color:#00aeef;
}

p

{

margin-top:10px;

}

h2

{

color:#006699;

}

h4
{
color:#000;
font-size:1.3em;
}

{

color:#d1640f;

}

.footer

{

display:none;

}

table
{
    width:100%;
    border:1px solid #000;
    border-collapse:collapse;
}
td
{
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}

.no_border table, .no_border table td
{
	border:0px solid black;
}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.box{
background-color:#F9E3CB;
padding:15px;

}

#prelims .char-style-override-31
{
	font-weight:bold;
}
#prelims .heading, #prelims .char-style-override-29
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-16
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:rgb(236, 0, 140); 
}
#prelims img
{
	width:100%;
}
.cover_img_small

{

width:50%;

}

@media only screen and (max-width: 767px) {

div.chapter_pos

{

top:40%;

font-size:1em;

}

div.chapter_pos div

{

width:80%;

}

.cover_img_small

{

width:90%;

}

}
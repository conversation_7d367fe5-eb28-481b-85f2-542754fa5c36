<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg viewBox="0 0 935 1265" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
<style type="text/css"><![CDATA[
.g0_14{
fill: #231F20;
}
.g1_14{
fill: none;
stroke: #231F20;
stroke-width: 1.5583333;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g2_14{
fill: none;
stroke: #939598;
stroke-width: 6.141667;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g3_14{
fill: #2E3092;
stroke: #2E3092;
stroke-width: 1.5583333;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 1000;
}
.g4_14{
fill: #F58220;
stroke: #F58220;
stroke-width: 1.5583333;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 1000;
}
.g5_14{
fill: none;
stroke: #231F20;
stroke-width: 1.491111;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g6_14{
fill: #F58220;
}
.g7_14{
fill: #FFFFFF;
stroke: #FCF9F8;
stroke-width: 0.16041668;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g8_14{
fill: #FFFFFF;
stroke: #F8F8F7;
stroke-width: 0.16041668;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g9_14{
fill: #F58220;
stroke: #FFFFFF;
stroke-width: 0.7455556;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
]]></style>
</defs>
<image x="110" y="106" width="580" height="30" xlink:href="shade/1.png" />
<path d="M829.7,1178.4H880v-25.8H829.7v25.8Z" class="g0_14" />
<path d="M110,1177.4H880" class="g1_14" />
<image preserveAspectRatio="none" x="794" y="223" width="86" height="100" xlink:href="img/1.png" />
<path d="M702.9,309.1h99.5m35.5,0H880" class="g1_14" />
<path d="M701.8,105.9V1128.3" class="g2_14" />
<path fill-rule="evenodd" d="M110.3,575.8H566.4v27.7H110.3V575.8Z" class="g3_14" />
<path fill-rule="evenodd" d="M168.4,446.7H336.1v26.1H168.4V446.7Z" class="g4_14" />
<path d="M113.7,419.7h48.7c1.3,0,2.4,1.2,2.4,2.6v48.3c0,1.4,-1.1,2.6,-2.4,2.6H113.7c-1.3,0,-2.4,-1.2,-2.4,-2.6V422.3c0,-1.4,1.1,-2.6,2.4,-2.6Z" class="g5_14" />
<path fill-rule="evenodd" d="M132.1,430.3c1,-4.2,9.8,-4.9,12,0c.3,1,.3,3.4,0,4.9L132,435.1c-.3,-1.4,-.2,-3.2,.1,-4.8Z" class="g6_14" />
<path fill-rule="evenodd" d="M135,430.5h.1c0,1.4,0,3.3,.2,4.7l-.4,-.1c.2,-1.4,.2,-3.2,.1,-4.6Z" class="g7_14" />
<path fill-rule="evenodd" d="M140.9,430.4h.2c-.1,1.5,-.1,3.4,.1,4.8l-.3,-.1c.1,-1.4,.1,-3.3,0,-4.7Z" class="g8_14" />
<path fill-rule="evenodd" d="M138.2,423.4c1.1,0,2.3,.9,2.3,2.7c0,1.9,-.8,3.7,-2.2,3.8c-1.4,0,-2.4,-2,-2.4,-3.8c0,-1.9,1.2,-2.7,2.3,-2.7Z" class="g9_14" />
<path fill-rule="evenodd" d="M119.6,447.2c-3.7,-2.4,-1.3,-10.9,4,-11.3c1.1,.1,3.3,.9,4.6,1.7l-4.1,11.3c-1.5,-.2,-3.1,-.8,-4.5,-1.7Z" class="g6_14" />
<path fill-rule="evenodd" d="M120.8,444.5l-.1,-.1c1.4,.6,3.2,1.2,4.5,1.4l-.2,.4c-1.2,-.7,-2.9,-1.2,-4.2,-1.7Z" class="g7_14" />
<path fill-rule="evenodd" d="M122.7,438.9v-.1c1.4,.5,3.2,1.2,4.5,1.5l-.1,.2c-1.3,-.5,-3.1,-1.1,-4.4,-1.6Z" class="g8_14" />
<path fill-rule="evenodd" d="M115.1,439.1c.4,-1,1.7,-1.8,3.4,-1.2c1.7,.6,3.2,2,2.8,3.4c-.5,1.3,-2.7,1.6,-4.4,.9c-1.8,-.6,-2.1,-2.1,-1.8,-3.1Z" class="g9_14" />
<path fill-rule="evenodd" d="M131.4,463.9c-3.5,2.6,-10.6,-2.6,-9.2,-7.7c.4,-.9,2,-2.8,3.2,-3.8l9.2,7.9c-.6,1.2,-1.8,2.6,-3.2,3.6Z" class="g6_14" />
<path fill-rule="evenodd" d="M129.3,461.9v-.1c.9,-1.1,2.1,-2.5,2.8,-3.7l.3,.4c-1.1,.9,-2.2,2.2,-3.1,3.4Z" class="g7_14" />
<path fill-rule="evenodd" d="M124.7,458.2v-.1c.9,-1.1,2.2,-2.6,2.9,-3.8l.2,.2c-1,1.1,-2.2,2.5,-3.1,3.7Z" class="g8_14" />
<path fill-rule="evenodd" d="M122.3,465.3c-.8,-.7,-1.1,-2.2,0,-3.6c1.2,-1.4,3,-2.3,4.2,-1.5c1,.9,.5,3.1,-.6,4.5c-1.2,1.4,-2.7,1.3,-3.6,.6Z" class="g9_14" />
<path fill-rule="evenodd" d="M151.2,458.4c1.2,4.2,-6.1,9.2,-10.3,6.1c-.8,-.7,-2,-2.8,-2.5,-4.3l10.5,-6c1,1.1,1.8,2.7,2.3,4.2Z" class="g6_14" />
<path fill-rule="evenodd" d="M148.6,459.7l-.1,.1c-.7,-1.3,-1.7,-2.9,-2.5,-3.9l.4,-.2c.5,1.3,1.4,2.8,2.2,4Z" class="g7_14" />
<path fill-rule="evenodd" d="M143.5,462.8h-.1c-.7,-1.3,-1.7,-2.9,-2.5,-4l.3,-.1c.6,1.2,1.6,2.9,2.3,4.1Z" class="g8_14" />
<path fill-rule="evenodd" d="M149.4,467.5c-.9,.5,-2.4,.3,-3.3,-1.2c-1,-1.6,-1.2,-3.6,0,-4.4c1.2,-.7,3.1,.5,4,2.1c.9,1.6,.2,2.9,-.7,3.5Z" class="g9_14" />
<path fill-rule="evenodd" d="M152.8,438c4.4,.3,6.6,8.8,2.2,11.8c-1,.5,-3.4,.9,-4.9,.9l-2,-11.9c1.3,-.6,3.1,-.8,4.7,-.8Z" class="g6_14" />
<path fill-rule="evenodd" d="M153.1,440.9l.1,.1c-1.4,.2,-3.3,.6,-4.6,1v-.4c1.4,0,3.1,-.4,4.5,-.7Z" class="g7_14" />
<path fill-rule="evenodd" d="M154.3,446.7v.1c-1.4,.3,-3.4,.6,-4.6,1l-.1,-.3c1.4,-.1,3.3,-.5,4.7,-.8Z" class="g8_14" />
<path fill-rule="evenodd" d="M160.7,442.8c.2,1.1,-.5,2.4,-2.3,2.7c-1.8,.3,-3.7,-.1,-4.1,-1.5c-.2,-1.3,1.5,-2.7,3.4,-3c1.8,-.4,2.8,.7,3,1.8Z" class="g9_14" />
<path fill-rule="evenodd" d="M137.8,460c7.5,0,13.6,-5.8,13.6,-13.1c0,-7.2,-6.1,-13,-13.6,-13c-7.5,0,-13.6,5.8,-13.6,13c0,7.3,6.1,13.1,13.6,13.1Z" class="g9_14" />
</svg>
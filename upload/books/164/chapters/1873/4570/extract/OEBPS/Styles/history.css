body {
font-family:arial;
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}
img{
max-width:100%;
}
.image {
text-align:center;
}

.chapterHeading {
	text-align:center;
	font-size:1.3em;
	color:#1874CD;
	font-family:arial;
	text-transform:uppercase;
	font-weight:bold;
}

.chapterNumber {
	font-size: 125%;
	font-family:arial;
	text-transform:uppercase;
	font-weight:bold;
}

/* Chapter sub heading */
.subHeading {
	font-size:1.3em;
	color:#1874CD;       
	margin-bottom:1%;
	font-weight:bold;
	text-transform: uppercase;
}

.author {
	text-align:right
}

.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}

.box {
	background-color: #C5EFFD;
	padding: 15px;
	border : 1px solid #DCF0F7;
     }

.bheading 
{
font-size:120%;
font-family:Arial, Helvetica, sans-serif;
font-weight:bold;
text-align:center;
}

/* Activity Box */
.activityBox {
	border:5px solid #3399FF;
	padding: 15px;
	background-color: #DCF0F7;
	}

.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 95%;

position:absolute;

top:50%;
line-height:110%;
font-weight:bold;

font-size:180%;

color:#fff;

}

div.chapter_pos div

{

background:#666;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.activityBox2 {
	background-color: #C5EFFD;
	padding: 15px;
	margin: 15px;
	border: 1ps solid #C5EFFD;
}

/* Lining Box */
.lining_box
{
	border:1px solid #1874CD;
	padding: 15px;
}

.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}

/* Chapter name */
h2
	{
        color:#fff;
        font-size:1.5em;
        background:#0099cc; 
        padding:10px;
		font-weight:bold;
	}

/* Chapter number */
h4
{
color:#000;   
font-size:1.3em;
}

.footer

{

display:none;

}

table td

{

padding:10px;

}

.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:10%;
font-size:1em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}

#prelims
{
	line-height:150%;
}
#prelims .char-style-override-16
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color:#00aeef;
}
#prelims .char-style-override-2
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:#00aeef;
}
.box1 {
	background-color: #CCC;
	padding: 15px;
	border : 1px solid #CCC;
     }
.subheading {
	color:#1874CD;
    font-size:1.15em;
    padding:5px;
	font-weight: bold;
	text-transform: capitalize;
	}
/* Paragraph Heading */
.pheading {
	color:#1874CD;
        font-size:1.0em;
        padding:5px;
	font-weight: bold;
	text-transform: capitalize;
	}	 
/* For Page Image */
.pimg {
	width: 75%;
	}
/* For exercise Box */
.ebox
{
	border:2px solid #1874CD;
	padding:15px;
	border-radius: 15px;
}